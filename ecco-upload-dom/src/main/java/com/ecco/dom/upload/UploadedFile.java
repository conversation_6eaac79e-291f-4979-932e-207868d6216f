package com.ecco.dom.upload;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToOne;
import javax.persistence.Transient;

import com.google.common.collect.ImmutableMap;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

import java.util.Map;

// made a superclass so we don't have too many nested collections (eg Referral has ReferralAttachment(s) has UploadedFile has UploadedBytes)
// so projects inherit this with other info (eg ReferralAttachment with UploadedFile)
// but the UploadedBytes object stays as-is

// followed http://stackoverflow.com/questions/2112615/persisting-large-files-with-hibernate-annotations
//@javax.persistence.Entity
//@javax.persistence.Table(name="uploads")
@MappedSuperclass
public abstract class UploadedFile extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    @Column
    String filename;
    @Column(name="UPLOAD_SIZE")
    long size;

    // did have mappedBy='file' which meant the bytes side was responsible for the relationship (had fileId)
    // but we saved the file object (not the bytes) and so the insert order was wrong
    // so when we save this the bytes insert first to get its id and its saved to the file
    // also, this cleared up the mappedsuperclass problem of bytes trying to refer to an abstract file class

    // we also want the bytes to be created and deleted but not updated - so we can save the parent again without loading the bytes
    // so for instance, we update ReferralAttachment to specify where the attachment is visible, and don't want to null the bytes
    // so we set everything but merge - (don't merge our null state with the db)
    // but if not using jpa entitymanager, then need hibnerate's own cascade
    // can't use hiberante's own cascade SAVE_UPDATE since its just that - we can't separate a save from an update
    // so we can either control the cascade ourselves and set to none here
    // or think about using jpa properly (requires entity manager?)
    // for onetoone to be possibly lazy loaded the optional=false needs to be set (else it has to sql to see if it exists)
    // "By default, single point associations are eagerly fetched in JPA 2"
    @OneToOne(cascade={CascadeType.PERSIST, CascadeType.REMOVE, CascadeType.DETACH, CascadeType.REFRESH}, orphanRemoval=true, fetch=FetchType.LAZY, optional=false)
    //@OneToOne(cascade={}, orphanRemoval=true, fetch=FetchType.LAZY, optional=false)
    //@Cascade(value={org.hibernate.annotations.CascadeType.PERSIST, org.hibernate.annotations.CascadeType.DELETE})
    @JoinColumn(name="bytesId", insertable=true, updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    // "JOIN overrides any lazy attribute (an association loaded through a JOIN strategy cannot be lazy)"
    //@Fetch(FetchMode.JOIN)
    UploadedBytes uploadedBytes;

    public UploadedBytes getUploadedBytes() {
        return uploadedBytes;
    }
    public void setUploadedBytes(UploadedBytes bytes) {
        //bytes.setFile(this);
        this.uploadedBytes = bytes;
    }

    public String getFilename() {
        return filename;
    }
    public long getSize() {
        return size;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }
    public void setSize(long size) {
        this.size = size;
    }

    public static enum Source {
        SERVICE_RECIPIENT("service-recipient"),
        NONE("global"); // see AvatarControl.ts

        private final String value;
        private static final Map<String, Source> lookup;

        static {
            final ImmutableMap.Builder<String, Source> builder = ImmutableMap.builder();
            for (Source source : Source.values()) {
                builder.put(source.toString(), source);
            }
            lookup = builder.build();
        }

        Source(String value) {
            this.value = value;
        }

        public static Source fromString(String value) {
            return lookup.get(value);
        }

        @Override
        public String toString() {
            return value;
        }
    }

    /** A string identifying which type of attachment this is. Used by UploadConfigGenerator and DownloadHiddenController. */
    @Transient
    public abstract Source getSource();

}
