package com.ecco.upload.config;

import com.ecco.upload.dao.UploadedFileRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import java.io.IOException;

/**
 * @since 12/08/2014
 */
@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(basePackageClasses=UploadedFileRepository.class)
public class UploadConfig {

    @Value("${misc.upload.maxSize}")
    private long maxUploadSize;

    @Value("${misc.upload.tempDir}")
    private Resource uploadTempDir;

    @Bean
    public CommonsMultipartResolver multipartResolver() throws IOException {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setResolveLazily(true);
        multipartResolver.setMaxUploadSize(maxUploadSize);
        multipartResolver.setUploadTempDir(uploadTempDir);
        // holds files in memory until written to disk: "Default is 10240, according to Commons FileUpload"
        multipartResolver.setMaxInMemorySize(10240);
        return multipartResolver;
    }
}
