package com.ecco.webApi.workers;

import com.ecco.dom.hr.Worker;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.contacts.WorkerJobViewModel;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import java.time.ZoneId;
import java.util.function.Function;

public class WorkerJobFromViewModel implements Function<WorkerJobViewModel, WorkerJob> {

    ServiceCategorisationRepository serviceCategorisationRepository;
    EntityManager em;

    public WorkerJobFromViewModel(EntityManager em, ServiceCategorisationRepository serviceCategorisationRepository) {
        this.em = em;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    @Nullable
    public WorkerJob apply(@Nullable WorkerJobViewModel input) {
        if (input == null) {
            throw new NullPointerException("input WorkerJobViewModel must not be null");
        }

        WorkerJob j = new WorkerJob();
        var w = em.getReference(Worker.class, input.getWorkerId());
        j.setWorker(w);
        j.setCode(input.getCode());
        j.setContractedWeeklyHours(input.getContractedWeeklyHours());
        if (input.getStartDate() != null) {
            j.setStartDate(input.getStartDate().atStartOfDay(ZoneId.of("UTC")));
        }
        if (input.getEndDate() != null) {
            j.setEndDate(input.getEndDate().atStartOfDay(ZoneId.of("UTC")));
        }

        // Worker.DEFAULT_SERVICE_ALLOCATION_ID
        var svcAllocId = input.getServiceRecipient().serviceAllocationId;
        var svcAlloc = em.getReference(ServiceCategorisation.class, svcAllocId);
        j.setServiceAllocation(svcAlloc);

        return j;
    }

}
