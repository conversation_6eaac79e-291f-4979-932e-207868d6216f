package com.ecco.webApi.controllers;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when the client makes a Web API request for an entity that
 * does not exist, and that it is an error to have requested this.
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public final class NotFoundException extends RuntimeException {
    private Object id;

    public NotFoundException(Object id) {
        super("Entity not found - client should not be requesting this: id = " + id);
        this.id = id;
    }

    public Object getId() {
        return id;
    }
}
