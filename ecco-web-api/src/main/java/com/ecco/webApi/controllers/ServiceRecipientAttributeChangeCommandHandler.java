package com.ecco.webApi.controllers;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.dom.servicerecipients.commands.ServiceRecipientAttributeChangeCommand;
import com.ecco.webApi.viewModels.ChoiceUpdateCommand;
import com.ecco.webApi.viewModels.DateUpdateCommand;
import com.ecco.webApi.viewModels.ServiceRecipientAttributeChangeCommandViewModel;
import com.ecco.webApi.viewModels.TextUpdateCommand;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.*;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;

public class ServiceRecipientAttributeChangeCommandHandler extends ServiceRecipientCommandHandler<ServiceRecipientAttributeChangeCommandViewModel,
        ServiceRecipientAttributeChangeCommand, @NonNull Integer> {

    @PersistenceContext
    EntityManager em;

    final ServiceRecipientRepository serviceRecipientRepository;

    public ServiceRecipientAttributeChangeCommandHandler(ObjectMapper objectMapper,
                                                         ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                         ServiceRecipientRepository serviceRecipientRepository) {
        super(objectMapper, serviceRecipientCommandRepository, ServiceRecipientAttributeChangeCommandViewModel.class);
        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull Integer serviceRecipientId, @NonNull ServiceRecipientAttributeChangeCommandViewModel viewModel) {

        switch (StringUtils.lowerCase(viewModel.attributeType)) {

            case "text":
                this.updateText(viewModel);
                break;

            case "date":
                this.updateDate(viewModel);
                break;

            case "choice":
                this.updateChoice(viewModel);
                break;

            default:
                throw new IllegalArgumentException("attributeType unknown: " + viewModel.attributeType);
        }

        return null;
    }

    /**
     * Updates mimic those from ClientUpdateCommandController.
     * Also see ClientAttributeChangeCommandHandler.
     */
    private void updateText(ServiceRecipientAttributeChangeCommandViewModel cmdVM) {
        BaseServiceRecipientEvidence sr = serviceRecipientRepository.findEvidenceCapableById(cmdVM.serviceRecipientId).orElseThrow();
        BeanWrapper wrapper = PropertyAccessorFactory.forBeanPropertyAccess(sr);
        String path = cmdVM.attributePath;
        Object currentValueObj = wrapper.getPropertyValue(path);
        String currentValue = currentValueObj != null ? currentValueObj.toString() : null;
        String fromValue = cmdVM.valueChange.from;
        String toValue = cmdVM.valueChange.to;
        AttributeChangeUtility.checkAndHandleNonMatchingTextValue(currentValue, new TextUpdateCommand(fromValue, toValue));
        wrapper.setPropertyValue(path, toValue);
        serviceRecipientRepository.save(sr);

        // CASCASE doesn't pass through for properties that don't allow it, obviously, but this means changes on referral/workers etc are ignored.
        // SO we get the target and merge it
        // We assume that we don't need any further depth because we never did with the previous version at ClientUpdateCommandController.
        // CLEAR is important else it seems the entity is tied to the sr, where cascade ingores the changes
        em.clear();
        em.merge(sr.getTargetEntity());

        // NB previous attempts used this useful snippet to get parent paths for underlying entities
        // int i = PropertyAccessorUtils.getFirstNestedPropertySeparatorIndex(path);
        // as well as attempting:
        //  em.refresh, Hibernate.initialize(), em.find(childClass, childId), em.flush and casting BeanWrapperImpl, AntiProxyUtils.ensureManaged
    }

    private void updateDate(ServiceRecipientAttributeChangeCommandViewModel cmdVM) {
        if (cmdVM.attributePath.equalsIgnoreCase("worker.startDate") ||
            cmdVM.attributePath.equalsIgnoreCase("worker.endDate")) {
            updateDateAsTime(cmdVM);
        } else {
            updateDateAsDate(cmdVM);
        }
    }

    private void updateDateAsDate(ServiceRecipientAttributeChangeCommandViewModel cmdVM) {
        BaseServiceRecipientEvidence sr = serviceRecipientRepository.findEvidenceCapableById(cmdVM.serviceRecipientId).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(sr);

        String path = cmdVM.attributePath;
        LocalDate currentValue = (LocalDate) wrapper.getPropertyValue(path);
        LocalDate fromValue = cmdVM.valueChange.from != null ? new LocalDate(cmdVM.valueChange.from) : null;
        LocalDate toValue = cmdVM.valueChange.to != null ? new LocalDate(cmdVM.valueChange.to) : null;
        AttributeChangeUtility.checkAndHandleNonMatchingDateValue(currentValue, new DateUpdateCommand(fromValue, toValue));

        wrapper.setPropertyValue(path, toValue);
        serviceRecipientRepository.save(sr);

        em.clear();
        em.merge(sr.getTargetEntity());
    }

    /**
     * When we want to use a date, but the database is datetime - treat everything as date until the last moment.
     */
    private void updateDateAsTime(ServiceRecipientAttributeChangeCommandViewModel cmdVM) {
        BaseServiceRecipientEvidence sr = serviceRecipientRepository.findEvidenceCapableById(cmdVM.serviceRecipientId).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(sr);

        String path = cmdVM.attributePath;
        DateTime currentValue = (DateTime) wrapper.getPropertyValue(path);
        LocalDate fromValue = cmdVM.valueChange.from != null ? new LocalDate(cmdVM.valueChange.from) : null;
        LocalDate toValue = cmdVM.valueChange.to != null ? new LocalDate(cmdVM.valueChange.to) : null;
        AttributeChangeUtility.checkAndHandleNonMatchingDateValue(currentValue != null ? currentValue.toLocalDate() : null, new DateUpdateCommand(fromValue, toValue));

        wrapper.setPropertyValue(path, toValue != null ? toValue.toDateTimeAtStartOfDay() : null);
        serviceRecipientRepository.save(sr);

        em.clear();
        em.merge(sr.getTargetEntity());
    }

    private void updateChoice(ServiceRecipientAttributeChangeCommandViewModel cmdVM) {
        BaseServiceRecipientEvidence sr = serviceRecipientRepository.findEvidenceCapableById(cmdVM.serviceRecipientId).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(sr);

        String path = cmdVM.attributePath.split(":")[0];
        Integer currentValue = (Integer) wrapper.getPropertyValue(path);
        Integer fromValue = cmdVM.valueChange.from != null ? Integer.valueOf(cmdVM.valueChange.from) : null;
        Integer toValue = cmdVM.valueChange.to != null ? Integer.valueOf(cmdVM.valueChange.to) : null;
        AttributeChangeUtility.checkAndHandleNonMatchingIntegerValue(currentValue, new ChoiceUpdateCommand(fromValue, toValue));

        wrapper.setPropertyValue(path, toValue);
        serviceRecipientRepository.save(sr);

        em.clear();
        em.merge(sr.getTargetEntity());
    }

    @NonNull
    @Override
    protected ServiceRecipientAttributeChangeCommand createCommand(Serializable targetId, @NonNull Integer serviceRecipientId, @NonNull String requestBody,
                                                                   @NonNull ServiceRecipientAttributeChangeCommandViewModel viewModel, long userId) {
        Assert.state(serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new ServiceRecipientAttributeChangeCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                serviceRecipientId);
    }

}
