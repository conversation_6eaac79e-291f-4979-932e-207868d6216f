package com.ecco.webApi.controllers;

import com.ecco.security.dom.QueuedCommand;
import com.ecco.webApi.GuavaResourceAssemblerSupport;
import com.ecco.webApi.viewModels.QueuedCommandViewModel;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.jspecify.annotations.NonNull;

import java.util.Locale;

public class QueuedCommandToViewModel extends GuavaResourceAssemblerSupport<QueuedCommand, QueuedCommandViewModel> {
    private final CommandRequestToViewModel commandRequestToViewModel = new CommandRequestToViewModel();

    public QueuedCommandToViewModel(Class<?> controller) {
        super(controller, QueuedCommandViewModel.class);
    }

    @NonNull
    @Override
    public QueuedCommandViewModel toModel(@NonNull QueuedCommand entity) {

        final QueuedCommandViewModel resource = createModelWithId(entity.getId(), entity);

        resource.valid = entity.isValid();
        resource.executed = entity.isExecuted();
        resource.archived = entity.isArchived();
        resource.dismissed = entity.isDismissed();

        resource.userDeviceId = entity.getUserDevice() == null ? null : entity.getUserDevice().getId();
        // TODO needs to be dd/MM/yy with time etc format
        resource.queueTime = DateTimeUtils.getFormattedDateTime(entity.getQueueTime(), Locale.ROOT);
        resource.commandRequest = commandRequestToViewModel.apply(entity.getCommandRequest());

        resource.result = entity.getResult() == null ? null : entity.getResult().toString();
        resource.executeTime = DateTimeUtils.getFormattedDateTime(entity.getExecuteTime(), Locale.ROOT);

        return resource;

    }
}
