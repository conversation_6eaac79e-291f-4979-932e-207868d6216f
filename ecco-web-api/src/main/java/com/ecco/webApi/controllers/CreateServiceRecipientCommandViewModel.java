package com.ecco.webApi.controllers;

import com.ecco.webApi.evidence.BaseCommandViewModel;

import org.jspecify.annotations.NonNull;

/**
 * Base command for creating service recipients
 */
public class CreateServiceRecipientCommandViewModel extends BaseCommandViewModel {

    @NonNull
    private String prefix;

    // we don't have a remove or update option, so for now, its always changed
    public boolean hasChanges() {
        return true;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected CreateServiceRecipientCommandViewModel() {
        super();
    }

    public CreateServiceRecipientCommandViewModel(String uri, @NonNull String prefix) {
        super(uri);
        this.prefix = prefix;
    }

    public String getPrefix() {
        return prefix;
    }
}
