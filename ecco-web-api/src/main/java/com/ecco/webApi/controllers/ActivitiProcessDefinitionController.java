package com.ecco.webApi.controllers;

import java.io.IOException;
import java.io.InputStream;

import javax.servlet.http.HttpServletResponse;

import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.webApi.support.Streams;
import com.ecco.webApi.viewModels.Result;

/**
 * For ecco use only
 */
@PreAuthorize("hasRole('ROLE_SYSADMIN')")
@RestController
public class ActivitiProcessDefinitionController extends BaseWebApiController {

    private final RepositoryService repositoryService;


    @Autowired
    public ActivitiProcessDefinitionController(RepositoryService repositoryService) {
        this.repositoryService = repositoryService;
    }


    @RequestMapping(value = "/activiti/definition/{processName}/", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_XML_VALUE)
    public void findProcessDefinitionByName(@PathVariable String processName,
            HttpServletResponse response) throws IOException {

        ProcessDefinition def = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(processName)
                .latestVersion()
                .singleResult();
        InputStream stream = repositoryService.getResourceAsStream(def.getDeploymentId(), def.getResourceName());
        Streams.copyWithCloseOfInput(stream, response.getOutputStream());
    }


    @RequestMapping(value = "/activiti/definition/{processName}/", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Result updateProcessDefinitionByName(@PathVariable String processName, InputStream inputStream) {

        Deployment deployment = repositoryService.createDeployment()
                .addInputStream(processName + ".bpmn20.xml", inputStream)
                .enableDuplicateFiltering()
                .deploy();

        return new Result(deployment.getId());
    }

}
