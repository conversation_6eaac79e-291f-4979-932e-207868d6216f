package com.ecco.webApi.controllers;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CreateServiceRecipientCommand;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.contacts.WorkerJobViewModel;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.workers.WorkerJobFromViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class CreateWorkerJobCommandHandler extends ServiceRecipientCommandHandler<CreateWorkerJobCommandViewModel,
        CreateServiceRecipientCommand, @NonNull CreateServiceRecipientParams> {

    @PersistenceContext
    EntityManager entityManager;
    @NonNull
    private final WorkerJobRepository workerJobRepository;
    @NonNull
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private WorkerJobFromViewModel workerJobFromViewModel;

    public CreateWorkerJobCommandHandler(ObjectMapper objectMapper,
                                         @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                         ServiceCategorisationRepository serviceCategorisationRepository,
                                         WorkerJobRepository workerJobRepository) {
        super(objectMapper, serviceRecipientCommandRepository, CreateWorkerJobCommandViewModel.class);
        this.workerJobRepository = workerJobRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @PostConstruct
    private void init() {
        workerJobFromViewModel = new WorkerJobFromViewModel(this.entityManager, this.serviceCategorisationRepository);
    }

    @NonNull
    @Override
    protected CreateServiceRecipientCommand createCommand(Serializable targetId, @NonNull CreateServiceRecipientParams params, @NonNull String requestBody,
                                                          @NonNull CreateWorkerJobCommandViewModel viewModel, long userId) {
        Assert.state(params.prefix.equals(viewModel.getPrefix()), "prefix in body must match URI");

        return new CreateServiceRecipientCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                (Integer) targetId);
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull CreateServiceRecipientParams params, @NonNull CreateWorkerJobCommandViewModel viewModel) {
        int id = CreateWorkerJobCommandHandler.createWorkerJob(viewModel.getWorkerJobViewModel(), this.workerJobFromViewModel,
                this.workerJobRepository);
        var workerJob = this.workerJobRepository.getById(id);

        // return the srId for the createCommand
        return CommandResult.ofLink(linkToApi(methodOn(WorkerJobController.class).findOneWorkerJob(id)).withSelfRel())
                .withTargetId(workerJob.getServiceRecipient().getId());
    }

    public static int createWorkerJob(WorkerJobViewModel viewModel, WorkerJobFromViewModel fromViewModel,
                                       WorkerJobRepository workerJobRepository) {
        final WorkerJob workerJob = fromViewModel.apply(viewModel);
        workerJobRepository.save(workerJob);
        return workerJob.getId();
    }

}
