package com.ecco.webApi.controllers;

import com.ecco.evidence.dom.TaskStatus;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.webApi.viewModels.TaskStatusToViewModel;
import com.ecco.webApi.viewModels.TaskStatusViewModel;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

/**
 * Controller for retrieval and manipulation of tasks.
 */
@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class TaskStatusController extends BaseWebApiController {

    private final Function<TaskStatus, TaskStatusViewModel> taskStatusToViewModel;

    private final TaskStatusRepository taskStatusRepository;

    public TaskStatusController(TaskStatusRepository taskStatusRepository,
                                ServiceRecipientRepository serviceRecipientRepository) {
        this.taskStatusRepository = taskStatusRepository;
        this.taskStatusToViewModel = new TaskStatusToViewModel(serviceRecipientRepository);
    }

    /**
     * Only lists tasks that are using linear tasks (which are not ad-hoc tasks)
     * NB unused (except for testing - TaskStatusActor) - perhaps consider creating a more appropriate call, eg 'latest'
     */
    @GetJson("/taskStatus/byServiceRecipient/{serviceRecipientId}/")
    public List<TaskStatusViewModel> tasksByServiceRecipient(@PathVariable int serviceRecipientId) {
        //cacheForXSecs(90, response);
        return taskStatusRepository.findAllByServiceRecipientId(serviceRecipientId)
                .stream()
                .map(taskStatusToViewModel)
                .collect(toList());
    }

}
