package com.ecco.webApi.contacts;

import org.jspecify.annotations.Nullable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties("discriminator") // used at offline client end for discriminating between diff entities
@Getter
@Setter
public class ClientViewModel extends ClientDetailAbstractViewModel {
    /**
     * The client ID.
     */
    public Long clientId;

    /**
     * The client's housing benefit number.
     */
    public String housingBenefit;

    /**
     * The building id of where the client is residing.
     */
    @Nullable
    public Integer residenceId;
    /** The name of the residence where the residing client resides */
    @Nullable
    public String residenceName;

    // Property accessors are unfortunately required for com.ecco.data.client.dataimport.csv.CSVBeanReader.
    // See ECCO-703

    public String getExternalSystemRef() {
        return externalSystemRef == null ? null : externalSystemRef.trim();
    }
    public String getCode() {
        return code == null ? null : code.trim();
    }


}
