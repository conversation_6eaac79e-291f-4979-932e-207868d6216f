package com.ecco.webApi.contacts;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.hr.Worker;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.DelegateResponse;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.IdNameService;
import com.ecco.service.hr.HrService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.Maps;

import java.util.stream.Stream;

import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@RestController
@PreAuthorize("hasRole('ROLE_STAFF')")
public class WorkerController extends BaseWebApiController {

    private final HrService hrService;

    private final WorkerRepository workerRepository;
    private final WorkerJobRepository workerJobRepository;
    private final UserRepository userRepository;

    private final WorkerFromViewModel workerFromViewModel;
    private final WorkerToViewModel workerToViewModel;

    private StaffDefinitionToViewModel staffDefinitionToViewModel;

    private final Function<DelegateResponse<Iterable<ClientDefinition>>, DelegateResponse<Iterable<WorkerViewModel>>> delegateResponseToViewModel
            = (@NonNull DelegateResponse<Iterable<ClientDefinition>> input) -> {
        if (input.getPayload() == null) {
            return DelegateResponse.emptyFrom(input);
        }
        else {
            Iterable<WorkerViewModel> result = StreamSupport.stream(input.getPayload().spliterator(), false)
                    .map(staffDefinitionToViewModel)
                    .collect(Collectors.toList());
            return DelegateResponse.ok(result);
        }
    };

    private final ClientDefinitionFromViewModel clientDefinitionFromViewModel = new ClientDefinitionFromViewModel();

    @Autowired
    public WorkerController(HrService hrService,
                            WorkerRepository workerRepository,
                            WorkerJobRepository workerJobRepository,
                            UserRepository userRepository,
                            IdNameService idNameService,
                            @NonNull AddressRepository addressRepository,
                            @NonNull ListDefinitionRepository listDefinitionRepository,
                            @NonNull FixedContainerRepository fixedContainerRepository) {
        this.hrService = hrService;
        this.workerRepository = workerRepository;
        this.workerJobRepository = workerJobRepository;
        this.userRepository = userRepository;
        this.workerToViewModel = new WorkerToViewModel(listDefinitionRepository, true);
        this.workerFromViewModel = new WorkerFromViewModel(idNameService, addressRepository, listDefinitionRepository, fixedContainerRepository);
        this.staffDefinitionToViewModel = new StaffDefinitionToViewModel(listDefinitionRepository);
    }

    @GetJson("/workers/{id}/")
    public WorkerViewModel findOne(@PathVariable long id) {
        return workerToViewModel.apply(workerRepository.findById(id).orElse(null));
    }

    @GetJson("/workers/byServiceRecipientIds/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    public Iterable<WorkerViewModel> findAllByIds(@RequestParam(name = "ids") List<Integer> srIds) {
        return workerJobRepository.findAllByServiceRecipient_IdIn(srIds).stream()
                .map(WorkerJob::getWorker)
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Worker::getId))),
                        ArrayList::new))
                .stream().map(workerToViewModel).collect(Collectors.toList());
    }

    @GetJson("/workers/byServiceRecipient/{serviceRecipientId}/")
    public WorkerViewModel findOneByServiceRecipient(@PathVariable int serviceRecipientId) {
        var vms = findAllByIds(List.of(serviceRecipientId)).iterator();
        return vms.hasNext() ? vms.next() : null;
    }

    @GetJson("/buildings/{buildingId}/workers/")
    public Stream<WorkerViewModel> findAllWorkersByPrimaryLocationId(
            @PathVariable Integer buildingId,
            @RequestParam(name = "isUser", defaultValue = "false") boolean isUser) {
        return StreamSupport.stream((isUser
                    ? workerRepository.findAllStaffUsersWithPrimaryLocationBuildingId(buildingId)
                    : workerRepository.findAllStaffWithPrimaryLocationBuildingId(buildingId)
        ).spliterator(), false)
                .map(workerToViewModel);
    }

    @GetJson("/workers/") // TODO HATEOAS / JSON Schema with search options
    public Stream<WorkerViewModel> findAll() {
        return workerRepository.findAll().stream().map(workerToViewModel);
    }

    @PostJsonReturningJson("/workers/")
    @ResponseStatus(HttpStatus.CREATED)
    public Result create(@RequestBody WorkerViewModel workerViewModel) {
        Assert.isNull(workerViewModel.workerId, "No serviceRecipientId should be set on POST");

        Worker worker = workerFromViewModel.apply(workerViewModel);

        hrService.createOrUpdateWorker(worker);

        return new Result(worker.getId());
    }

    @PostJsonReturningJson("/workers/{workerId}/linkUser/{username}")
    @ResponseStatus(HttpStatus.CREATED)
    public Result linkUser(@PathVariable long workerId, @PathVariable String username) {

        Worker w = workerRepository.findById(workerId).orElseThrow();
        User u = userRepository.getByUsername(username);
        w.setLinkToUser(u);
        hrService.linkToUser(w);

        return new Result(w.getId());
    }

    @GetJson("/workersWithUser/")
    public Stream<WorkerViewModel> findAllByUserIsNotNull() {
        return workerRepository.findAllByContact_UserIsNotNull().map(workerToViewModel);
    }

    /**
     * Queries across internal sources of client data only.
     * @return map of externalsystem name to a list of matching clients. Local clients are identified by key 'ecco'.
     */
    @PostJsonReturningJson("/staff/local/query")
    public Map<String, DelegateResponse<Iterable<WorkerViewModel>>> queryLocalStaffByExample(@RequestBody ClientViewModel exemplar) {
        // local query based on ClientDefinition - from ClientViewModel
        final Map<String,DelegateResponse<Iterable<ClientDefinition>>> map
            = hrService.queryStaffByExample(clientDefinitionFromViewModel.apply(exemplar), false);

        // put back to ClientViewModel
        return Maps.transformValues(map, delegateResponseToViewModel::apply);
    }

    /**
     * Queries across internal and external sources of client data.
     * @return map of externalsystem name to a list of matching clients. Local clients are identified by key 'ecco'.
     */
    @PostJsonReturningJson("/staff/all/query")
    public Map<String, DelegateResponse<Iterable<WorkerViewModel>>> queryAllClientsByExample(@RequestBody ClientViewModel exemplar) {
        // query based on the ClientDefinition - from ClientViewModel
        final Map<String,DelegateResponse<Iterable<ClientDefinition>>> map
                = hrService.queryStaffByExample(clientDefinitionFromViewModel.apply(exemplar), true);

        // put back to ClientViewModel
        return Maps.transformValues(map, delegateResponseToViewModel::apply);
    }


}
