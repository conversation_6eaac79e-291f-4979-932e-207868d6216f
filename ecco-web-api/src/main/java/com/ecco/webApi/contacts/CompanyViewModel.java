package com.ecco.webApi.contacts;

import com.ecco.webApi.contacts.address.AddressViewModel;

public class CompanyViewModel {

    /**
     * unique key, unique amongst all contacts (company, agency, individual)
     */
    public Long companyId;

    public String companyName;

    public AddressViewModel address;

    public String phoneNumber;

    /** This may just be useful, so I've added it */
    public String calendarId;


    // Property accessors are unfortunately required for com.ecco.data.client.csv.CSVBeanReader.
    // See ECCO-703

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public AddressViewModel getAddress() {
        return address;
    }

    public void setAddress(AddressViewModel address) {
        this.address = address;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCalendarId() {
        return calendarId;
    }

    public void setCalendarId(String calendarId) {
        this.calendarId = calendarId;
    }

}
