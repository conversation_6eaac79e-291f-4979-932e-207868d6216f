package com.ecco.webApi.contacts;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Agency;
import com.ecco.dom.ContactImpl;
import com.ecco.dom.Individual;
import com.ecco.dom.ServiceRecipientContact;
import com.ecco.dom.commands.ServiceRecipientAssociatedContactCommand;
import com.ecco.infrastructure.dom.BaseCommand;
import com.ecco.security.repositories.ContactRepository;
import java.util.function.Function;
import org.joda.time.DateTime;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.util.List;
import java.util.Optional;

public final class ServiceRecipientAssociatedContactToViewModel implements Function<ServiceRecipientContact, ServiceRecipientAssociatedContactViewModel> {

    private final AgencyToViewModel agencyToViewModel = new AgencyToViewModel();
    private final IndividualToViewModel individualToViewModel = new IndividualToViewModel();

    @NonNull
    private final ContactRepository contactRepository;
    @NonNull
    private final ServiceRecipientCommandRepository serviceRecipientCommandRepository;
    public ServiceRecipientAssociatedContactToViewModel(ContactRepository contactRepository,
                                                        ServiceRecipientCommandRepository serviceRecipientCommandRepository) {
        this.contactRepository = contactRepository;
        this.serviceRecipientCommandRepository = serviceRecipientCommandRepository;
    }

    @Override
    public ServiceRecipientAssociatedContactViewModel apply(@Nullable ServiceRecipientContact input) {
        if (input == null) {
            throw new NullPointerException("input must not be null");
        }

        Optional<ContactImpl> c = this.contactRepository.findById(input.getId().getContactId());
        var contact = c.orElseThrow(NullPointerException::new);

        List<ServiceRecipientAssociatedContactCommand> commandHistory = this.serviceRecipientCommandRepository.findAssociatedContactByServiceRecipientIdOrderByCreatedAsc(input.getId().getServiceRecipientId(), input.getId().getContactId().intValue());
        DateTime created = commandHistory.stream().findFirst().map(BaseCommand::getCreated).orElse(null);

        ServiceRecipientAssociatedContactViewModel result = new ServiceRecipientAssociatedContactViewModel();
        result.serviceRecipientId = input.getId().getServiceRecipientId();
        result.contactId = contact.getId().intValue();
        result.created = created;
        result.associatedTypeIds = input.getAssociatedTypeIds() != null
            ? input.getAssociatedTypeIds().toArray(Integer[]::new)
            : new Integer[0];
        result.archived = input.getArchived();

        var individual = contact instanceof Individual ? (Individual) contact : null;
        var organisation = contact instanceof Individual
                ? (Agency) ((Individual) contact).getCompany()
                : (Agency) contact; // should be an agency, not just company
        result.contact = individual != null ? individualToViewModel.apply(individual) : null;
        if (organisation != null) {
            result.organisation = agencyToViewModel.apply(organisation);
        }

        return result;
    }

}
