package com.ecco.webApi.finance;

import com.ecco.dom.contracts.RateCard;
import com.ecco.infrastructure.commands.CommandMappings;

import org.mapstruct.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RateCardMapper extends CommandMappings {

    /**
     * Updates a RateCard entity from a RateCardCommandDto.
     * This method only maps fields that are present in the DTO (not null).
     *
     * @param dto The source DTO with changes
     * @param rateCard The target RateCard entity to update
     */
    @Mapping(target = "startInstant", source = "startDateTime", qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "endInstant", source = "endDateTime", qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "chargeNameId", source = "chargeNameId")
    @Mapping(target = "contracts", ignore = true) // Contracts are handled separately
    @Mapping(target = "rateCardEntries", ignore = true) // Not in the DTO
    @Mapping(target = "id", ignore = true) // Don't modify the ID
    @Mapping(target = "collectionId", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateRateCardFromDto(RateCardCommandDto dto, @MappingTarget RateCard rateCard);

    /**
     * Converts a LocalDateTime to Instant at UTC zone
     */
    @Named("localDateTimeToInstant")
    default Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime != null ? localDateTime.toInstant(ZoneOffset.UTC) : null;
    }
}
