package com.ecco.webApi.finance;

import com.ecco.finance.webApi.dto.SalesInvoice;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * View model, for JSON serialization.
 *
 * @since 12/10/2016
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateClientSalesInvoiceCommandViewModel extends BaseServiceRecipientCommandViewModel implements SalesInvoice {

    private LocalDate invoiceDate;

    CreateClientSalesInvoiceCommandViewModel() { } // For Jackson

    public CreateClientSalesInvoiceCommandViewModel(Integer serviceRecipientId, LocalDate invoiceDate) {
        super(buildUri(serviceRecipientId), serviceRecipientId);
        this.invoiceDate = invoiceDate;
    }

    public static String buildUri(Integer serviceRecipientId) {
        return UriComponentsBuilder.fromUriString("finance/invoice/serviceRecipient/{id}/").buildAndExpand(serviceRecipientId).toString();
    }

    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    @Override
    public BigDecimal getAmount() {
        return BigDecimal.ZERO;
    }

    @Override
    public Status getStatus() {
        return Status.DRAFT;
    }
}
