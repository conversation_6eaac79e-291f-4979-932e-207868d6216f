package com.ecco.webApi.rota;

import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;
import java.time.Instant;

/**
 * Command to perform an action on a repeating appointment schedule - ALLOCATE / DEALLOCATE.
 * Could be called ScheduleActionCommandDto - but fits with a single AppointmentActionCommandDto.
 */
@Slf4j
@NoArgsConstructor
public class AppointmentRecurringActionCommandDto extends BaseServiceRecipientCommandViewModel {

    @NonNull
    public static final String OPERATION_ALLOCATE = "allocate";
    @NonNull
    public static final String OPERATION_DEALLOCATE = "deallocate";

    @NonNull
    public String operation; // allocate / deallocate

    /**
     * The appointment reference to the calendar system. See RotaAppointmentViewModel.
     * Could be calendarId, itemUid, recurrence handle, but could be RecurringEntry.Handle to represent the whole schedule.
     */
    @NonNull
    public String activityRef;

    @NonNull
    public Integer serviceRecipientId; // activityRef serviceRecipientId

    @NonNull // could be nullable if from 'appointment schedule'
    public String resourceFilter;

    @NonNull // could be nullable if from 'appointment schedule'
    public String demandFilter;

    /**
     * Apply the recurring action according to parts of a schedule defined here.
     * Typically this will be 'calendarDays', perhaps with 'start' and 'end', but could be others.
     * Many properties of the schedule may be unused, but we will only store the defined properties.
     * Nonnull - because we want an applicableFrom date for accuracy..
     * NB Possibly look at using RecurringEntryDefinition as a specific representation for creating recurring entries.
     * The command dto was useful since we don't want to break non-null requirements in RecurringDemandScheduleDto.kt
     */
    @NonNull
    public ServiceRecipientAppointmentScheduleCommandDto partSchedule;

    // ***** ALLOCATE
    @Nullable
    public Integer allocateResourceId; // the workerJobId or buildingId (building or carerun or bookable resource)

    @Nullable
    public Integer allocateRescheduledMins;

    // ***** DEALLOCATE
    @Nullable
    public Integer deallocateResourceId; // the workerJobId or buildingId (building or carerun or bookable resource)

    /**
     * For tests only (since Jackson uses default constructor).
     * NB Currently demandedResourceFilter and serviceRecipientFilter are required, but this is only to use the url for RotaHandler - but it may be we do this from a schedule not rota.
     */
    public AppointmentRecurringActionCommandDto(@NonNull String operation, @NonNull String activityRef, int serviceRecipientId,
                                                @NonNull String resourceFilter, @NonNull String demandFilter,
                                                @NonNull ServiceRecipientAppointmentScheduleCommandDto partSchedule) {
        super(UriComponentsBuilder
                .fromUriString("rota/{demandedFilter}/appointment-recurring-action/{serviceRecipientId}/")
                .buildAndExpand(resourceFilter, serviceRecipientId)
                .toUriString(), serviceRecipientId);
        this.operation = operation;
        this.activityRef = activityRef;
        this.serviceRecipientId = serviceRecipientId;
        this.resourceFilter = resourceFilter;
        this.demandFilter = demandFilter;
        this.partSchedule = partSchedule;
    }

    public Instant getCacheResetDate() {
        return partSchedule.applicableFromDate != null
            ? partSchedule.applicableFromDate.atStartOfDay(EccoTimeUtils.LONDON).toInstant()
            : null;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        switch (this.operation) {
            case OPERATION_ALLOCATE:
                if (allocateResourceId == null) {
                    log.error("Required field: allocateResourceId");
                    return false;
                }
                break;
            case OPERATION_DEALLOCATE:
                if (deallocateResourceId == null) {
                    log.error("Required field: deallocateResourceId");
                    return false;
                }
                /*if (partSchedule.days == null) {
                    log.error("Required field: days (else we get the deallocate bug)");
                    return false;
                }*/
                break;
            default:
                throw new IllegalArgumentException("operation not understood: " + this.operation);
        }

        return valid;
    }

}
