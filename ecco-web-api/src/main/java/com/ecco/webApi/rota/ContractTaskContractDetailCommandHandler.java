package com.ecco.webApi.rota;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.contracts.Contract;
import com.ecco.repositories.contracts.ContractRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskCommandHandler;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskParams;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@Component
public class ContractTaskContractDetailCommandHandler extends ServiceRecipientTaskCommandHandler<ContractTaskContractDetailCommandViewModel> {

    private final ContractRepository contractRepository;
    private final ListDefinitionRepository listDefinitionRepository;

    @Autowired
    public ContractTaskContractDetailCommandHandler(ObjectMapper objectMapper,
                                                    @NonNull WorkflowTaskController workflowTaskController,
                                                    @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                    ContractRepository contractRepository,
                                                    ListDefinitionRepository listDefinitionRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ContractTaskContractDetailCommandViewModel.class);
        this.contractRepository = contractRepository;
        this.listDefinitionRepository = listDefinitionRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ContractTaskContractDetailCommandViewModel dto) {

        Integer contractId = dto.contractId != null ? dto.contractId : null;
        Integer serviceRecipientId = params.getServiceRecipientId();

        switch (dto.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                Contract c = addContract(dto);
                contractId = c.getId();
                serviceRecipientId = c.getServiceRecipient().getId();
                break;
            case BaseCommandViewModel.OPERATION_UPDATE:
                updateContract(dto);
                break;
            case BaseCommandViewModel.OPERATION_REMOVE:
                deleteContract(dto);
                break;
            default:
                throw new IllegalArgumentException("cannot handle operation: " + dto.operation);
        }

        return CommandResult.ofLink(linkToApi(methodOn(ContractController.class).getContract(contractId)).withSelfRel())
                .withTargetId(serviceRecipientId);
    }

    private Contract addContract(ContractTaskContractDetailCommandViewModel dto) {
        Contract c = new Contract();
        applyChanges(c, dto);
        return contractRepository.save(c);
    }

    private void updateContract(ContractTaskContractDetailCommandViewModel dto) {
        applyChanges(contractRepository.findOne(dto.contractId), dto);
    }

    private void deleteContract(ContractTaskContractDetailCommandViewModel dto) {
        contractRepository.deleteById(dto.contractId);
    }

    private void applyChanges(Contract c, ContractTaskContractDetailCommandViewModel dto) {
        if (dto.name != null) {
            c.setName(dto.name.to);
        }
        if (dto.agreedCharge != null) {
            c.setAgreedCharge(dto.agreedCharge.to);
        }
        if (dto.contractTypeId != null) {
            c.setContractType(dto.contractTypeId.to != null ? listDefinitionRepository.findById(dto.contractTypeId.to).orElseThrow() : null);
        }
        if (dto.startDateTime != null) {
            c.setStartInstant(dto.startDateTime.to == null ? null : dto.startDateTime.to.toInstant(ZoneOffset.UTC));
        }
        if (dto.endDateTime != null) {
            c.setEndInstant(dto.endDateTime.to == null ? null : dto.endDateTime.to.toInstant(ZoneOffset.UTC));
        }
        if (dto.PONumbers != null) {
            c.setPONumbers(dto.PONumbers.to);
        }
    }

}
