package com.ecco.webApi.rota;

import com.ecco.dom.agreements.ClientSalesInvoice;
import com.ecco.finance.webApi.dto.ClientSalesInvoiceResource;
import com.ecco.finance.webApi.dto.SalesInvoice;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

import java.math.BigDecimal;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static com.ecco.finance.webApi.dto.SalesInvoice.Status.DRAFT;
import static com.ecco.finance.webApi.dto.SalesInvoice.Status.POSTED;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * @since 12/10/2016
 */
public class ClientSalesInvoiceResourceAssembler extends RepresentationModelAssemblerSupport<ClientSalesInvoice, ClientSalesInvoiceResource> {
    public ClientSalesInvoiceResourceAssembler() {
        super(RotaActivityInvoiceController.class, ClientSalesInvoiceResource.class);
    }

    public ClientSalesInvoice fromResource(Integer serviceRecipientId, SalesInvoice invoice) {
        return new ClientSalesInvoice(serviceRecipientId, invoice.getInvoiceDate());
    }

    @Override
    @NonNull
    public ClientSalesInvoiceResource toModel(ClientSalesInvoice clientSalesInvoice) {
        final ClientSalesInvoiceResource resource = new ClientSalesInvoiceResource(clientSalesInvoice.getServiceRecipientId(), clientSalesInvoice.getId(), clientSalesInvoice.getInvoiceDate(),
                clientSalesInvoice.isPosted() ? POSTED : DRAFT, BigDecimal.ZERO);
        resource.add(linkToInvoiceDetail(clientSalesInvoice.getId()).withSelfRel());
        return resource;
    }

    private static LinkBuilder linkToInvoiceDetail(Integer invoiceId) {
        return linkToApi(methodOn(RotaActivityInvoiceController.class).fetchSingleInvoice(invoiceId));
    }
}
