package com.ecco.webApi.evidence;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.UUID;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dao.SignatureRepository;
import com.ecco.evidence.dom.Signature;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NotFoundException;

@RestController
@RequestMapping("/signatures")
@PreAuthorize("hasRole('ROLE_STAFF')")
public class SignatureController extends BaseWebApiController {
    private final SignatureRepository signatureRepository;

    private final SignatureToViewModel signatureToViewModel = new SignatureToViewModel();


    @Autowired
    public SignatureController(SignatureRepository signatureRepository) {
        this.signatureRepository = signatureRepository;
    }

    @RequestMapping(value = "/{id}/", method = GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public SignatureViewModel findOne(@PathVariable UUID id, HttpServletResponse response) {
        Signature signature = signatureRepository.findById(id).orElse(null);

        cacheForXHours(24, response);

        if (signature == null) {
            throw new NotFoundException(id);
        }

        return signatureToViewModel.apply(signature);
    }
}
