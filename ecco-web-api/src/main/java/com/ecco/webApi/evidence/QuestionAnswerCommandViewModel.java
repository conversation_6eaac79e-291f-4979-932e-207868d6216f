package com.ecco.webApi.evidence;

import java.util.UUID;
import javax.annotation.Nullable;

import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import org.springframework.web.util.UriComponentsBuilder;

public class QuestionAnswerCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public String operation;

    public long questionDefId;

    public UUID workUuid;

    @Nullable
    public ChangeViewModel<String> answerChange;

    /** For Jackson etc */
    QuestionAnswerCommandViewModel() {
        super();
    }

    public QuestionAnswerCommandViewModel(UUID workUuid, int serviceRecipientId,
                                          EvidenceGroup evidenceGroup, EvidenceTask evidenceTask, long questionDefId) {
        super(UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/questionanswer/{questionDefId}/")
                .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName(), questionDefId)
                .toString(),
            serviceRecipientId);
        this.questionDefId = questionDefId;
        this.workUuid = workUuid;
    }

    public boolean hasChanges() {
        return answerChange != null;
    }

    @Override
    public String toString() {
        return "QuestionAnswerCommandViewModel [operation=" + operation + ", questionId="
                + questionDefId + ", workUuid=" + workUuid + ", answerChange=" + answerChange + ", serviceRecipientId="
                + serviceRecipientId + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp
                + "]";
    }

}
