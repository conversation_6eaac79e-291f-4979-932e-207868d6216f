package com.ecco.webApi.evidence

import com.ecco.infrastructure.dom.BaseCommand
import org.springframework.context.ApplicationEvent

class CommandCreatedEvent
/**
 * Event used to intercept and communicate to other systems.
 * @param urlServletBase for the purposes of identifying the source system (eg in emails, to know if its test/live)
 * @param command the command created
 * @param viewModel the view model used to create and process the command
 */
internal constructor(
    val urlServletBase: String,
    val command: BaseCommand<*>,
    val viewModel: BaseCommandViewModel,
) :
    /* we treat the command as the source, because it could be triggered from there
    we only do outside of the command because we want the already-constructed viewModel
    which is only available in the web api, not ecco-infrastructure as per BaseCommand */ ApplicationEvent(command)