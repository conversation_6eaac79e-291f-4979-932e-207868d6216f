package com.ecco.webApi.evidence;

import com.ecco.dao.EvidenceFormWorkSummary;

import org.jspecify.annotations.Nullable;
import java.util.function.Function;


public class EvidenceFormWorkSummaryToViewModel implements Function<EvidenceFormWorkSummary, EvidenceFormWorkViewModel> {

    @Override
    @Nullable
    public EvidenceFormWorkViewModel apply(@Nullable EvidenceFormWorkSummary input) {
        if (input == null) {
            throw new NullPointerException("input EvidenceFormWorkSummary must not be null");
        }

        EvidenceFormWorkViewModel viewModel = new EvidenceFormWorkViewModel();
        viewModel.id = input.getId();
        viewModel.requestedDelete = input.getRequestedDelete() != null;
        viewModel.taskName = input.getTaskName();
        viewModel.serviceRecipientId = input.getServiceRecipientId();
        viewModel.serviceAllocationId = input.getServiceAllocationId();
        viewModel.authorDisplayName = input.getAuthor().getDisplayName();

        viewModel.workDate = input.getWorkDate().toLocalDateTime(); // TODO: review did involve Locale.ROOT
        viewModel.createdDate = input.getCreatedDate() == null ? null : input.getCreatedDate().toLocalDateTime();
        viewModel.signatureId = input.getSignatureId();

        viewModel.form = input.getForm();
        viewModel.formDefinitionUuid = input.getFormDefinitionUuid();

        viewModel.comment = input.getComment();
        viewModel.minsSpent = input.getCommentMinutesSpent();
        viewModel.commentTypeId = input.getCommentTypeId();

        return viewModel;
    }

}
