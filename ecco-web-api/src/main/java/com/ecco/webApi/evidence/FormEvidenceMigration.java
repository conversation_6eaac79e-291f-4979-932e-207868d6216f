package com.ecco.webApi.evidence;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.Referral;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.util.FlagMap;
import com.ecco.service.ReferralService;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.util.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import static com.ecco.service.security.RunAsTemplate.runAsExternalUserAccountIfNecessary;

/**
 * Migration code which is part of the application (ie, not acceptance-tests) and Java-based
 * so we can run on startup (and avoid Nashorn for this one-off task).
 * Designed to be called with specific parameters to build a FormUpdateCommandViewModel from
 * the appropriate source, depending on the customer need.
 */
@RequiredArgsConstructor
public class FormEvidenceMigration {

    private final ReferralService referralService;
    private final ReferralRepository referralRepository;
    private final EvidenceFormWorkController formController;
    private final ListDefinitionRepository listDefinitionRepository;
    private final ObjectMapper objectMapper;

    private Referral getReferral(long referralId) {
        List<Referral> referrals = this.referralService.getCustomProperties(Arrays.asList(referralId), null);
        Referral referral = referrals.get(0);

        // apply the srId
        int serviceRecipientId = referralRepository.getServiceRecipientId(referralId);
        ReferralServiceRecipient sr = new ReferralServiceRecipient();
        sr.setId(serviceRecipientId);
        referral.setServiceRecipient(sr);

        return referral;
    }

    private EvidenceFormSnapshotCommandViewModel constructViewModel(int serviceRecipientId, @NonNull String taskName,
                                                                    @NonNull String evidenceGroup, @NonNull UUID fromDefinitionUuid,
                                                                    @NonNull JsonNode jsonPatch) {
        EvidenceFormSnapshotCommandViewModel vm = new EvidenceFormSnapshotCommandViewModel(BaseCommandViewModel.OPERATION_ADD,
                serviceRecipientId, taskName, evidenceGroup, fromDefinitionUuid);
        vm.workUuid = UUID.randomUUID();
        vm.timestamp = Instant.now();
        vm.workDate = ChangeViewModel.changeNullTo(DateTimeUtils.convertFromUtcToUsersLocalDateTime(new DateTime()));
        vm.jsonPatch = jsonPatch;
        return vm;
    }

    // see InboundReferralController
    private String savePatch(int serviceRecipientId, String taskName, String evidenceGroup, UUID formDefinitionUuid, JsonNode jsonPatch) throws IOException {

        if (jsonPatch == null) {
            return null;
        }
        // NB asText on a container/array node produces empty string even if children are in it
        String asText = objectMapper.writeValueAsString(jsonPatch);
        if (StringUtils.isEmpty(asText) || "null".equals(asText) || "{}".equals(asText)) {
            return null;
        }

        EvidenceFormSnapshotCommandViewModel vm = constructViewModel(serviceRecipientId, taskName, evidenceGroup, formDefinitionUuid, jsonPatch);
        String jsonViewModel = objectMapper.writeValueAsString(vm);
        //{"commandName":"formUpdate","commandUri":"service-recipients/200641/evidence/json/referralDetails/referralDetails/","uuid":"f39630cc-af3f-4d64-7a61-ddaa345ea062","operation":"add","timestamp":"2018-02-08T15:36:29.076Z","serviceRecipientId":200641,"evidenceGroup":"referralDetails","taskName":"referralDetails","workUuid":"993c0f9e-4127-4312-769c-d5bd76a123a8","workDate":{"from":null,"to":"2018-02-08T15:36:29.073"},"jsonPatch":[{"op":"replace","path":"/medication/0/name","value":"save in Task.referralDetails4"}]}

        EvidenceParams params = new EvidenceParams();
        params.setServiceRecipientId(serviceRecipientId);
        params.setTaskName(taskName);
        params.setEvidenceGroupKey(evidenceGroup);

        Result result = formController.patchSnapshot(SecurityContextHolder.getContext().getAuthentication(), params, jsonViewModel);
        return result.getId();
    }

    /**
     * This is one conversion that takes the flagMap (eg copied from referralDetails_p2p) and finds the source of the data
     * in the various custom data maps on Referral, and transforms and saves them into an EvidenceFormSnapshot)
     * @return the id of the evidence, or null
     */
    public String singleReferralFromFlagMap(long referralId, String flagMapAsCSV, HashMap<String,
            String> fieldNameTranslation, String evidenceGroup, UUID formDefinitionUuid) {

        Referral referral = this.getReferral(referralId);

        FormEvidenceFromFlagMap flagMapMigration = new FormEvidenceFromFlagMap(true, new FlagMap(flagMapAsCSV),
                fieldNameTranslation, this.objectMapper, this.listDefinitionRepository);
        JsonNode jsonPatch = flagMapMigration.apply(referral);

        // see InboundReferralController
       return runAsExternalUserAccountIfNecessary(() -> {
            try {
                return savePatch(referral.getServiceRecipientId(), evidenceGroup,
                                evidenceGroup, formDefinitionUuid, jsonPatch);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
       });
    }

}
