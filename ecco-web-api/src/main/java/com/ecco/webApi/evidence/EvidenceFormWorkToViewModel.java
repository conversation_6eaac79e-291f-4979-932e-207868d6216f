package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceFormWork;

import org.jspecify.annotations.Nullable;
import java.util.function.Function;

public class EvidenceFormWorkToViewModel implements Function<EvidenceFormWork, EvidenceFormWorkViewModel> {

    @Override
    @Nullable
    public EvidenceFormWorkViewModel apply(@Nullable EvidenceFormWork work) {

        EvidenceFormWorkViewModel viewModel = new EvidenceFormWorkViewModel();
        viewModel.id = work.getId();
        viewModel.requestedDelete = work.isRequestedDelete();
        // NB we have lasted a long time without the proper taskName being here, so must be okay!
        viewModel.taskName = Long.toString(work.getTaskDefId());
        viewModel.serviceRecipientId = work.getServiceRecipientId();
        viewModel.serviceAllocationId = work.getServiceRecipient().getServiceAllocationId();
        viewModel.authorDisplayName = work.getAuthor().getDisplayName();
        viewModel.workDate = work.getWorkDate().toLocalDateTime();
        viewModel.createdDate = work.getCreated() == null ? null : work.getCreated().toLocalDateTime();
        // a 'snapshot' has to exist, even if the json doesn't
        if (work.getSnapshot() != null) {
            viewModel.form = work.getSnapshot().getJson();
            viewModel.formDefinitionUuid = work.getSnapshot().getFormDefinitionUuid();
        }
        viewModel.signatureId = work.getSignature() != null ? work.getSignature().getId() : null;
        if (work.getComment() != null) {
            viewModel.comment = work.getComment().getComment();
            viewModel.minsSpent = work.getComment().getMinutesSpent();
            viewModel.commentTypeId = work.getComment().getTypeDefId();
        }

        return viewModel;
    }

}
