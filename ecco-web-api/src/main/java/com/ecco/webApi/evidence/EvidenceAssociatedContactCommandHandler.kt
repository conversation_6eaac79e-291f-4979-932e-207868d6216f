package com.ecco.webApi.evidence

import com.ecco.calendar.core.CalendarService
import com.ecco.dao.ClientRepository
import com.ecco.dao.EvidenceSupportWorkRepository
import com.ecco.dao.commands.ServiceRecipientCommandRepository
import com.ecco.dom.CalendarEventSnapshot
import com.ecco.dom.commands.EvidenceAssociatedContactCommand
import com.ecco.evidence.EvidenceTask
import com.ecco.evidence.ParentChildResolver
import com.ecco.evidence.repositories.CalendarEventSnapshotRepository
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.hibernate.EntityUriMapper
import com.ecco.infrastructure.time.toJDKInstant
import com.ecco.security.SecurityUtil
import com.ecco.security.repositories.ContactRepository
import com.ecco.service.TaskDefinitionService
import com.ecco.serviceConfig.repositories.ServiceRepository
import com.ecco.webApi.CommandResult
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator
import com.ecco.webApi.evidence.EvidenceAssociatedContactCommandViewModel.AttendanceStatus.END
import com.ecco.webApi.evidence.EvidenceAssociatedContactCommandViewModel.AttendanceStatus.START
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.security.core.Authentication
import org.springframework.util.Assert
import java.io.Serializable

/**
 * When a rota visit 'start'/'stop' is hit, client side createLoneWorkerCommand creates this audit.
 */
class EvidenceAssociatedContactCommandHandler(
    objectMapper: ObjectMapper,
    serviceRecipientCommandRepository: ServiceRecipientCommandRepository,
    private val workRepository: EvidenceSupportWorkRepository,
    private val clientRepository: ClientRepository,
    private val contactRepository: ContactRepository,
    private val eventStatusRepository: CalendarEventSnapshotRepository,
    serviceRecipientRepository: ServiceRecipientRepository,
    serviceRepository: ServiceRepository,
    parentChildResolver: ParentChildResolver,
    entityUriMapper: EntityUriMapper,
    calendarService: CalendarService,
    taskDefinitionService: TaskDefinitionService,
) : EvidenceCommandHandler<EvidenceAssociatedContactCommandViewModel, EvidenceAssociatedContactCommand, AssociatedContactParams>(
    objectMapper,
    serviceRecipientRepository,
    serviceRecipientCommandRepository,
    serviceRepository,
    parentChildResolver,
    calendarService,
    taskDefinitionService,
    entityUriMapper,
    EvidenceAssociatedContactCommandViewModel::class.java,
) {
    override fun handleInternal(
        parentServiceRecipientId: Int,
        childServiceRecipientId: Int?,
        auth: Authentication,
        params: AssociatedContactParams,
        viewModel: EvidenceAssociatedContactCommandViewModel,
    ): CommandResult? {
        val type = taskDefinitionService.getTaskType(EvidenceTask.fromTaskName(params.taskName))
        Assert.state(!taskDefinitionService.isThreatBased(type), "Threat based tasks are not supported")
        findOrCreateWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel)

//        if (viewModel.hasChanges()) {
//        }
//        else {
//        }
        return null // TODO: Could return Hateoas Link to created/updated
    }

    override fun createCommand(
        targetId: Serializable?,
        params: AssociatedContactParams,
        requestBody: String,
        viewModel: EvidenceAssociatedContactCommandViewModel,
        userId: Long,
    ): EvidenceAssociatedContactCommand {
        Assert.state(
            params.serviceRecipientId == viewModel.serviceRecipientId,
            "serviceRecipientId in body must match URI",
        )
        Assert.state(params.contactId == viewModel.contactId, "contactId in body must match URI")
        return EvidenceAssociatedContactCommand(
            viewModel.uuid,
            viewModel.timestamp,
            userId,
            requestBody,
            params.serviceRecipientId,
            params.taskName,
            params.evidenceGroupKey,
        )
    }

    /** Other commands may create work first, so we may be creating a new work item or updating it.
     */
    private fun findOrCreateWork(
        parentServiceRecipientId: Int,
        childServiceRecipientId: Int?,
        auth: Authentication,
        params: AssociatedContactParams,
        viewModel: EvidenceAssociatedContactCommandViewModel,
    ) {
        // NB 'start visit' kicks off this lone working which includes a workUuid which gets created
        if (!workRepository.existsById(viewModel.workUuid)) {
            // NOTE: This will be null for non-Referral evidence
            val client = clientRepository.findOneByServiceRecipientId(parentServiceRecipientId)
            val task = EvidenceTask.fromTaskName(params.taskName)
            val grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey)
            val uri = entityUriMapper.uriForEntity(EvidenceAssociatedContactCommandViewModel::class.java.simpleName, viewModel.uuid)
            ensureConcreteRecurrence(uri, viewModel.eventId)
            // create a basic work item
            val work =
                createNewSupportWork(
                    parentServiceRecipientId,
                    childServiceRecipientId,
                    auth,
                    task,
                    grp,
                    viewModel.workUuid,
                    viewModel.timestamp,
                    client,
                )
                    // foreign key to cosmo_item
                    .withEventId(viewModel.eventId)
                    .build()
            entityManager.persist(work)
        }

        // it will be rare that a lone worker event doesn't already exist,
        // since it's pre-populated (for live monitoring, and added to for scheduling)
        // however, should the event not exist, then populate as normal

        val status =
            eventStatusRepository.findOneByEventUid(viewModel.eventId).orElseGet {
                val result = CalendarEventSnapshot()

                // get the lone worker for the workUuid, or create one
                val event = calendarService.findEntry(viewModel.eventId)
                val resourceCalendarId = ServiceRecipientRotaDecorator.attendeeOwner(event).get().calendarId
                val resource = contactRepository.findByCalendarId(resourceCalendarId)
                val sr = serviceRecipientRepository.findById(viewModel.serviceRecipientId!!)

//            val link: Link = viewModel. .getLinks().getLink("demand-schedule").orElse(null)!!
//            val scheduleId = CalendarEventSnapshotServiceImpl.EXTRACT_ID_FN.apply(link!!.href)

                // PLANNED
                result.eventUid = viewModel.eventId
                result.serviceRecipientId = viewModel.serviceRecipientId
                result.demandContactId = sr.get().contact?.id
                result.resourceContactId = resource.get().id
                result.plannedStartInstant = event.startInstant()
                result.plannedEndInstant = event.endInstant()

                result
            }

        viewModel.location?.let {
            status.location = objectMapper.writeValueAsString(it)
        }

        // ACTUAL
        if (viewModel.attendanceStatus == START) {
            status.contactId = SecurityUtil.getUser(auth).contact.id // current user recording the work
            status.startInstant = viewModel.timestamp.toJDKInstant()
            status.workUuid = viewModel.workUuid
        }
        if (viewModel.attendanceStatus == END) {
            status.endInstant = viewModel.timestamp.toJDKInstant()
        }

        eventStatusRepository.save(status)
    }

    override fun persistCommand(command: EvidenceAssociatedContactCommand): EvidenceAssociatedContactCommand {
        val savedCmd = super.persistCommand(command)
        entityManager.flush()
        return savedCmd
    }
}