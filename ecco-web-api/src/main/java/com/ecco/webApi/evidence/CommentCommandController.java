package com.ecco.webApi.evidence;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import org.jspecify.annotations.NonNull;

import com.ecco.evidence.EvidenceTask;
import com.ecco.service.TaskDefinitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CommentCommand;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

@RestController
public class CommentCommandController extends BaseWebApiController {

    @NonNull
    private final ServiceRecipientCommandRepository commandRepository;

    @NonNull
    private final TaskDefinitionService taskDefinitionService;

    @NonNull
    private final RiskCommentCommandHandler riskCommentCommandHandler;

    @NonNull
    private final SupportCommentCommandHandler supportCommentCommandHandler;

    @NonNull
    private final QuestionnaireCommentCommandHandler questionnaireCommentCommandHandler;

    @NonNull
    private final EvidenceFormCommentCommandHandler evidenceFormCommentCommandHandler;

    @NonNull
    private GoalCommandExtractCommandViewModelJson<CommentCommand> extractJsonBody;


    @Autowired
    public CommentCommandController(@NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                    @NonNull TaskDefinitionService taskDefinitionService,
                                    @NonNull RiskCommentCommandHandler riskCommentCommandHandler,
                                    @NonNull SupportCommentCommandHandler supportCommentCommandHandler,
                                    @NonNull QuestionnaireCommentCommandHandler questionnaireCommentCommandHandler,
                                    @NonNull EvidenceFormCommentCommandHandler evidenceFormCommentCommandHandler,
                                    @Qualifier("SvcRecExtractJson")
                                    @NonNull GoalCommandExtractCommandViewModelJson extractJsonBody) {
        this.commandRepository = serviceRecipientCommandRepository;
        this.taskDefinitionService = taskDefinitionService;
        this.riskCommentCommandHandler = riskCommentCommandHandler;
        this.supportCommentCommandHandler = supportCommentCommandHandler;
        this.questionnaireCommentCommandHandler = questionnaireCommentCommandHandler;
        this.evidenceFormCommentCommandHandler = evidenceFormCommentCommandHandler;
        this.extractJsonBody = extractJsonBody;
    }

    /**
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/commands/evidence/{evidenceGroupKey}/comments/",
            method = RequestMethod.GET, produces = APPLICATION_JSON_VALUE)
    public String findCommandsByReferralAndEvidenceGroup(
            @PathVariable int serviceRecipientId,
            @NonNull @PathVariable String evidenceGroupKey) {

        List<CommentCommand> commands =
                commandRepository.findAllCommentsByServiceRecipientIdAndEvidenceGroup(serviceRecipientId, evidenceGroupKey);

        CharSequence joinedJson = commands.stream().map(extractJsonBody).collect(Collectors.joining(","));
        // TODO: Could go with Stream.of to allow lazy eval if we can return a stream
        return "[" + joinedJson + "]";
    }

    /**
     * @param params
     *  contains taskName, which is what we used to call sourcePage.  A number of different tasks could relate to the same
     *  evidenceGroupId for recording of evidence, e.g. splitting needs over multiple pages.
     */
    @SuppressWarnings("MVCPathVariableInspection") // Inspection gets it wrong
    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/{taskName}/comments/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result createCommentV2(
            @NonNull Authentication authentication,
            EvidenceParams params,
            @NonNull @RequestBody String requestBody) throws IOException {

        EvidenceTask task = EvidenceTask.fromTaskName(params.taskName);

        var type = taskDefinitionService.getTaskType(task);
        if (taskDefinitionService.isThreatBased(type)) {
            return riskCommentCommandHandler.handleCommand(authentication, params, requestBody);
        } else if (taskDefinitionService.isQuestionnaireBased(type)) {
            return questionnaireCommentCommandHandler.handleCommand(authentication, params, requestBody);
        } else if (taskDefinitionService.isSupportSmartStepBased(type)) {
            return supportCommentCommandHandler.handleCommand(authentication, params, requestBody);
        } else if (taskDefinitionService.isCustomFormBased(type)) {
            return evidenceFormCommentCommandHandler.handleCommand(authentication, params, requestBody);
        } else if (taskDefinitionService.isCustomFormAllowed(task.getTaskName(), type)) {
            return evidenceFormCommentCommandHandler.handleCommand(authentication, params, requestBody);
        } else {
            throw new IllegalArgumentException("Unsupported evidenceGroupKey: " + params.evidenceGroupKey);
        }
    }
}
