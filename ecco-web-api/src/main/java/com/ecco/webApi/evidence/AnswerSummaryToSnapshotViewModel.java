package com.ecco.webApi.evidence;

import java.util.function.Function;

import org.jspecify.annotations.Nullable;

import com.ecco.dao.EvidenceQuestionAnswerSummary;

public class AnswerSummaryToSnapshotViewModel implements Function<EvidenceQuestionAnswerSummary, QuestionAnswerSnapshotViewModel> {

    @Override
    @Nullable
    public QuestionAnswerSnapshotViewModel apply(@Nullable EvidenceQuestionAnswerSummary input) {
        if (input == null) {
            throw new NullPointerException("input Referral must not be null");
        }

        QuestionAnswerSnapshotViewModel viewModel = new QuestionAnswerSnapshotViewModel();
        viewModel.id = input.getId();
        viewModel.questionId = input.getQuestionId();
        viewModel.answer = input.getAnswer();
        // workDate is redundant data, but set in the parent to be helpful to the client

        return viewModel;
    }

}
