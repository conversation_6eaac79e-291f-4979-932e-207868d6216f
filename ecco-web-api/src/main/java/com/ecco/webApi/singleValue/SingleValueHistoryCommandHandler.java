package com.ecco.webApi.singleValue;

import com.ecco.dao.SingleValueHistoryRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.SingleValueHistory;
import com.ecco.dom.commands.SingleValueHistoryCommand;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Component
public final class SingleValueHistoryCommandHandler
        extends ServiceRecipientCommandHandler<SingleValueHistoryCommandViewModel, SingleValueHistoryCommand, @NonNull SingleValueHistoryParams>
        implements HistoryItemCommandSupport<SingleValueHistory, SingleValueHistoryCommandViewModel> {

    @NonNull
    private final SingleValueHistoryRepository singleValueHistoryRepository;

    @NonNull
    private final HistoryItemCommandSupportHandler<SingleValueHistory, SingleValueHistoryCommandViewModel> support;

    @Autowired
    public SingleValueHistoryCommandHandler(ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull SingleValueHistoryRepository singleValueHistoryRepository) {
        super(objectMapper, serviceRecipientCommandRepository, SingleValueHistoryCommandViewModel.class);
        this.singleValueHistoryRepository = singleValueHistoryRepository;
        this.support = new HistoryItemCommandSupportHandler<>(singleValueHistoryRepository, this);
    }

    @Override
    public void applyChanges(SingleValueHistory entry, SingleValueHistoryCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.value != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.value, entry.value, "value");
                entry.setValue(cmdVM.value.to);
            }
        }
    }

    @Override
    public SingleValueHistory createItem(SingleValueHistoryCommandViewModel cmdVM) {
        assert cmdVM.serviceRecipientId != null;
        assert cmdVM.validFrom != null;
        return new SingleValueHistory(cmdVM.serviceRecipientId, cmdVM.key);
    }

    @Override
    public void warnIfPrevValueDoesntMatchDelegate(SingleValueHistoryCommandViewModel cmdVM, ChangeViewModel<java.time.LocalDateTime> change, java.time.LocalDateTime from, String fieldName) {
        warnIfPrevValueDoesntMatch(cmdVM, change, from, fieldName);
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull SingleValueHistoryParams params,
                                           @NonNull SingleValueHistoryCommandViewModel viewModel) {
        validateParams(params);
        assert Objects.equals(viewModel.serviceRecipientId, params.serviceRecipientId);

        // for each operation there we need to verify validTo dates so we load once here
        // we make use of the unique constraint on the table (idx_singlevalue_srId_key_vF)
        // so we can use the dates to locate correct values
        List<SingleValueHistory> svhList = this.singleValueHistoryRepository.
                findByServiceRecipientIdAndKeyOrderByValidFromDesc(params.serviceRecipientId, viewModel.key);

        support.handleHistoryCommand(viewModel, svhList);

        return null;
    }

    @NonNull
    @Override
    protected SingleValueHistoryCommand createCommand(Serializable targetId, @NonNull SingleValueHistoryParams params,
                                                      @NonNull String requestBody, @NonNull SingleValueHistoryCommandViewModel viewModel, long userId) {
        validateParams(params);
        if (params.key != null) {
            Assert.state(params.key.equals(viewModel.key),
                    "SingleValueHistory Handler: the 'key' on the url doesn't match the command view model");
        }

        return new SingleValueHistoryCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId, viewModel.key);
    }

    private static void validateParams(@NonNull SingleValueHistoryParams params) {
    }

}
