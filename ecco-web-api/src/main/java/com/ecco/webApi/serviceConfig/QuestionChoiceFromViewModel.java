package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.dom.QuestionAnswerChoiceView;
import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.dom.QuestionAnswerChoice;

import java.util.function.Function;

public class QuestionChoiceFromViewModel implements Function<QuestionAnswerChoiceView, QuestionAnswerChoice> {

    @Override
    @Nullable
    public QuestionAnswerChoice apply(@Nullable QuestionAnswerChoiceView input) {
        if (input == null) {
            throw new NullPointerException("input Question must not be null");
        }

        QuestionAnswerChoice result = new QuestionAnswerChoice();
        // use collection id to allow key to map same values, but don't setId() as Hibernate can't cope
        result.setCollectionId(input.id().intValue());
        result.setDisplayValue(input.displayValue());
        result.setDisplayImage(input.displayImage());
        result.setValue(input.value());

        return result;
    }

}
