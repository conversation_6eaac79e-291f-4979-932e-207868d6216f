package com.ecco.webApi.serviceConfig;

import org.jspecify.annotations.NonNull;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
public class CacheCommandController extends BaseWebApiController {

    @NonNull
    private final CacheCommandHandler cacheCommandHandler;

    @Autowired
    public CacheCommandController(
            @NonNull CacheCommandHandler cacheCommandHandler) {
        this.cacheCommandHandler = cacheCommandHandler;
    }

    @RequestMapping(value = "/cache/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Result processCacheCommand(
            @NonNull Authentication authentication,
            CacheParams params,
            @NonNull @RequestBody String requestBody) throws IOException {

        return cacheCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
