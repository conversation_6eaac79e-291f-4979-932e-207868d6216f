package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.util.UUID;

/**
 * Base command for configuration operations on action groups
 */
@Slf4j
public class ActionGroupDefCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public String operation;
    @NonNull
    public UUID actionGroupDefUuid;
    @Nullable
    public UUID outcomeDefUuid; // only required on addition
    @Nullable
    public ChangeViewModel<String> nameChange;
    @Nullable
    public ChangeViewModel<Integer> orderByChange;
    @Nullable
    public ChangeViewModel<Boolean> disabledChange;

    // Distinguisher so its clear looking at the stored commands
    // what outcome it is - it could be this, the OutcomeThreatCommandViewModel,
    // or some OutcomeHr.
    // The url alone might not be as helpful to remember, or as reliable
    // when parsing audits to the screen.
    //@Nullable
    //public OutcomeType outcomeType;

    public boolean hasChanges() {
        return nameChange != null || orderByChange != null || disabledChange != null;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected ActionGroupDefCommandViewModel() {
        super();
    }

    public ActionGroupDefCommandViewModel(@NonNull String operation, @NonNull UUID actionGroupDefUuid) {
        super(UriComponentsBuilder
                .fromUriString("config/actionGroupDef/")
                .toUriString());
        this.operation = operation;
        this.actionGroupDefUuid = actionGroupDefUuid;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.outcomeDefUuid == null) {
                log.error("Required field: outcomeDefUuid");
                return false;
            }
        }

        return valid;
    }

}
