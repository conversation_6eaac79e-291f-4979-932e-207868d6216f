package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Project;
import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.security.dom.LdapGroupMapping;
import com.ecco.security.repositories.LdapRepository;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.ProjectCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class ProjectCommandHandler extends BaseCommandHandler<ProjectCommandViewModel, Long, ConfigCommand, @Nullable Void> {

    @NonNull
    private final ProjectRepository projectRepository;
    @NonNull
    private final LdapRepository ldapRepository;
    @NonNull
    private final EntityRestrictionService entityRestrictionService;

    @Autowired
    public ProjectCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                 @NonNull ProjectRepository projectRepository,
                                 @NonNull LdapRepository ldapRepository,
                                 @NonNull EntityRestrictionService entityRestrictionService) {
        super(objectMapper, configCommandRepository, ProjectCommandViewModel.class);
        this.projectRepository = projectRepository;
        this.ldapRepository = ldapRepository;
        this.entityRestrictionService = entityRestrictionService;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull ProjectCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case ProjectCommandViewModel.OPERATION_ADD:
                this.addOrUpdateProject(auth, viewModel);
                break;

            case ProjectCommandViewModel.OPERATION_UPDATE:
                this.addOrUpdateProject(auth, viewModel);
                break;

            case ProjectCommandViewModel.OPERATION_REMOVE:
                this.removeProject(auth, viewModel);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addOrUpdateProject(Authentication auth, ProjectCommandViewModel viewModel) {
        Project project = (viewModel.projectId == null)
            ? createNewProject(viewModel)
            : projectRepository.findById(viewModel.projectId).orElseThrow(NullPointerException::new);

        if (viewModel.changeName != null) {
            project.setName(viewModel.changeName.to);
        }

        projectRepository.save(project);

        if (viewModel.projectId == null) {
            entityRestrictionService.ensureAcls();
        }

        if (viewModel.ldapGroupsToAdd != null) {
            for (String ldapGroup : viewModel.ldapGroupsToAdd) {
                addLdapGroup(project.getId(), ldapGroup);
            }
        }

    }

    private void removeProject(Authentication auth, ProjectCommandViewModel cmdVM) {
        Project project = this.projectRepository.findById(cmdVM.projectId).orElseThrow(NullPointerException::new);
        this.projectRepository.delete(project);
        // TODO delete from the service_projects
    }

    private Project createNewProject(ProjectCommandViewModel viewModel) {
        Project project = new Project(viewModel.projectId);
        return project;
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull ProjectCommandViewModel viewModel, long userId) {
        return new ProjectCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

    private void addLdapGroup(long projectId, String ldapGroup) {
        LdapGroupMapping ldap = new LdapGroupMapping(
                ldapGroup,
                Project.class.getName(),
                projectId);
        this.ldapRepository.save(ldap);
    }

}
