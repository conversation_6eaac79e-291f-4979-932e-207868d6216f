package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TaskDefinitionCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public static final String OPERATION_ADD = "add";
    @NonNull
    public static final String OPERATION_UPDATE = "update";
    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    public enum EditableEvidenceType { EVIDENCE_CHECKLIST, EVIDENCE_CUSTOMFORM,
        EVIDENCE_RISK, EVIDENCE_QUESTIONNAIRE, EVIDENCE_SUPPORT }

    public String operation;
    public @Nullable Integer taskDefinitionId;
    public @Nullable ChangeViewModel<String> nameChange;
    public TaskDefinition.@Nullable Type type;

    /** For Jackson etc */
    @Deprecated
    TaskDefinitionCommandViewModel() {
        super();
    }

    public TaskDefinitionCommandViewModel(@NonNull String operation, String name) {
        super(UriComponentsBuilder
                .fromUriString("service-config/taskDef/")
                .toUriString());
        this.operation = operation;
        if (name != null) {
            this.nameChange = ChangeViewModel.changeNullTo(name);
        }
    }

    public boolean hasChanges() {
        return operation.equalsIgnoreCase(OPERATION_ADD) || nameChange != null;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.taskDefinitionId != null) {
                log.error("Unexpected field on add: taskDefinitionId");
                return false;
            }
            if (this.nameChange == null) {
                log.error("Required field on add: nameChange");
                return false;
            }
            if (this.type == null) {
                log.error("Required field on add: type");
                return false;
            }
            try {
                EditableEvidenceType.valueOf(this.type.name());
            } catch (IllegalArgumentException e) {
                log.error("Required field on add: type is not of the types {}", (Object) EditableEvidenceType.values());
                return false;
            }
        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_UPDATE)) {
            if (taskDefinitionId == null) {
                log.error("Required field on update: taskDefinitionId");
                return false;
            }
            if (type != null) {
                log.error("Disallowed field on update: type");
                return false;
            }
        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_REMOVE)) {
            if (taskDefinitionId == null) {
                log.error("Required field on remove: taskDefinitionId");
                return false;
            }
        }

        return valid;
    }

    @Override
    public String toString() {
        return "TaskDefinitionCommandViewModel [operation=" + operation
                + ", taskDefinitionId=" + taskDefinitionId
                + ", nameChange=" + nameChange
                + ", type=" + type
                + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp + "]";
    }

}