package com.ecco.webApi.serviceConfig;

import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;

/**
 * Configuration of a questionGroup
 */
public class QuestionGroupCommandViewModel extends QuestionnaireBaseCommandViewModel {

    @Nullable
    public ChangeViewModel<String> nameChange;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected QuestionGroupCommandViewModel() {
        super();
    }

    public boolean hasChanges() {
        return nameChange != null;
    }

    public QuestionGroupCommandViewModel(@NonNull String operation, Integer id) {
        super(UriComponentsBuilder
                .fromUriString("config/questionnaire/questionGroup/")
                .toUriString(), operation, id);
    }

    // TODO toString inc all inherited fields

}
