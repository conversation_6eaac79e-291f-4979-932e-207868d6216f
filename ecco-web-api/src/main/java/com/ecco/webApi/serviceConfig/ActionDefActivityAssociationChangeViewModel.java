package com.ecco.webApi.serviceConfig;

import org.jspecify.annotations.NonNull;

import com.ecco.webApi.evidence.BaseCommandViewModel;

/**
 * Data-transfer object representing a command to add or remove an association
 * between a Goal and an Activity Type.
 */
public class ActionDefActivityAssociationChangeViewModel extends BaseCommandViewModel {
    @NonNull
    public static final String OPERATION_ADD = "add";

    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    /**
     * The operation to perform; either {@link #OPERATION_ADD}, or
     * {@link #OPERATION_REMOVE}.
     */
    @NonNull
    public String operation;

    /** The ID of the Action definition where the association is to be added or removed. */
    public long actionDefId;

    /** The ID of the Activity Type to be added or removed. */
    public long activityTypeId;
}
