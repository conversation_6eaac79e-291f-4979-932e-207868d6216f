package com.ecco.webApi.forms;

import com.ecco.config.dom.FormDefinition;

import java.util.function.Function;

public class FormDefinitionToViewModel implements Function<FormDefinition, FormDefinitionViewModel> {

    @Override
    public FormDefinitionViewModel apply(FormDefinition input) {

        final FormDefinitionViewModel result = new FormDefinitionViewModel();
        result.uuid = input.getId();
        result.name = input.getName();
        result.orderby = input.getOrderby();
        result.deleted = input.isHidden();
        result.definition = input.getBody();

        return result;
    }

}
