package com.ecco.webApi.featureConfig;

import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.webApi.controllers.BaseWebApiController;
import lombok.RequiredArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.WebAttributes;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequiredArgsConstructor
public class PublicController extends BaseWebApiController {

    final ApplicationProperties applicationProperties;

    @GetJson("/config/public/")
    @ReadOnlyTransaction
    public PublicConfigViewModel findPublicConfig(HttpServletResponse response) {
        // cache for an hour, but don't use etag as its not expensive to create
        PublicConfigViewModel config = new PublicConfigViewModel();

        config.loginProviders = applicationProperties.getLoginProviders();
        cacheModerately(response);
        return config;
    }

    /**
     * Get the last login exception of the session.
     * See https://spring.io/guides/tutorials/spring-boot-oauth2/#_social_login_custom_error
     */
    @GetJson("/login_error")
    public String error(HttpServletRequest request) {
        // TODO remove auth error?
        var msg = request.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION) == null
            ? "login failed (tech: no session?)"
            : ((AuthenticationException) request.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION)).getMessage();
        return "{\"login_error\":\"" + msg + "\"}";
    }

}
