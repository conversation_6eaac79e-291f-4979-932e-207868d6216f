package com.ecco.webApi.featureConfig;

import com.ecco.config.dom.SoftwareFeature;
import java.util.function.Function;

import org.jspecify.annotations.Nullable;

public final class FeatureVoteToViewModel implements Function<SoftwareFeature, FeatureViewModel> {

    @Nullable
    @Override
    public FeatureViewModel apply(@Nullable SoftwareFeature input) {
        if (input == null) {
            throw new NullPointerException("input SoftwareFeature must not be null");
        }

        FeatureViewModel vm = new FeatureViewModel();
        vm.name = input.getName();
        vm.description = input.getDescription();
        vm.defaultVote = input.getDefaultVote().toString();

        return vm;
    }

}