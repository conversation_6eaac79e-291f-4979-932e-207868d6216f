package com.ecco.webApi.featureConfig;

import org.jspecify.annotations.NonNull;

import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.config.service.FeatureEnablementVoter.Vote;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

public class FeatureVoteChangeCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public String name;

    @NonNull
    public ChangeViewModel<Vote> voteChange;


    public FeatureVoteChangeCommandViewModel() {
        super(UriComponentsBuilder
                .fromUriString("feature-config/")
                .toUriString());
    }
}
