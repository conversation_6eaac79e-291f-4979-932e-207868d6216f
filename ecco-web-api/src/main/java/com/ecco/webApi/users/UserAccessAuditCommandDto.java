package com.ecco.webApi.users;

import com.ecco.dom.commands.UserAccessAuditLevel;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

@Getter
@Setter
@Slf4j
@NoArgsConstructor // only for Cglib/Hibernate etc
public class UserAccessAuditCommandDto extends BaseServiceRecipientCommandViewModel {
    /**
     * Extract the taskName from the constructor to be referenced.
     * Not primitive, so it could be created by data-client command imports
     * This will then be verified by {@link com.ecco.webApi.evidence.BaseCommandViewModel#valid()}
     * Nice to be private, but then we need a setter anyway for command import
     */
    @NonNull
    public String taskName;

    /**
     * @see com.ecco.workflow.WorkflowTask
     */
    @Nullable
    public String taskHandle;

    public UserAccessAuditLevel level;

    /**
     * eg "qr"
     */
    public String source;

    /* constructor for tests only - Jackson creates the full object from JSON - this omits things that a
     * proper command handler will require, such as initialising the UUID etc via super(uuid,...) */
    public UserAccessAuditCommandDto(
            int serviceRecipientId, @NonNull String taskName, @Nullable String taskHandle, UserAccessAuditLevel level) {
        super(UriComponentsBuilder
                        .fromUriString("service-recipients/{serviceRecipientId}/tasks/{taskName}/audit")
                        .buildAndExpand(serviceRecipientId, taskName).toUriString(),
                serviceRecipientId);
        this.taskName = taskName;
        this.taskHandle = taskHandle;
        this.level = level;
    }
}
