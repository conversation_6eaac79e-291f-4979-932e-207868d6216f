package com.ecco.webApi.viewModels;

import org.springframework.hateoas.RepresentationModel;

public class QueuedCommandViewModel extends RepresentationModel<QueuedCommandViewModel> {

    public boolean valid;
    public Boolean executed;
    public Boolean dismissed;
    public Boolean archived;

    public Long userDeviceId;
    public String queueTime;
    public String executeTime;
    public String result; // ecco interpretation of the result status, see QueuedCommand.Status

    public CommandRequestViewModel commandRequest;
}
