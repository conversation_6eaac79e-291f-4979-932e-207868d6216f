package com.ecco.webApi.viewModels;

import com.ecco.dom.EvidenceGroup;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.ecco.evidence.EvidenceTask;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.UUID;

public class DeleteEvidenceCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public String reason;

    @NonNull
    public UUID requestDeletionUuid;

    @NonNull
    public String evidenceGroup;

    @NonNull
    public String evidenceTask; // NB helpful not to be taskName for not identifying as a task in the 'audits' tab

    /**
     * The view model that contains everything about this evidence in case
     * we haven't got the whole picture from the commands, and for convenience in one place.
     */
    @NonNull
    public String jsonViewModel;

    /** For Jackson etc */
    DeleteEvidenceCommandViewModel() {
        super();
    }

    /**
     * Constructor for testing.
     * The client code is mapped using Jackson, with a default constructor, where Nonnull's are verified
     */
    public DeleteEvidenceCommandViewModel(int serviceRecipientId, @NonNull UUID workUuid, @NonNull UUID requestDeletionUuid,
                                          @NonNull EvidenceGroup evidenceGroup,
                                          @NonNull EvidenceTask evidenceTask,
                                          @NonNull String jsonViewModel) {
        super(UriComponentsBuilder
                        .fromUriString("service-recipient/{serviceRecipientId}/evidence/{workUuid}/delete/")
                        .buildAndExpand(serviceRecipientId, workUuid)
                        .toString(),
                serviceRecipientId);
        this.requestDeletionUuid = requestDeletionUuid;
        this.evidenceGroup = evidenceGroup.nameAsLowercase();
        this.evidenceTask = evidenceTask.getTaskName();
        this.jsonViewModel = jsonViewModel;
    }

}
