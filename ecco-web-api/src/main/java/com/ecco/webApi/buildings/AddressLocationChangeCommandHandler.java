package com.ecco.webApi.buildings;

import com.ecco.buildings.dom.AddressCommand;
import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.buildings.repositories.AddressesCommandRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class AddressLocationChangeCommandHandler extends BaseCommandHandler<AddressLocationChangeCommandViewModel, Integer, AddressCommand, Integer> { //params is actually null

    private final AddressRepository addressRepository;

    public AddressLocationChangeCommandHandler(ObjectMapper objectMapper,
                                               AddressesCommandRepository commandRepository,
                                               AddressRepository addressRepository) {
        super(objectMapper, commandRepository, AddressLocationChangeCommandViewModel.class);
        this.addressRepository = addressRepository;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, Integer dontUse, @NotNull AddressLocationChangeCommandViewModel viewModel) {
        var adr = addressRepository.findById(viewModel.addressLocationId).orElseThrow();

        if (viewModel.disabled != null) {
            adr.setDisabled(viewModel.disabled.to);
        }
        addressRepository.save(adr);

        return new CommandResult().withMessage("address archived");
    }

    @Override
    protected AddressCommand createCommand(Serializable targetId, Integer dontUse, String requestBody, AddressLocationChangeCommandViewModel viewModel,
                                            long userId) {
        return new AddressCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, viewModel.addressLocationId);
    }
}
