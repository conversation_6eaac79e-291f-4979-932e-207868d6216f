package com.ecco.webApi.taskFlow;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.LinearWorkflowService;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.workflow.WorkflowTask;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.UUID;

@Component
public class ReferralTaskStatusCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskStatusCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final TaskStatusRepository taskStatusRepository;

    @NonNull
    private final UserRepository userRepository;

    @Autowired
    public ReferralTaskStatusCommandHandler(
            @NonNull ObjectMapper objectMapper,
            @NonNull WorkflowTaskController workflowTaskController,
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull TaskStatusRepository taskStatusRepository,
            @NonNull UserRepository userRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskStatusCommandViewModel.class);
        this.taskStatusRepository = taskStatusRepository;
        this.userRepository = userRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params, ReferralTaskStatusCommandViewModel vm) {
        Assert.state(vm.serviceRecipientId.equals(params.serviceRecipientId), "serviceRecipientId mismatch");

        switch (vm.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                this.createNewTaskStatus(vm);
                break;

            case BaseCommandViewModel.OPERATION_UPDATE:
                this.updateTaskStatus(vm);
                break;

            case BaseCommandViewModel.OPERATION_REMOVE:
                this.deleteTaskStatus(vm);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + vm.operation);
        }

        return null;
    }

    @Override
    protected boolean canCompleteWorkflowTaskIfPossible() {
        // don't complete the workflow task when we are just updating the task itself
        return false;
    }

    private void createNewTaskStatus(ReferralTaskStatusCommandViewModel vm) {
        var instanceUuid = vm.taskInstanceId != null ? vm.taskInstanceId : UUID.randomUUID().toString();
        TaskStatus taskStatus = new TaskStatus(instanceUuid, vm.serviceRecipientId);
        if (vm.taskHandle != null) {
            taskStatus.setTaskDefinitionId(LinearWorkflowService.getTaskDefId(Objects.requireNonNull(WorkflowTask.Handle.fromString(vm.taskHandle))));
        }
        applyChanges(vm, taskStatus);
        entityManager.persist(taskStatus);
    }

    private void updateTaskStatus(ReferralTaskStatusCommandViewModel vm) {
        // if we don't have an instance then we are updating a legacy file that indicates a task is ready,
        // but has no taskStatus to edit, so we create it first
        // NB we could potentially get the client to use 'add' but this doesn't match with the ui which hints at an update.
        TaskStatus taskStatus;
        if (vm.taskInstanceId == null) {
            createNewTaskStatus(vm);
            return;
        } else {
            taskStatus = taskStatusRepository.findById(UUID.fromString(vm.taskInstanceId)).orElseThrow(NullPointerException::new);
            applyChanges(vm, taskStatus);
            removeTasksDueAfter(vm, taskStatus);
        }

        Assert.state(taskStatus.getServiceRecipientId() == vm.serviceRecipientId, "serviceRecipientId mismatch");

        // see commit and TaskForm to understand this, but basically we allow to undo the completed status
        // (not the completedStatus reason) in order to allow the user to re-complete the task
        if (taskStatus.getCompleted() != null && vm.completed != null && vm.completed.to == null) {
            taskStatus.setCompleted(null);
        }

        // NB consider removing this since we call completeWorkflowTask below, which repeats this - at least for linear tasks
        if (vm.completed != null && vm.completed.to != null) {
            taskStatus.setCompleted(vm.completed.to.toInstant(ZoneOffset.UTC));
        }
        taskStatusRepository.save(taskStatus);

        if (vm.completed != null && vm.completed.to != null && vm.taskHandle != null) {
            this.workflowTaskController.completeWorkflowTask(vm.taskHandle);
        }
    }

    /**
     * When a manager sets a due date on a workflow task, it must be to set the next one, so lets clear anything after it
     * because it's going to be dodgy data that has built up.
     * This may introduce an error - but at least it's a user error, not a system one, and it's only a timer that's gone.
     */
    private void removeTasksDueAfter(ReferralTaskStatusCommandViewModel vm, TaskStatus taskStatus) {
        // only for workflow tasks - leave ad-hoc
        if (taskStatus.getTaskDefinitionId() != null && vm.dueDate != null && vm.dueDate.to != null) {
            var now = LocalDateTime.now(ZoneId.of("UTC"));
            var dueDateResetAfter = vm.dueDate.to.isAfter(now) ? vm.dueDate.to : now;
            taskStatusRepository.findAllByServiceRecipientIdAndTaskDefinitionId(taskStatus.getServiceRecipientId(), taskStatus.getTaskDefinitionId()).stream()
                    .filter(t -> t.getDueDate() != null)
                    .filter(t -> t.getDueDate().isAfter(dueDateResetAfter))
                    .forEach(t -> taskStatusRepository.deleteById(t.getId()));
        }
    }

    private void deleteTaskStatus(ReferralTaskStatusCommandViewModel vm) {
        TaskStatus taskStatus = taskStatusRepository.findById(UUID.fromString(vm.taskInstanceId)).orElseThrow(NullPointerException::new);
        Assert.state(taskStatus.getServiceRecipientId() == vm.serviceRecipientId, "serviceRecipientId mismatch");

        taskStatusRepository.deleteById(taskStatus.getId());
    }

    private void applyChanges(ReferralTaskStatusCommandViewModel vm, TaskStatus taskStatus) {
        if (vm.description != null) {
            taskStatus.setDescription(vm.description.to);
        }

        if (vm.dueDate != null) {
            taskStatus.setDueDate(vm.dueDate.to);
        }

        if (vm.assignee != null) {
            taskStatus.setAssignedUser(userRepository.getByUsername(vm.assignee.to));
        }

        if (vm.relevantGroup != null) {
            // TODO
        }

    }

}
