package com.ecco.webApi.taskFlow;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.hr.Worker;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class StaffTaskStaffDetailCommandHandler
        extends ServiceRecipientTaskCommandHandler<StaffTaskStaffDetailCommandViewModel> {

    @NonNull
    private final WorkerRepository workerRepository;
    @NonNull
    private final WorkerJobRepository workerJobRepository;

    @NonNull
    private final TaskClientDetailAbstractCommandHandler detailsHandler;

    @Autowired
    public StaffTaskStaffDetailCommandHandler(ObjectMapper objectMapper,
                                              @NonNull WorkflowTaskController workflowTaskController,
                                              @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                              @NonNull WorkerRepository workerRepository,
                                              @NonNull WorkerJobRepository workerJobRepository,
                                              @NonNull TaskClientDetailAbstractCommandHandler detailsHandler) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, StaffTaskStaffDetailCommandViewModel.class);

        this.workerRepository = workerRepository;
        this.workerJobRepository = workerJobRepository;
        this.detailsHandler = detailsHandler;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               StaffTaskStaffDetailCommandViewModel vm) {

        Worker c = workerJobRepository.findByServiceRecipient_Id(params.serviceRecipientId).get().getWorker();

        this.detailsHandler.handleAbstractDetails(c, vm);

        return null;
    }
}
