package com.ecco.webApi.taskFlow;

import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Map.Entry;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Project;
import com.ecco.dom.Referral;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dto.ChangeViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ReferralTaskEditDetailsCommandHandler
        extends ServiceRecipientTaskCommandHandler<ReferralTaskEditDetailsCommandViewModel> {

    private final ServiceCategorisationRepository serviceCategorisationRepository;
    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ReferralRepository referralRepository;

    @Autowired
    public ReferralTaskEditDetailsCommandHandler(ObjectMapper objectMapper,
                                                 @NonNull WorkflowTaskController workflowTaskController,
                                                 ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                 ReferralRepository referralRepository, ServiceCategorisationRepository serviceCategorisationRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditDetailsCommandViewModel.class);

        this.referralRepository = referralRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskEditDetailsCommandViewModel vm) {

        // BEWARE: don't call any other ReferralRepository methods - update r and then save
        // We'd made this mistake on daysAttending
        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        if (vm.daysAttendingChange != null) {
            DaysOfWeek days = DaysOfWeek.fromBits(vm.daysAttendingChange.to);
            r.setMeetingDays(days);
        }

        if (vm.projectChange != null) {
            Project project = vm.projectChange.to == null ? null
                    : entityManager.getReference(Project.class, vm.projectChange.to);
            var svcCat = serviceCategorisationRepository.findOneByService_IdAndProject_Id(r.getServiceRecipient().getServiceAllocation().getServiceId(), project.getId());
            r.setServiceAllocation(svcCat);
        }

        if (vm.choicesMapChanges != null) {
            for (Entry<String, ChangeViewModel<Integer>> entry : vm.choicesMapChanges.entrySet()) {
                r.getChoicesMap().put(entry.getKey(), entry.getValue().to);
            }
        }

        if (vm.exitedDateChange != null) {
            r.setExited(vm.exitedDateChange.to == null ? null : vm.exitedDateChange.to.toDateTimeAtStartOfDay());
        }

        if (vm.isPrimaryChildReferralChange != null) {
            r.setIsPrimaryChildReferral(vm.isPrimaryChildReferralChange.to);
        }

        if (vm.receivingServiceDateChange != null) {
            boolean setStartDate = vm.receivingServiceDateChange.to != null;
            r.setReceivingServiceDate(setStartDate ? vm.receivingServiceDateChange.to.toDateTimeAtStartOfDay()
                : null);
            // Set/reset these when using this approach
            r.setAcceptedReferral(setStartDate);
        }

        if (vm.textMapChanges != null) {
            for (Entry<String, ChangeViewModel<String>> entry : vm.textMapChanges.entrySet()) {
                r.getTextMap().put(entry.getKey(), entry.getValue().to);
            }
        }
        return null;
    }
}
