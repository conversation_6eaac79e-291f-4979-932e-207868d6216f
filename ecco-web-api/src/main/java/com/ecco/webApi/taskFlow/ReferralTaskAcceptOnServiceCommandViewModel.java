package com.ecco.webApi.taskFlow;


public class ReferralTaskAcceptOnServiceCommandViewModel extends ReferralTaskBaseAcceptCommandViewModel {

    static String TASK_ACCEPTONSERVICE = "decideFinal";

    /** For Jackson etc */
    @Deprecated
    ReferralTaskAcceptOnServiceCommandViewModel() {
        super();
    }

    public ReferralTaskAcceptOnServiceCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_ACCEPTONSERVICE, taskHandle);
    }

    @Override
    public String toString() {
        return "ReferralTaskAcceptOnServiceCommandViewModel [acceptedDateChange=" + acceptedDate
                + ", taskName=" + taskName + ", serviceRecipientId=" + serviceRecipientId
                + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp + "]";
    }

}
