package com.ecco.webApi.tasks;

import com.ecco.calendar.core.CalendarEntries;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.messaging.EmailService;
import com.ecco.security.dom.Group;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.messaging.CalendarNotificationAgent;
import com.ecco.webApi.viewModels.Result;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;


@RestController
@RequestMapping("/email-event")
@Slf4j
public class EmailEventController {

    private final UserRepository userRepository;
    private final CachedAclVisibilityService aclService;
    private final CalendarService calendarService;
    private final SoftwareFeatureService featureService;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    protected final EmailService emailService;

    @Value("${enableEmailEvent:false}")
    private boolean enableEmailEvent;

    @Autowired
    public EmailEventController(@Nonnull UserRepository userRepository,
                                @Nonnull CachedAclVisibilityService aclService,
                                @Nonnull CalendarService calendarService,
                                @Nonnull SoftwareFeatureService featureService,
                                @Nonnull RepositoryBasedServiceCategorisationService serviceCategorisationService,
                                @Nonnull EmailService emailService) {
        this.emailService = emailService;
        this.userRepository = userRepository;
        this.aclService = aclService;
        this.calendarService = calendarService;
        this.featureService = featureService;
        this.serviceCategorisationService = serviceCategorisationService;
    }

    // runtime config changes may be possible in ScheduledTaskRegistrar
    // runs sometime between 3am and 4am on Sunday - if no provided -DemailSchedule=""
    @Scheduled(cron = "0 #{new java.util.Random().nextInt(59)} 3 * * SUN", zone = "UTC")
    public void trigger() {

        if (!featureService.featureEnabled("ecco.email")) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            //log.error("EMAIL NOT SENT - not enabled");
            return;
        }

        if (enableEmailEvent) {
            emailEvents();
        }
    }

    @GetMapping("/events/")
    public Result emailEvents() {
        findEventsAndSendEmails(null);
        return new Result("Processed all entries");
    }

    @GetMapping("/events/{userId}/")
    public Result emailEventsOfUserId(@PathVariable Long userId) {
        var user = userRepository.findById(userId).orElseThrow();
        findEventsAndSendEmails(user.buildIndividualUserSummary());
        return new Result("Processed all entries for user " + user.getUsername());
    }

    private void findEventsAndSendEmails(@Nullable IndividualUserSummary user) {
        var users = user == null ? findTopLevelStaff() : List.of(user);
        users.forEach(u -> {
            var events = findEvents(u);
            this.sendEmail(events, u.getEmail());
        });
    }

    private CalendarEntries findEvents(IndividualUserSummary user) {
        var start = DateTimeUtils.getUtcNow().withTimeAtStartOfDay();
        DateTime end = start.plusWeeks(1);

        final Interval interval = new Interval(start, end);
        return calendarService.findEntries(user.getCalendarId(), interval);
    }

    // we assume we are a larger system, so we use the cache of the RepositoryBasedServiceCategorisationService to get the svccats
    // and we get the cache of the CachedAclVisibilityService to get the users
    // then we unique the users and return them
    // NB similar to findTasksAndSendManagerEmails
    private List<IndividualUserSummary> findTopLevelStaff() {
        // if needed to collect as we go - but we instead group by serviceAllocationId
        HashMap<Integer, List<IndividualUserSummary>> usersBySvcCatId = new HashMap<>();

        var svcCats = serviceCategorisationService.getAll()
                .stream().filter(s -> s.getId() > 0)
                .toList();

        // build the users per svcCat
        svcCats.forEach(svcCat -> {
            var users = this.aclService.getUsersWithAccessToByGroups(svcCat.getServiceId(), svcCat.getProjectId(), Group.STAFF_GROUP, Group.MANAGER_GROUP, Group.SENIORMANAGER_GROUP);
            if (!usersBySvcCatId.containsKey(svcCat.getId())) {
                usersBySvcCatId.put(svcCat.getId(), users);
            }
        });

        // for each unique user out of all the svcCats
        // which basically returns all the users in the system, but using caches (not paging)
        return usersBySvcCatId.values().stream().flatMap(Collection::stream).distinct()
                .toList();
    }

    protected void sendEmail(CalendarEntries events, String email) {

        // nothing to send
        if (events.getEntries().isEmpty() || !StringUtils.hasLength(email)) {
            // ? email service for unassigned tasks
            //Service service = serviceRepository.findById(serviceId).orElseThrow(NullPointerException::new);
            //String emailAddrs = service.getParameterAsString(Service.PARAM_EMAIL);
            //Assert.hasText(emailAddrs, "You must configure an email address (email.notification) for this service");
            return;
        }

        //String body = templateService.getPopulatedTemplateWithSubstitutions("NotifyAppointments", rows);
        var body = CalendarNotificationAgent.generateEmailTemplate(events);
        log.info("EMAIL SENDING - to: {}\nSubject: Upcoming appointments\n\n{}", email, body);
        // SYNC is probably okay, its meant to be a quiet time
        emailService.sendMessage(email, "Upcoming appointments", body);
    }

}
