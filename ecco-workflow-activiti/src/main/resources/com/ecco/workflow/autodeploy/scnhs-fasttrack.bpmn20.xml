<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="scnhs.fasttrack" isExecutable="true">
        <startEvent id="theStart" name="Start Event"/>

        <sequenceFlow id="_27" sourceRef="theStart" targetRef="loadReferral"/>

        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <sequenceFlow id="sid-623E1719-4856-4A77-B105-DC26615AF2C5" sourceRef="loadReferral" targetRef="fork1"/>

        <parallelGateway id="fork1"/>

        <sequenceFlow id="_19" sourceRef="fork1" targetRef="referralSource"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin" activiti:formKey="flow:/sourceWithIndividual"/>

        <sequenceFlow id="sid-26822A52-51B4-47B2-AB98-FF67A4A0398F" sourceRef="referralSource" targetRef="join1"/>

        <sequenceFlow id="_21" sourceRef="fork1" targetRef="referralDetails"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <documentation>32</documentation>
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="nhs" writable="false"/>
            </extensionElements>
        </userTask>

        <sequenceFlow id="_63" sourceRef="referralDetails" targetRef="referralAccepted"/>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
        </userTask>

        <sequenceFlow id="_64" sourceRef="referralAccepted" targetRef="pendingStatus"/>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin" activiti:formKey="flow:/pendingStatus"/>

        <sequenceFlow id="_65" sourceRef="pendingStatus" targetRef="assessmentDate"/>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin" activiti:formKey="flow:/assessmentDate"/>

        <sequenceFlow id="_42" sourceRef="assessmentDate" targetRef="initialProcess-admin"/>

        <userTask id="initialProcess-admin" name="initial process (admins)" activiti:candidateGroups="admin"
                activiti:formKey="generic:/initialProcess" activiti:dueDate="${now.plusDays(14).toDate()}">
            <documentation>80</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="create,supporting docs,duty handover" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <sequenceFlow id="_10" sourceRef="initialProcess-admin" targetRef="placement-admin"/>

        <userTask id="placement-admin" name="placement (admin)" activiti:candidateGroups="admin" activiti:formKey="generic:/placement-admin">
            <documentation>84</documentation>
            <extensionElements>
                <activiti:formProperty id="overviewLinks" expression="changes" writable="false"/>
                <activiti:formProperty id="outcomes" expression="placement (admin)" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar,access,overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <sequenceFlow id="_31" sourceRef="placement-admin" targetRef="startService"/>

        <userTask id="startService" name="start" activiti:candidateGroups="admin" activiti:formKey="flow:/start"/>

        <sequenceFlow id="_45" sourceRef="startService" targetRef="funding"/>

        <userTask id="funding" name="funding" activiti:candidateGroups="admin" activiti:formKey="flow:/funding"/>

        <sequenceFlow id="_47" sourceRef="funding" targetRef="scheduleReviews"/>

        <userTask id="scheduleReviews" name="scheduleReviews" activiti:candidateGroups="admin" activiti:formKey="flow:/scheduleReviews">
            <extensionElements>
                <activiti:formProperty id="reviewSchedule" expression="3m" writable="false"/>
            </extensionElements>
        </userTask>

        <sequenceFlow id="sid-E7B8F382-BBCF-4F15-B08B-68FEFAFCC861" sourceRef="scheduleReviews" targetRef="closeReferral"/>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="allocations" activiti:formKey="flow:/close"/>

        <sequenceFlow id="sid-0F2A248A-FEED-487A-B34E-C3AEEC40714D" sourceRef="closeReferral" targetRef="join1"/>

        <parallelGateway id="join1"/>

        <sequenceFlow id="sid-57A6EADD-C60E-452C-9588-9CCCC0FFAD7F" sourceRef="join1" targetRef="theEnd"/>
        <endEvent id="theEnd" name="End Event"/>
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_scnhs.fasttrack">
        <bpmndi:BPMNPlane bpmnElement="scnhs.fasttrack" id="BPMNPlane_scnhs.fasttrack">
            <bpmndi:BPMNShape bpmnElement="theStart" id="BPMNShape__12">
                <omgdc:Bounds height="30.0" width="30.0" x="30.0" y="26.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="initialProcess-admin" id="BPMNShape__3">
                <omgdc:Bounds height="55.0" width="136.0" x="72.0" y="255.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="placement-admin" id="BPMNShape__24">
                <omgdc:Bounds height="55.0" width="137.0" x="735.0" y="405.5"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="startService" id="BPMNShape__30">
                <omgdc:Bounds height="55.0" width="120.0" x="900.0" y="405.5"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="theEnd" id="BPMNShape__38">
                <omgdc:Bounds height="28.0" width="28.0" x="1380.0" y="27.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="referralDetails" id="BPMNShape__29">
                <omgdc:Bounds height="55.0" width="85.0" x="97.5" y="165.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="referralAccepted" id="BPMNShape__34">
                <omgdc:Bounds height="55.0" width="85.0" x="210.0" y="165.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="pendingStatus" id="BPMNShape__36">
                <omgdc:Bounds height="55.0" width="85.0" x="328.75" y="165.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="assessmentDate" id="BPMNShape__40">
                <omgdc:Bounds height="55.0" width="85.0" x="450.0" y="165.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="funding" id="BPMNShape__44">
                <omgdc:Bounds height="55.0" width="85.0" x="1057.5" y="405.5"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="scheduleReviews" id="BPMNShape__46">
                <omgdc:Bounds height="56.0" width="131.0" x="1170.0" y="405.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="closeReferral" id="BPMNShape__48">
                <omgdc:Bounds height="55.0" width="85.0" x="1320.0" y="330.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="referralSource" id="BPMNShape__4">
                <omgdc:Bounds height="55.0" width="85.0" x="328.75" y="13.5"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="fork1" id="BPMNShape_fork1">
                <omgdc:Bounds height="40.0" width="40.0" x="232.5" y="21.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="join1" id="BPMNShape_join1">
                <omgdc:Bounds height="40.0" width="40.0" x="1305.0" y="21.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="loadReferral" id="BPMNShape_loadReferral">
                <omgdc:Bounds height="80.0" width="100.0" x="90.0" y="1.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="sid-0F2A248A-FEED-487A-B34E-C3AEEC40714D" id="BPMNEdge_sid-0F2A248A-FEED-487A-B34E-C3AEEC40714D">
                <omgdi:waypoint x="1362.5" y="330.0"/>
                <omgdi:waypoint x="1362.5" y="195.5"/>
                <omgdi:waypoint x="1325.0" y="195.5"/>
                <omgdi:waypoint x="1325.0" y="61.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-E7B8F382-BBCF-4F15-B08B-68FEFAFCC861" id="BPMNEdge_sid-E7B8F382-BBCF-4F15-B08B-68FEFAFCC861">
                <omgdi:waypoint x="1235.5" y="405.0"/>
                <omgdi:waypoint x="1235.5" y="357.5"/>
                <omgdi:waypoint x="1320.0" y="357.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_27" id="BPMNEdge__27">
                <omgdi:waypoint x="60.0" y="41.0"/>
                <omgdi:waypoint x="90.0" y="41.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_63" id="BPMNEdge__63">
                <omgdi:waypoint x="182.5" y="192.5"/>
                <omgdi:waypoint x="210.0" y="192.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19">
                <omgdi:waypoint x="272.5" y="41.0"/>
                <omgdi:waypoint x="328.75" y="41.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_64" id="BPMNEdge__64">
                <omgdi:waypoint x="295.0" y="192.5"/>
                <omgdi:waypoint x="328.75" y="192.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_65" id="BPMNEdge__65">
                <omgdi:waypoint x="413.75" y="192.5"/>
                <omgdi:waypoint x="450.0" y="192.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_42" id="BPMNEdge__42">
                <omgdi:waypoint x="492.5" y="220.0"/>
                <omgdi:waypoint x="492.5" y="282.5"/>
                <omgdi:waypoint x="208.0" y="282.5"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_31" id="BPMNEdge__31">
                <omgdi:waypoint x="872.0" y="433.0"/>
                <omgdi:waypoint x="900.0" y="433.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_21" id="BPMNEdge__21">
                <omgdi:waypoint x="252.5" y="61.0"/>
                <omgdi:waypoint x="252.5" y="122.5"/>
                <omgdi:waypoint x="140.0" y="122.5"/>
                <omgdi:waypoint x="140.0" y="165.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_47" id="BPMNEdge__47">
                <omgdi:waypoint x="1142.5" y="433.0"/>
                <omgdi:waypoint x="1170.0" y="433.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-57A6EADD-C60E-452C-9588-9CCCC0FFAD7F" id="BPMNEdge_sid-57A6EADD-C60E-452C-9588-9CCCC0FFAD7F">
                <omgdi:waypoint x="1345.0" y="41.0"/>
                <omgdi:waypoint x="1380.0" y="41.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-26822A52-51B4-47B2-AB98-FF67A4A0398F" id="BPMNEdge_sid-26822A52-51B4-47B2-AB98-FF67A4A0398F">
                <omgdi:waypoint x="413.75" y="41.0"/>
                <omgdi:waypoint x="1305.0" y="41.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_45" id="BPMNEdge__45">
                <omgdi:waypoint x="1020.0" y="433.0"/>
                <omgdi:waypoint x="1057.5" y="433.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_10" id="BPMNEdge__10">
                <omgdi:waypoint x="140.0" y="310.0"/>
                <omgdi:waypoint x="140.0" y="555.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-623E1719-4856-4A77-B105-DC26615AF2C5" id="BPMNEdge_sid-623E1719-4856-4A77-B105-DC26615AF2C5">
                <omgdi:waypoint x="190.0" y="41.22123893805309"/>
                <omgdi:waypoint x="232.9111111111111" y="41.41111111111111"/>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>