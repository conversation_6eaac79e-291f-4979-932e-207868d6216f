package com.ecco.workflow.activiti;

import com.ecco.workflow.WorkflowLinkedResource;
import com.google.common.collect.Lists;

import org.activiti.engine.form.FormProperty;
import org.activiti.engine.form.TaskFormData;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ecco.workflow.WorkflowInstance;
import com.ecco.workflow.WorkflowTask;
import com.ecco.workflow.WorkflowTaskDefinition;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.jspecify.annotations.Nullable;

/**
 * Provides methods for constructing an implementation URI for the task from the
 * form key and any form properties, and for obtaining the opaque handle of a
 * task.
 */
@SuppressWarnings("serial")
public abstract class ActivitiWorkflowTask implements WorkflowTask {
    protected final transient Logger log = LoggerFactory.getLogger(getClass());
    private final transient AttachmentDescriptionFormatter descriptionFormatter = new AttachmentDescriptionFormatter();

    private final String processInstanceId;
    private final String processInstanceKey;
    private final String processDefinitionId;
    private final String taskFormKey;
    private final String taskName;
    private final String taskInstanceId;
    private final String taskDefinitionKey;
    private final List<FormProperty> formProperties;
    private final String assignee;
    private final LocalDateTime dueDate;
    private final List<Attachment> taskAttachments;

    ActivitiWorkflowTask(HistoricProcessInstance process, HistoricTaskInstance task,
                         TaskFormData taskFormData, String assignee, LocalDateTime dueDate, List<Attachment> taskAttachments) {
        this(process.getId(), process.getBusinessKey(), process.getProcessDefinitionId(),
                task.getId(), task.getName(), task.getTaskDefinitionKey(), taskFormData,
                assignee, dueDate, taskAttachments);
    }

    ActivitiWorkflowTask(@Nullable ProcessInstance process, String taskInstanceId, String taskName, String taskDefinitionKey,
                         TaskFormData taskFormData, String assignee, LocalDateTime dueDate, List<Attachment> taskAttachments) {
        this(process == null? null : process.getId(), process == null? null : process.getBusinessKey(), process == null? null : process.getProcessDefinitionId(),
                taskInstanceId, taskName, taskDefinitionKey, taskFormData, assignee, dueDate, taskAttachments);
    }

    private ActivitiWorkflowTask(@Nullable String processInstanceId, @Nullable String processInstanceKey, @Nullable String processDefinitionId,
                                 String taskInstanceId, String taskName, String taskDefinitionKey, TaskFormData taskFormData,
                                 String assignee, LocalDateTime dueDate, List<Attachment> taskAttachments) {
        this.processInstanceId = processInstanceId;
        this.processInstanceKey = processInstanceKey;
        this.processDefinitionId = processDefinitionId;
        this.taskDefinitionKey = taskDefinitionKey;
        this.taskInstanceId = taskInstanceId;
        this.taskName = taskName;
        if (taskFormData != null) {
            this.taskFormKey = taskFormData.getFormKey();
            this.formProperties = taskFormData.getFormProperties();
        } else {
            this.taskFormKey = null;
            this.formProperties = null;
        }
        this.assignee = assignee;
        this.dueDate = dueDate;
        this.taskAttachments = taskAttachments;
    }

    @Override
    public Handle getHandle() {
        return Handle.fromString(getTaskInstanceId());
    }

    @Override
    public WorkflowTaskDefinition.TaskDefinitionHandle getTaskDefinitionHandle() {
        if (processDefinitionId != null && taskDefinitionKey != null) {
            return WorkflowTaskDefinition.TaskDefinitionHandle.from(processDefinitionId, taskDefinitionKey);
        } else {
            return null;
        }
    }

    @Override
    public WorkflowInstance getWorkflowInstance() {
        if (processInstanceId != null && processInstanceKey != null) {
            return new ActivitiWorkflowInstance(processInstanceId, processInstanceKey);
        } else {
            return null;
        }
    }

    @Override
    public String getName() {
        return taskName;
    }

    @Override
    public String getAssignee() {
        return assignee;
    }

    @Override
    public Optional<LocalDateTime> getDueDate() {
        return dueDate == null ? Optional.empty() : Optional.of(dueDate);
    }

    @Override
    public URI getImplementationUri() {
        return ImplementationURI.from(taskFormKey, formProperties);
    }

    String getTaskInstanceId() {
        return taskInstanceId;
    }

    @Override
    public Collection<WorkflowLinkedResource> getLinkedResources() {
        return Lists.transform(taskAttachments, attachment -> {
            final WorkflowLinkedResource.Builder builder = WorkflowLinkedResource.BuilderFactory.create()
                    .title(attachment.getName())
                    .mediaType(attachment.getType())
                    .uri(URI.create(attachment.getUrl()));
            descriptionFormatter.parseDescription(attachment.getDescription(), builder);
            return builder.build();
        });
    }

    /** Extract the task id from the handle - this mirrors {@link #getHandle()} */
    static String getIdFromHandle(Handle handle) {
        return handle.toString();
    }
}
