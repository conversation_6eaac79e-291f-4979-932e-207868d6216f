package com.ecco.webApi.calendar;

import com.ecco.calendar.CombinedEntry;
import com.ecco.calendar.EventDecorator;
import com.ecco.calendar.dom.EventType;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.CustomEventImpl;
import com.ecco.dom.CustomEventRecurringImpl;
import com.ecco.dom.CustomEventWithContact;
import com.ecco.dom.CustomEventWithServiceRecipient;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.contacts.AddressLike;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.hibernate.EntityUriMapper.EntityComponents;
import com.ecco.infrastructure.web.UriUtils;
import com.ecco.security.SecurityUtil;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.ecco.calendar.core.webapi.EventResource;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.web.util.UriTemplate;

import org.jspecify.annotations.Nullable;
import java.net.URI;

/**
 * Decorates the EventResource hateoas links based on its uri information.
 * Also populates the eventResource.serviceRecipientId for where available
 * (NB this is populated for local ecco events by EventResourceAssembler, even for recurring events
 * but the rota is not part of this - see CosmoCalendarService.createRecurringEntryInternal)
 */
@AllArgsConstructor
public class ServiceRecipientEventDecorator implements EventDecorator {
    private static final Logger log = LoggerFactory.getLogger(ServiceRecipientEventDecorator.class);
    private static final String REL_EDIT_FORM = "edit-adhoc";
    private static final String REL_EDIT = "edit";
    private static final String REL_VISIT = "visit";

    private final EntityUriMapper entityUriMapper;
    private final ReferralRepository referralRepository;
    private final ServiceRecipientRepository serviceRecipientRepository;
    private final ServiceRecipientSummaryService serviceRecipientSummaryService;
    private final SoftwareFeatureService featureService;
    private final ServiceRecipientRotaDecorator<EventResource> rotaDecorator;
    protected final ApplicationProperties appConfig;

    /**
     * Decorate EventResource with various conditional or implementation specific properties such as links and location
     */
    @Override
    public void decorate(EventResource resource, CombinedEntry entry) {

        // need this before call decorate as we override the location for recurring links (only rota ones)
        // NB a recurring standard event doesn't set a location anyway (doesn't need to - just 'project calendar')
        //      - see CustomEventRecurringImpl.getLocationContact - its null
        // although this could give false confidence if some recurring locations are not overridden
        // NB Also used in CosmoConverter
        if (!featureService.featureEnabled(SoftwareFeatureService.CALENDAR_SUPPRESS_LOCATION)) {
            resource.setLocation(entry.getIcalEntry().getLocation());
        }

        EntityComponents components = getEntityComponents(entityUriMapper, entry.getIcalEntry().getManagedByUri());
        decorate(resource, components);
    }

    public static EntityComponents getEntityComponents(EntityUriMapper entityUriMapper, URI uri) {
        EntityComponents components = null;
        if (uri != null) {
            try {
                components = entityUriMapper.componentsForEntity(uri);
            } catch (IllegalArgumentException e) {
                // see if below
            }
            if (components == null || components.getEntityClass() == null) {
                log.warn("Failed to lookup entity class for " + uri);
            }
        }
        return components;
    }

    private void decorate(EventResource resource, EntityComponents managedBy) {

        if (SecurityUtil.authenticatedUserExists() && SecurityUtil.getAuthenticatedUser().hasAuthority("ROLE_STAFF")) {

            if (isAdHocCalendarEntry(resource, managedBy)) {
                // 'edit-adhoc' link - popup the edit form
                addEditAdHocLink(resource);
                // if we have an event category - open up an evidence screen
                addEvidenceLink(resource, managedBy);
            } else {
                // open referral (if managed by CustomEventWithSR for 'Interview' etc)
                // rota visit (if managed by demand schedule)
                addEditManagedLink(resource, managedBy);
            }
        }
    }

    private void addEvidenceLink(EventResource resource, EntityComponents components) {
        // we just assume at the moment that a category does link to evidence (until its clear what counts as evidence)
        // but it might be better decorated as a 'ManagedBy' from EventServiceImpl - which has access to EventEntry.
        if (resource.getEventCategoryId() != null) {
            // open referral (if managed by CustomEventWithSR)
            final int srId = resource.getServiceRecipientId() != null
                ? resource.getServiceRecipientId()
                // could this be needed for older historical recurring events?
                // but the srId should be there since CustomEventWithServiceRecipient and CustomEventRecurringImpl both use srId
                : CustomEventWithServiceRecipient.class.equals(components.getEntityClass())
                        ? serviceRecipientRepository.findServiceRecipientIdForCustomEvent(components.getId())
                        : serviceRecipientRepository.findServiceRecipientIdForCustomRecurringEvent(components.getId()).get();

            final Long referralId = referralRepository.getReferralIdByServiceRecipientId(srId);
            if (referralId != null) {

                var sr = this.serviceRecipientSummaryService.findOne(srId);
                var location = getAppointmentAddress(sr);
                resource.setLocation(location != null ? location.toCommaSepString() : null);

                resource.add(linkToNeedsAssessmentReduction(referralId).withRel(REL_VISIT));
                // don't need to link to file?
                //resource.add(linkToReferralOverview(referralId, false).withRel(REL_EDIT));
            }
        }
    }

    private void addEditAdHocLink(EventResource resource) {
        // Only ad-hoc events should have this edit form associated with them.
        if (canEditDeleteAdHocCalendarEntry(resource)) {
            LinkBuilder link = linkToLegacyEventForm(resource);
            if (link != null) {
                resource.add(link.withRel(REL_EDIT_FORM));
            }
        }
    }

    private boolean isAdHocCalendarEntry(EventResource resource, EntityComponents managedBy) {
        boolean isBaseClass = (managedBy != null && CustomEventImpl.class.equals(managedBy.getEntityClass()));
        // we now include CustomEventWithServiceRecipient, which until now redirected the link
        // so we ensure only the type 'Other' is classed as ad-hoc
        boolean isSrClass = (managedBy != null && CustomEventWithServiceRecipient.class.equals(managedBy.getEntityClass()));
        boolean isContactClass = (managedBy != null && CustomEventWithContact.class.equals(managedBy.getEntityClass()));
        boolean isBaseRecurringClass = (managedBy != null && CustomEventRecurringImpl.class.equals(managedBy.getEntityClass()));
        // 'Other' is an important distinguisher between ad-hoc entries, and 'Interview' etc (which are generated, not ad-hoc)
        // but we could just check for isGenerated - and put an appropriate ManagedBy to distinguish in EventServiceImpl
        boolean isSrAdHoc = (isSrClass || isBaseRecurringClass) && (resource.getEventType() == null || EventType.Other.equals(resource.getEventType()));
        return isBaseClass || isSrAdHoc || isContactClass;
    }

    private boolean canEditDeleteAdHocCalendarEntry(EventResource resource) {
        // if we are attached to something then we are managing in the system so open up to all (except clients?)
        // NB a local EventEntry does populate the serviceRecipientId prior to this decorator (see EventResourceAssembler)
        if (resource.getServiceRecipientId() != null &&
                !SecurityUtil.getAuthenticatedUser().hasAuthority("ROLE_CLIENT")) {
            return true;
        }
        String calendarId = SecurityUtil.getAuthenticatedUserCalendarId();
        return resource.getOwnerCalendarId().equals(calendarId) || SecurityUtil.getAuthenticatedUser().hasAuthority("ROLE_CALENDARADMIN");
    }

    private void addEditManagedLink(EventResource resource, EntityComponents components) {
        // Referral-based tasks (eg 'Interview') should link to the referral to be dealt with there.
        if (components != null && CustomEventWithServiceRecipient.class.equals(components.getEntityClass())) {
            final int srId = resource.getServiceRecipientId() != null
                ? resource.getServiceRecipientId()
                : serviceRecipientRepository.findServiceRecipientIdForCustomEvent(components.getId());
            final Long referralId = referralRepository.getReferralIdByServiceRecipientId(srId);
            if (referralId != null) {
                resource.add(linkToReferralOverviewBySrId(srId).withRel(REL_EDIT));
            }
        // Rota events should link to the rota, and back to the referral.
        } else if (components != null && DemandSchedule.class.isAssignableFrom(components.getEntityClass())) {
            rotaEventDecorator(resource, components);
        }
    }

    /**
     * When finding appointments from a calendar (events), we want the same information as the rota appointments, where appropriate.
     * So this method should match the rota handler methods - which call findRecurrences -> rotaEventLinks (ServiceRecipientRotaDecorator, as below) -> addDemandAppointment -> RotaAppointmentViewModel.
     */
    private void rotaEventDecorator(EventResource resource, EntityComponents components) {
        var scheduleId = components.getId();

        // NB this method should perhaps include the logic underneath, but we have access to EventResource
        // although we could set some locationHref as a link and utilise that
        rotaDecorator.rotaEventLinks(resource, scheduleId, resource.getStart(), resource.getEnd());

        // for event-based APIs (ie not rota APIs) the EventResource can come with a serviceRecipientId
        // from the local ecco event, ie CustomEventWithSR
        // so using the event-based API we should also populate the serviceRecipientId
        // to easily produce commands from the event for rota events
        // NB we don't set the contactId here, as we're expecting an either-or situation of srId (client) or contactId (me/staff/other)
        Integer srId = serviceRecipientRepository.findServiceRecipientIdForDemandSchedule(scheduleId);
        resource.setServiceRecipientId(srId);

        var sr = this.serviceRecipientSummaryService.findOne(srId);
        var location = getAppointmentAddress(sr);
        resource.setServiceRecipientPrefix(sr.prefix);
        resource.setServiceAllocationId(sr.serviceAllocationId);
        resource.setLocation(location != null ? location.toCommaSepString() : null);
    }

    /**
     * DEV-2386 Determine the appointment address
     * This is the current client address, but we did code below to pull from history if we really wanted.
     */
    public static AddressLike getAppointmentAddress(ServiceRecipientSummary serviceRecipientSummary) {

        // the rota handlers set the location as the current sr address - so we do the same here
        // NB the 'decorate' method above starts by setLocation using the ical event - largely for single events
        // otherwise setLocation as before... to allow for 'interviews' which uses setLocation, but override everywhere else
        //      e.g. hr, or ReferralServiceImpl for interviews etc - see ReferralServiceImpl EventUtils createEventForIndividual
        // but this meant the original location when the schedule was created was coming through
        return serviceRecipientSummary.address;

        // LOCATION - from AddressHistory - show the sr address at time of the apt
        // we removed saving the location on recurring entries as it gets out of date
        // NB the event-based recurring entry never saved a location, so we focus on setting location for rota events
        /*String location = null;
        var asAt = JodaToJDKAdapters.localDateToJDk(start.toLocalDate());
        var history = addressHistoryRepository.findAtDateByServiceRecipientId(asAt, srId);
        if (history.isEmpty()) {
            var adr = serviceRecipientSummaryService.findOne(srId).address;
            location = adr != null ? adr.toCommaSepString() : null;
        } else {
            var adr = history.get(0).getAddressLocationId();
            location = addressRepository.findById(adr).map(AddressedLocation::toCommaSepString).orElse(null);
        }*/
    }

    private LinkBuilder linkToNeedsAssessmentReduction(long referralId) {
        // eg. http://localhost:8080/ecco-war/online/referrals/432423/tasks/needsReduction/
        return UriUtils.hostRelativeLinkBuilder()
                .slash("online").slash("referrals").slash(referralId)
                .slash("task").slash("needsAssessmentReduction");
    }

    public static LinkBuilder linkToReferralOverviewBySrId(int srId) {
        return linkToReferralOverviewBySrId(UriUtils.hostRelativeLinkBuilder(), srId);
    }

    public static LinkBuilder linkToReferralOverviewBySrId(UriUtils.ExplicitLinkBuilder builder, int srId) {
        return builder.slash("online").slash("referrals").slash("sr")
                .slash(srId+"/");
        //(supportPlanView ? "&referralTasksFilterKey='tasksToShowClientView'" : ""));
    }

    @Nullable
    private LinkBuilder linkToLegacyEventForm(EventResource resource) {
            // eg. http://localhost:8080/ecco-war/nav/secure/entity/events/editHidden.html?uid=undefined&random=1385137940220&_=1385137927188
            // we no longer user the link, but do use the precence of 'edit-adhoc', so just supply a known broken link
            UriTemplate template = new UriTemplate("/");
            return UriUtils.hostRelativeLinkBuilder()
                    .slash("dynamic").slash(template.expand("events")).slash("editHidden.html?uid=" + resource.getEntryId());
    }
}