package com.ecco.calendar.msgraph;

import com.ecco.calendar.msgraph.data.Calendar;
import com.ecco.calendar.msgraph.data.DateTimeTimeZone;
import com.ecco.calendar.msgraph.data.Event;
import com.ecco.calendar.msgraph.data.ItemBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@RestController
@Slf4j
public class MsGraphCalendarController {
    private final MsGraphCalendarService calendarService;

    @GetMapping("/graph-client")
    @ResponseBody
    // demo configuration in the yml authorization-clients: graph
    public String graphClient(@RegisteredOAuth2AuthorizedClient("graph") OAuth2AuthorizedClient client) {
        log.info("{}", client.getClientRegistration().getRegistrationId());
        return client.getPrincipalName() + ": " + client.getClientRegistration().getScopes();
    }

    @GetMapping("/admin-client")
    @ResponseBody
    // demo configuration in the yml authorization-clients: admin
    public String adminClient(@RegisteredOAuth2AuthorizedClient("admin") OAuth2AuthorizedClient client) {
        log.info("{}", client.getClientRegistration().getRegistrationId());
        return client.getPrincipalName() + ": " + client.getClientRegistration().getScopes();
    }

    @RequestMapping("/msgraph/calendars/")
    @ResponseBody
    public Response<List<Calendar>> index() throws IOException {
        var response = calendarService.listCalendars().execute();
        if (response.isSuccessful()) {
            return response.body();
        } else {
            log.error("Failed with code: {}", response.code());
            log.error("{}", response.errorBody().string());
            return response.body();
        }
    }

    @RequestMapping("/msgraph/calendarView/")
    @ResponseBody
    public Response<List<Event>> calendarView() throws IOException {
        Instant now = Instant.now();
        Instant startDateTime = now.minus(Duration.ofDays(7));
        Instant endDateTime = now.plus(Duration.ofDays(7));

        var response = calendarService
                .calendarView(startDateTime, endDateTime)
                .execute();

        if (response.isSuccessful()) {
            return response.body();
        } else {
            // TODO do something sensible here.
            throw new RuntimeException(response.errorBody().string());
        }
    }

    @RequestMapping("/msgraph/createEvent")
    public String createEvent() throws IOException {
        Event event = new Event();
        event.attendees = new ArrayList<>();
        event.body = new ItemBody();
        event.body.contentType = "Text";
        event.body.content = "Demo event created by ecco-office-365-spike.";
        event.bodyPreview = "Demo event created by ecco-office-365-spike.";
        event.categories = new ArrayList<>();
        event.start = new DateTimeTimeZone();
        event.start.dateTime = LocalDateTime.ofInstant(Instant.now().plus(Duration.ofMinutes(30)), ZoneOffset.UTC);
        event.start.timeZone = "UTC";
        event.end = new DateTimeTimeZone();
        event.end.dateTime = event.start.dateTime.plus(Duration.ofMinutes(30));
        event.end.timeZone = "UTC";
        event.subject = "ecco demo event";

        var response = calendarService
                .createEvent(event)
                .execute();

        if (response.isSuccessful()) {
            return "Created demo event at " + event.start.dateTime.toString() + ".";
        } else {
            // TODO do something sensible here.
            throw new RuntimeException(response.errorBody().string());
        }
    }
}
