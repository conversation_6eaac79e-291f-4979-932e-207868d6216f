package com.ecco.serviceConfig.viewModel;

import com.ecco.serviceConfig.dom.Action;

import org.jspecify.annotations.Nullable;
import java.util.function.Function;

public class RiskActionFromViewModel implements Function<RiskActionViewModel, Action> {

    @Override
    @Nullable
    public Action apply(@Nullable RiskActionViewModel input) {
        if (input == null) {
            throw new NullPointerException("input RiskAction must not be null");
        }

        Action a = new Action();
        a.setUuid(input.uuid);
        a.setName(input.name);
        a.setOrderby(input.orderby);

        return a;
    }

}
