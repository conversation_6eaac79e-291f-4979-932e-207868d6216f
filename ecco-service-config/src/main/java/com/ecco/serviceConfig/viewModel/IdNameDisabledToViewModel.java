package com.ecco.serviceConfig.viewModel;

import com.ecco.infrastructure.entity.IdNameDisabled;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

public final class IdNameDisabledToViewModel<T extends IdNameDisabled> implements Function<T, IdNameViewModel> {

    @Nullable
    @Override
    public IdNameViewModel apply(@Nullable T input) {
        if (input == null) {
            throw new NullPointerException("input IdNameWithCode must not be null");
        }

        IdNameViewModel vm = new IdNameViewModel();
        vm.id = input.getId() == null ? null : ((Number)input.getId()).longValue();
        vm.name = input.getName();
        vm.disabled = input.isDisabled();

        return vm;
    }

}