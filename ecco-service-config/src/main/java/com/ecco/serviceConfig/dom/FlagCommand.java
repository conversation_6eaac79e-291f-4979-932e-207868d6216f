package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Configuration command for questionnaire
 */
@Entity
@DiscriminatorValue("flag")
public class FlagCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public FlagCommand() {
        super();
    }

    public FlagCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                       long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
