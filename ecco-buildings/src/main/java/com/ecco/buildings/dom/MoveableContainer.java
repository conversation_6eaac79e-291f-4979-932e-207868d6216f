package com.ecco.buildings.dom;

import com.ecco.dom.AddressedLocation;

/**
 * A MoveableContainer may be something like a vehicle, shipping container, truck into which other containers may be
 * placed.
 * Something like Bed 1, Ward 5, St Christopher's Palliative Care may actually be a normal container as it may indicate
 * allocating the space in the ward rather than the physical bed, which may be wheeled around.
 * Similarly, a berth on a ship is fixed within the ship so will be tracked with it's parent.
 */
public class MoveableContainer extends Container<MoveableContainer> {

    private static final long serialVersionUID = 1L;

    /**
     * We've not specified serviceRecipientId on MoveableContainer but its used as an identifier
     * therefore returning getId is sufficient
     */
    @Override
    public Integer getServiceRecipientId() {
        return getId();
    }

    @Override
    public AddressedLocation getTopParentLocation() {
        return null; // TODO: sort this when we implement this. May be different e.g. GPS.
    }
}
