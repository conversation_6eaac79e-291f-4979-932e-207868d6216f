package com.ecco.security.dom;

import com.ecco.config.dom.Setting;
import com.ecco.config.service.SettingsService;
import com.ecco.dom.Individual;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;
import com.ecco.infrastructure.time.Clock;
import com.ecco.security.repositories.UserRepository;
import com.ecco.security.service.UserManagementService;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.collect.Iterables.tryFind;

@Entity
@Table(name = "users")
@Audited
@Cacheable
@Cache(usage=CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Getter
@Setter
@SuppressWarnings("deprecation")
public class User extends ConfigurableLongKeyedEntity implements UserDetails, UserSource {

    private static final long serialVersionUID = 2L;

    @PersistenceContext
    @Transient
    private transient EntityManager em;

    @Autowired
    @Transient
    private transient UserRepository userRepository;

    @Autowired
    @Transient
    private transient UserManagementService userManagementService;

    @Column(unique = true)
    @NotNull
    @NotAudited
    private String username;

    private String password;

    private boolean enabled;

    private boolean activeDirectoryUser;

    private String oAuthId;

    @Type(type = "locale")
    private Locale locale;

    @Column(name = "timezone")
    @Type(type = "com.ecco.infrastructure.hibernate.CustomHibernateDateTimeZoneUserType")
    private DateTimeZone timeZone;

    private String country;

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @NotNull
    private final DateTime registered = new DateTime(DateTimeZone.UTC).withMillisOfSecond(0); // avoid MySQL 5.6 rounding *up*!

    private String domain;

    // Refers to a Group but this is association not aggregation so avoiding proxy for eager etc
    /** Relates to a Group which if set determines the set of workers that are used for sync */
    private Long syncGroupId;

    @Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @OneToOne(mappedBy = "user",
            cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH},
            fetch = FetchType.EAGER, optional = false)
    @NotAudited
    private Individual contact;

    @Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @OneToMany(mappedBy = "member", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<GroupMember> groupMemberships;

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @NotAudited
    private DateTime lastLoggedIn;

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @NotAudited
    private DateTime lastFailedLogin;

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @NotAudited
    private DateTime lastPasswordChange;

    @Column(nullable = false)
    @NotAudited
    private int failedLoginAttempts = 0;

    /**
     * Whether the user is required to use a TOTP system
     * Potentially overridden by a global 'setting'
     */
    private boolean mfaRequired;

    /**
     * Secret, randomly generated base64, that stays with on the user's account for the TOTP system
     */
    private String mfaSecret;

    @Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @OneToMany(mappedBy = "userId", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @NotAudited
    private final List<PasswordHistory> passwordHistory = new LinkedList<>();

    @Transient
    private Set<String> transientGroups;

    @Transient
    private String newPassword;

    @Transient
    private Clock clock = Clock.DEFAULT;

    @Autowired
    @Transient
    private transient SettingsService settings;

    /** cached authorities set for performance */
    @Transient
    private transient Set<SimpleGrantedAuthority> authorities;


    public User() {
    }

    public User(Individual contact, String username) {
        this.contact = contact;
        setUsername(username);
    }

    @Override
    public User getEccoUser() {
        return this;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    public IndividualUserSummary buildIndividualUserSummary() {
        return new IndividualUserSummary(getContact().getId(), getContact().getFirstName(),
                getContact().getLastName(), this.getId(), this.getUsername(), this.getContact().getCalendarId(), this.getContact().getEmail(), this.isEnabled());
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (authorities == null) {
            authorities = new HashSet<>();
            //List<GroupMember> members = userRepository.findUserGroups(this.getId());
            if (groupMemberships != null) {
                for (GroupMember m : groupMemberships) {
                    for (GroupAuthority ga : m.getGroup().getAuthorities()) {
                        authorities.add(new SimpleGrantedAuthority(ga.getAuthority()));
                    }
                }
            }
        }
        return authorities;
    }

    public boolean hasAuthority(String authority) {
        return getAuthorities().contains(new SimpleGrantedAuthority(authority));
    }

    public Set<String> getGroups() {
        return deriveGroupsFromMemberships();
    }

    // For JSP when rendering the page - so we don't set nothing
    public void primeNewGroups() {
        this.setNewGroups(this.deriveGroupsFromMemberships());
    }

    public Set<String> deriveGroupsFromMemberships() {
        if (getGroupMemberships() == null) {
            return new HashSet<>();
        }
        return getGroupMemberships().stream().map(gm -> gm.getGroup().getName()).collect(Collectors.toSet());
    }

    /**
     * The transient transientGroups is derived from groupMemberships. If we setNewGroups (without reference to the groupMemberships) then
     * we are implying that we know better - so we explicitly clear the groupMemberships. Otherwise the ui can apply
     * setNewGroups(null) to mean no transientGroups, but then getGroups regenerates the old transientGroups.
     */
    public Set<String> getNewGroups() {
        return this.transientGroups;
//        if (this.transientGroups == null) {
//            this.transientGroups = deriveGroupsFromMemberships();
//        }
    }

    public void setNewGroups(Set<String> groups) {
        this.transientGroups = groups;
        if (groupMemberships != null) {
            this.groupMemberships.clear();
        }
    }

    public String getEmail() {
        return contact.getEmail();
    }

    public String getSaltSource() {
        DateTime now = registered;
        // the registration saving uses the utc date as the salt, and gets saved to the database
        // which uses the underlying OS to take the ms value and convert to a datetime,
        // this happens to be +1hrs at the moment, so we convert to utc just to be sure
        // because the return value is +1 in hours, but also passes the offset with it

        // ISO8601 is the international standard for data interchange
        // Chronology can be ignored as it will default to the ISOChronology
        // but we ensure we use UTC, and that our string is in the known ISO standard

        // technicall this need only be done once in CustomAcegiUserDetailsServiceDaoMix.register
        now = now.withMillisOfSecond(0);

        // but this needs to be done all the time
        DateTime saltUtc = now.withZone(DateTimeZone.UTC);
        DateTimeFormatter saltFormat = ISODateTimeFormat.dateTime();
        String salt = saltFormat.print(saltUtc);

        // we would like the salt to be less obvious than simply one column in the database,
        // because with the key iteration quite low and if someone gets the database the time is reduced,
        // however guessing the salt would take time anyway since only the ms value is stored
        // and without the application - the hacker would not know the string format used
        // so it seems pointless to simply double the salt length or something
        // and I don't want to include any other factors since database can change - ie id's or usernames!

        return salt;
    }


    // Instead of FetchType.LAZY
    @Transient
    public Group getSyncGroup() {
        return syncGroupId == null ? null : em.find(Group.class, syncGroupId);
    }

    public void failedToLogin() {
        setFailedLoginAttempts(getFailedLoginAttempts() + 1);
        setLastFailedLogin(clock.now());
    }

    public void markAsSuccessfullyLoggedIn() {
        setLastLoggedIn(clock.now());
        setFailedLoginAttempts(0);
    }

    public boolean hasFailedLoginMoreThan(Integer maxAllowed) {
        return getFailedLoginAttempts() >= maxAllowed;
    }

    public boolean needsToChangePassword() {
        int passwordValidityPeriod = numberSetting("PASSWORD_VALIDITY_PERIOD");
        return passwordValidityPeriod >= 0 && effectiveLastPasswordChangeDate().plusDays(passwordValidityPeriod).isBefore(clock.now());
    }

    private DateTime effectiveLastPasswordChangeDate() {
        return firstNonNull(lastPasswordChange, registered);
    }

    /**
     * Use setNewPassword for changing the passwords.
     * This method should only be used by userManagement services on a password change
     */
    public synchronized void encodedNewPassword(String encodedPassword) {
        if (password != null) {
            passwordHistory.add(new PasswordHistory(getId(), password));
        }
        this.password = encodedPassword;
        this.lastPasswordChange = clock.now();
        // clear the newPassword as it should be for ui-processing purposes only
        // and if we are in the same session then this can trigger a password change in userManagementService.updateUser
        this.newPassword = null;
    }

    public boolean hasPassword(String encodedPassword) {
        return password.equals(encodedPassword);
    }

    public boolean rejectsStrengthOfPassword(String newPassword) {
        return !acceptStrengthOfPassword(newPassword);
    }

    private boolean acceptStrengthOfPassword(String newPassword) {
        return longEnoughPassword(newPassword) && complexEnoughPassword(newPassword);
    }

    public boolean longEnoughPassword(String newPassword) {
        return newPassword.length() >= minPasswordLength();
    }

    public int minPasswordLength() {
        return numberSetting("MIN_LENGTH");
    }

    private boolean complexEnoughPassword(String newPassword) {
        PasswordComplexity complexity = settingFor("COMPLEXITY").getAsEnum(); //JDK8 type inference
        return complexity.isSecureEnough(newPassword);
    }


    public boolean hasPreviousPassword(String newPassword) {
        return userManagementService.passwordMatchesEncoded(newPassword, this, password) ||
                tryFind(lastNPasswords(), withPassword(newPassword)::test).isPresent();
    }

    private List<PasswordHistory> lastNPasswords() {
        int length = passwordHistory.size();
        int passwordHistoryLength = numberSetting("HISTORY");
        int fromIndex = Math.max(length - passwordHistoryLength, 0);
        return passwordHistory.subList(fromIndex, length);
    }

    private Predicate<PasswordHistory> withPassword(final String password) {
        return old -> userManagementService.passwordMatchesEncoded(password, this, old.getPassword());
    }

    public DateTime nextPermittedLoginTime() {
        int lockoutDuration = numberSetting("LOCKOUT_DURATION");
        return failedLoginTooOften() ? lastFailedLogin.plusSeconds(lockoutDuration) : clock.now();
    }

    private boolean failedLoginTooOften() {
        int toleratedFailures = numberSetting("LOGIN_FAILURES_BEFORE_LOCKOUT");
        return toleratedFailures >= 0 && failedLoginAttempts > toleratedFailures;
    }

    public void enableMfa() {
        this.mfaRequired = true;
    }

    private int numberSetting(String key) {
        return Integer.parseInt(settingFor(key).getValue());
    }

    private Setting settingFor(String key) {
        return checkNotNull(settings, "no Settings").settingFor(SettingsService.SecurityAuthnPasswd.NAMESPACE, key);
    }

    public User withClock(Clock clock) {
        this.clock = clock;
        return this;
    }

    public User withSettings(SettingsService settings) {
        this.settings = settings;
        return this;
    }

    @Override
    public String toString() {
        if (isNewEntity()) {
            return "new";
        } else {
            return getId().toString();
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof User)) {
            return false;
        }

        User user = (User) o;

        return getUsername() == null ? user.getUsername() == null : getUsername().equals(user.getUsername());
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (getUsername() != null ? getUsername().hashCode() : 0);
        return result;
    }
}
