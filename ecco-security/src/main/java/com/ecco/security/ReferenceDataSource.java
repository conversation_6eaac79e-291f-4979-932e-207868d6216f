package com.ecco.security;

import javax.servlet.http.HttpServletRequest;

import org.springframework.ui.ModelMap;

// application reference data could be achieved through an interceptor - http://developingdeveloper.wordpress.com/2008/02/28/common-reference-data-in-spring-mvc/
public interface ReferenceDataSource {

    // probably best to use a ModelMap
    // http://stackoverflow.com/questions/2902706/modelmap-usage-in-spring
    void addReferenceDataToModel(ModelMap model, HttpServletRequest request);

}
