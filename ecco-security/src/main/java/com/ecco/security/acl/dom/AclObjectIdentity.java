package com.ecco.security.acl.dom;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "acl_object_identity", uniqueConstraints = @UniqueConstraint(name = "acl_o_i_unique_class_oid", columnNames = {"object_id_class", "object_id_identity"}))
public class AclObjectIdentity extends AbstractAclEntity {
    @ManyToOne(optional = false)
    @JoinColumn(name = "object_id_class", nullable = false)
    private AclClass objectIdClass;

    @Column(name = "object_id_identity", nullable = false)
    private Long objectIdIdentity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_object")
    private AclObjectIdentity parentObject;

    @ManyToOne
    @JoinColumn(name = "owner_sid")
    private AclSecurityIdentity ownerSid;

    @Column(name = "entries_inheriting", nullable = false)
    private boolean entriesInheriting;

    // include PERSIST (and MERGE) since REMOVE is relevant only for em.remove, whereas we also want saving with orphanRemoval
    // http://stackoverflow.com/questions/1069992/jpa-entitymanager-why-use-persist-over-merge
    @OneToMany(mappedBy = "aclObjectIdentity", cascade = { CascadeType.REMOVE, CascadeType.PERSIST, CascadeType.MERGE }, orphanRemoval = true)
    @OrderBy("entryOrder ASC")
    private List<AclEntry> aclEntries = new ArrayList<>();

    public AclClass getObjectIdClass() {
        return objectIdClass;
    }

    public Long getObjectIdIdentity() {
        return objectIdIdentity;
    }

    public AclObjectIdentity getParentObject() {
        return parentObject;
    }

    public AclSecurityIdentity getOwnerSid() {
        return ownerSid;
    }

    public boolean isEntriesInheriting() {
        return entriesInheriting;
    }

    public List<AclEntry> getAclEntries() {
        return aclEntries;
    }

    public void setObjectIdClass(AclClass objectIdClass) {
        this.objectIdClass = objectIdClass;
    }

    public void setObjectIdIdentity(Long objectIdIdentity) {
        this.objectIdIdentity = objectIdIdentity;
    }

    public void setParentObject(AclObjectIdentity parentObject) {
        this.parentObject = parentObject;
    }

    public void setOwnerSid(AclSecurityIdentity ownerSid) {
        this.ownerSid = ownerSid;
    }

    public void setEntriesInheriting(boolean entriesInheriting) {
        this.entriesInheriting = entriesInheriting;
    }

    void setAclEntries(List<AclEntry> aclEntries) {
        this.aclEntries = aclEntries;
    }
}
