package com.ecco.dom;

import com.ecco.security.SecurityUtil;
import org.hibernate.envers.RevisionListener;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * A custom {@link RevisionListener} which populates the username property
 * of the {@link EccoRevision} entity via the {@link SecurityContextHolder}
 */
public class EccoRevisionListener implements RevisionListener {

    @Override
    public void newRevision(Object o) {
        EccoRevision rev = (EccoRevision) o;
        if (SecurityUtil.authenticatedUserExists())
            rev.setUsername(SecurityUtil.getAuthenticatedUsername());
    }
}
