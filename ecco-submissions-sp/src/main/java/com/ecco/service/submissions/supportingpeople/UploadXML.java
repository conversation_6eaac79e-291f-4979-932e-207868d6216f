//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.1-b02-fcs
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2010.11.02 at 02:51:40 AM GMT
//


package com.ecco.service.submissions.supportingpeople;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="APIKEY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="XMLData">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="ClientRecords" type="{https://www.spclientrecord.org.uk/webservices/}ClientRecords"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="option" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "apikey",
    "xmlData",
    "option"
})
@XmlRootElement(name = "uploadXML")
public class UploadXML {

    @XmlElement(name = "APIKEY", required = true)
    protected String apikey;
    @XmlElement(name = "XMLData", required = true)
    protected String xmlData;
    @XmlElement(required = true)
    protected String option;
    //@XmlSeeAlso(ClientRecords.class)
    //protected ClientRecords clientRecords;

    /**
     * Gets the value of the apikey property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAPIKEY() {
        return apikey;
    }

    /**
     * Sets the value of the apikey property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAPIKEY(String value) {
        this.apikey = value;
    }

    /**
     * Gets the value of the xmlData property.
     *
     * @return
     *     possible object is
     *     {@link UploadXML.XMLData }
     *
     */
    public String getXMLData() {
        return xmlData;
    }

    public String createXMLData() {
        xmlData = new String();
        return getXMLData();
    }

    /**
     * Sets the value of the xmlData property.
     *
     * @param value
     *     allowed object is
     *     {@link UploadXML.XMLData }
     *
     */
    public void setXMLData(String value) {
        this.xmlData = value;
    }

    /**
     * Gets the value of the option property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption() {
        return option;
    }

    /**
     * Sets the value of the option property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption(String value) {
        this.option = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="ClientRecords" type="{https://www.spclientrecord.org.uk/webservices/}ClientRecords"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */

    /* USE A STRING
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "clientRecords"
    })
    public static class XMLData {

        @XmlElement(name = "ClientRecords", required = true)
        protected ClientRecords clientRecords;

        **
         * Gets the value of the clientRecords property.
         *
         * @return
         *     possible object is
         *     {@link ClientRecords }
         *
         *
        public ClientRecords getClientRecords() {
            return clientRecords;
        }

        **
         * Sets the value of the clientRecords property.
         *
         * @param value
         *     allowed object is
         *     {@link ClientRecords }
         *
         *
        public void setClientRecords(ClientRecords value) {
            this.clientRecords = value;
        }

    }
    */

}
