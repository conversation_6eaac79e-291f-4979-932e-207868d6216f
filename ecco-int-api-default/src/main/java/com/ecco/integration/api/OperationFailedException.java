package com.ecco.integration.api;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value=HttpStatus.INTERNAL_SERVER_ERROR, reason="External system returned an error")
public class OperationFailedException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public OperationFailedException(String message) {
        super(message);
    }
}
