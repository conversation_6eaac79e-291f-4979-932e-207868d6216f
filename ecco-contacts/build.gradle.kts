/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation("org.springframework:spring-web")
    implementation("org.springframework:spring-jdbc")
    implementation("org.springframework.security:spring-security-core")
    implementation(project(":ecco-infrastructure"))
    implementation("org.hibernate.validator:hibernate-validator")
    implementation("com.google.guava:guava")
}

description = "ecco-contacts"
