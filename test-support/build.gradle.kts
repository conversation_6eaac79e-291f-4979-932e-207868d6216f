dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-utils"))
    api("com.h2database:h2")
    implementation("org.hibernate:hibernate-jcache")
    implementation("org.ehcache:ehcache:3.9.10")
    api("org.dbunit:dbunit:2.7.3")
    api("com.github.springtestdbunit:spring-test-dbunit:1.3.0")
    implementation("org.unitils:unitils-core:3.3")
    implementation("opensymphony:ognl:2.6.11")
    implementation("org.springframework.boot:spring-boot")
    implementation("org.springframework:spring-aspects")
    api("org.springframework:spring-test")
    implementation("org.springframework:spring-tx")
    implementation("org.springframework.security:spring-security-test")
    api("org.mockito:mockito-core")
    implementation("org.junit.jupiter:junit-jupiter-api")
    implementation("com.google.guava:guava")
}

description = "test-support"