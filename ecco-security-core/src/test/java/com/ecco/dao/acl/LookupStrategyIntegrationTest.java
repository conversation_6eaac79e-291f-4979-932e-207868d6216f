package com.ecco.dao.acl;

import static java.util.Collections.emptyList;

import com.ecco.security.config.AclConfig;
import com.ecco.config.security.EnableAclsConfig;
import com.ecco.config.security.SecurityDomainAppContextInitializer;
import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.dom.BaseEntity;
import com.ecco.security.dto.AclExtractor;

import org.apache.commons.lang3.reflect.MethodUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.acls.domain.BasePermission;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.security.acls.domain.PrincipalSid;
import org.springframework.security.acls.jdbc.LookupStrategy;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.AuditableAccessControlEntry;
import org.springframework.security.acls.model.MutableAcl;
import org.springframework.security.acls.model.NotFoundException;
import org.springframework.security.acls.model.ObjectIdentity;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.acls.model.Sid;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.sql.DataSource;

/**
 * Based on {@link org.springframework.security.acls.jdbc.BasicLookupStrategyTests}.
 *
 * @since 24/04/2013
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = SecurityDomainAppContextInitializer.class,
    classes={ InfrastructureConfig.class, AclConfig.class, EnableAclsConfig.class, LookupStrategyIntegrationTest.TestConfig.class } )
@DirtiesContext
public class LookupStrategyIntegrationTest {
    private static final Sid BEN_SID = new PrincipalSid("ben");
    private static final String TARGET_CLASS = "org.springframework.security.acls.TargetObject";

    @Autowired
    @Qualifier(value="lookupStrategy")
    private LookupStrategy strategy;

    @Autowired
    private DataSource dataSource;

    private JdbcTemplate jdbcTemplate;

    class TestEntity implements BaseEntity<Long> {

        @Override
        public boolean isNewEntity() {
            return false;
        }

        @Override
        public void setId(Long id) {
        }

        @Override
        public Integer getCollectionId() {
            return null;
        }

        @Override
        public void setCollectionId(int id) {
        }

        @Override
        public Long getId() {
            return null;
        }
    }
    @Configuration
    public static class TestConfig {
        @Bean
        public AclExtractor<TestEntity> aclExtractor() {
            return new AclExtractor<>() {
                @Override
                public List<TestEntity> listObjects() {
                    return emptyList();
                }

                @Override
                public Class<TestEntity> getClazz() {
                    return TestEntity.class;
                }
            };
        }
    }

    @Before
    public void populateDatabase() {
        jdbcTemplate = new JdbcTemplate(dataSource);
        String query = "INSERT INTO acl_sid(ID,PRINCIPAL,SID) VALUES (1,1,'ben');"
                + "INSERT INTO acl_class(ID,CLASS) VALUES (2,'" + TARGET_CLASS + "');"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (1,2,100,null,1,1);"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (2,2,101,1,1,1);"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (3,2,102,2,1,1);"
                + "INSERT INTO acl_entry(ID,ACL_OBJECT_IDENTITY,ACE_ORDER,SID,MASK,GRANTING,AUDIT_SUCCESS,AUDIT_FAILURE) VALUES (1,1,0,1,1,1,0,0);"
                + "INSERT INTO acl_entry(ID,ACL_OBJECT_IDENTITY,ACE_ORDER,SID,MASK,GRANTING,AUDIT_SUCCESS,AUDIT_FAILURE) VALUES (2,1,1,1,2,0,0,0);"
                + "INSERT INTO acl_entry(ID,ACL_OBJECT_IDENTITY,ACE_ORDER,SID,MASK,GRANTING,AUDIT_SUCCESS,AUDIT_FAILURE) VALUES (3,2,0,1,8,1,0,0);"
                + "INSERT INTO acl_entry(ID,ACL_OBJECT_IDENTITY,ACE_ORDER,SID,MASK,GRANTING,AUDIT_SUCCESS,AUDIT_FAILURE) VALUES (4,3,0,1,8,0,0,0);";
        jdbcTemplate.execute(query);
    }

    @After
    public void emptyDatabase() {
        String query = "DELETE FROM acl_entry;" + "DELETE FROM acl_object_identity WHERE ID = 7;"
                + "DELETE FROM acl_object_identity WHERE ID = 6;" + "DELETE FROM acl_object_identity WHERE ID = 5;"
                + "DELETE FROM acl_object_identity WHERE ID = 4;" + "DELETE FROM acl_object_identity WHERE ID = 3;"
                + "DELETE FROM acl_object_identity WHERE ID = 2;" + "DELETE FROM acl_object_identity WHERE ID = 1;"
                + "DELETE FROM acl_class;" + "DELETE FROM acl_sid;";
        jdbcTemplate.execute(query);
    }

    @Test
    public void testAclsRetrievalWithDefaultBatchSize() throws Exception {
        ObjectIdentity topParentOid = new ObjectIdentityImpl(TARGET_CLASS, 100L);
        ObjectIdentity middleParentOid = new ObjectIdentityImpl(TARGET_CLASS, 101L);
        // Deliberately use an integer for the child, to reproduce bug report in SEC-819
        ObjectIdentity childOid = new ObjectIdentityImpl(TARGET_CLASS, 102L);

        Map<ObjectIdentity, Acl> map = this.strategy.readAclsById(Arrays.asList(topParentOid, middleParentOid, childOid), null);
        checkEntries(topParentOid, middleParentOid, childOid, map);
    }

    @Test
    public void testAclsRetrievalFromCacheOnly() throws Exception {
        ObjectIdentity topParentOid = new ObjectIdentityImpl(TARGET_CLASS, 100L);
        ObjectIdentity middleParentOid = new ObjectIdentityImpl(TARGET_CLASS, 101L);
        ObjectIdentity childOid = new ObjectIdentityImpl(TARGET_CLASS, 102L);

        // Objects were put in cache
        strategy.readAclsById(Arrays.asList(topParentOid, middleParentOid, childOid), null);

        // Let's empty the database to force acls retrieval from cache
        emptyDatabase();
        Map<ObjectIdentity, Acl> map = this.strategy.readAclsById(Arrays.asList(topParentOid, middleParentOid, childOid), null);

        checkEntries(topParentOid, middleParentOid, childOid, map);
    }

    @Test
    public void testAclsRetrievalWithCustomBatchSize() throws Exception {
        ObjectIdentity topParentOid = new ObjectIdentityImpl(TARGET_CLASS, 100L);
        ObjectIdentity middleParentOid = new ObjectIdentityImpl(TARGET_CLASS, 101L);
        ObjectIdentity childOid = new ObjectIdentityImpl(TARGET_CLASS, 102L);

        // Set a batch size to allow multiple database queries in order to retrieve all acls
        this.setBatchSize(1);
        Map<ObjectIdentity, Acl> map = this.strategy.readAclsById(Arrays.asList(topParentOid, middleParentOid, childOid), null);
        checkEntries(topParentOid, middleParentOid, childOid, map);
    }

    @SuppressWarnings("AssertEqualsBetweenInconvertibleTypes")
    private void checkEntries(ObjectIdentity topParentOid, ObjectIdentity middleParentOid, ObjectIdentity childOid,
            Map<ObjectIdentity, Acl> map) throws Exception {
        Assert.assertEquals(3, map.size());

        MutableAcl topParent = (MutableAcl) map.get(topParentOid);
        MutableAcl middleParent = (MutableAcl) map.get(middleParentOid);
        MutableAcl child = (MutableAcl) map.get(childOid);

        // Check the retrieved versions has IDs
        Assert.assertNotNull(topParent.getId());
        Assert.assertNotNull(middleParent.getId());
        Assert.assertNotNull(child.getId());

        // Check their parents were correctly retrieved
        Assert.assertNull(topParent.getParentAcl());
        Assert.assertEquals(topParentOid, middleParent.getParentAcl().getObjectIdentity());
        Assert.assertEquals(middleParentOid, child.getParentAcl().getObjectIdentity());

        // Check their ACEs were correctly retrieved
        Assert.assertEquals(2, topParent.getEntries().size());
        Assert.assertEquals(1, middleParent.getEntries().size());
        Assert.assertEquals(1, child.getEntries().size());

        // Check object identities were correctly retrieved
        Assert.assertEquals(topParentOid, topParent.getObjectIdentity());
        Assert.assertEquals(middleParentOid, middleParent.getObjectIdentity());
        Assert.assertEquals(childOid, child.getObjectIdentity());

        // Check each entry
        Assert.assertTrue(topParent.isEntriesInheriting());
        Assert.assertEquals(topParent.getId(), 1L);
        Assert.assertEquals(topParent.getOwner(), new PrincipalSid("ben"));
        Assert.assertEquals(topParent.getEntries().get(0).getId(), 1L);
        Assert.assertEquals(topParent.getEntries().get(0).getPermission(), BasePermission.READ);
        Assert.assertEquals(topParent.getEntries().get(0).getSid(), new PrincipalSid("ben"));
        Assert.assertFalse(((AuditableAccessControlEntry) topParent.getEntries().get(0)).isAuditFailure());
        Assert.assertFalse(((AuditableAccessControlEntry) topParent.getEntries().get(0)).isAuditSuccess());
        Assert.assertTrue((topParent.getEntries().get(0)).isGranting());

        Assert.assertEquals(topParent.getEntries().get(1).getId(), 2L);
        Assert.assertEquals(topParent.getEntries().get(1).getPermission(), BasePermission.WRITE);
        Assert.assertEquals(topParent.getEntries().get(1).getSid(), new PrincipalSid("ben"));
        Assert.assertFalse(((AuditableAccessControlEntry) topParent.getEntries().get(1)).isAuditFailure());
        Assert.assertFalse(((AuditableAccessControlEntry) topParent.getEntries().get(1)).isAuditSuccess());
        Assert.assertFalse(topParent.getEntries().get(1).isGranting());

        Assert.assertTrue(middleParent.isEntriesInheriting());
        Assert.assertEquals(middleParent.getId(), 2L);
        Assert.assertEquals(middleParent.getOwner(), new PrincipalSid("ben"));
        Assert.assertEquals(middleParent.getEntries().get(0).getId(), 3L);
        Assert.assertEquals(middleParent.getEntries().get(0).getPermission(), BasePermission.DELETE);
        Assert.assertEquals(middleParent.getEntries().get(0).getSid(), new PrincipalSid("ben"));
        Assert.assertFalse(((AuditableAccessControlEntry) middleParent.getEntries().get(0)).isAuditFailure());
        Assert.assertFalse(((AuditableAccessControlEntry) middleParent.getEntries().get(0)).isAuditSuccess());
        Assert.assertTrue(middleParent.getEntries().get(0).isGranting());

        Assert.assertTrue(child.isEntriesInheriting());
        Assert.assertEquals(child.getId(), 3L);
        Assert.assertEquals(child.getOwner(), new PrincipalSid("ben"));
        Assert.assertEquals(child.getEntries().get(0).getId(), 4L);
        Assert.assertEquals(child.getEntries().get(0).getPermission(), BasePermission.DELETE);
        Assert.assertEquals(child.getEntries().get(0).getSid(), new PrincipalSid("ben"));
        Assert.assertFalse(((AuditableAccessControlEntry) child.getEntries().get(0)).isAuditFailure());
        Assert.assertFalse(((AuditableAccessControlEntry) child.getEntries().get(0)).isAuditSuccess());
        Assert.assertFalse((child.getEntries().get(0)).isGranting());
    }

    @Test
    @Transactional
    public void testAllParentsAreRetrievedWhenChildIsLoaded() throws Exception {
        String query = "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (4,2,103,1,1,1);";
        jdbcTemplate.execute(query);

        ObjectIdentity topParentOid = new ObjectIdentityImpl(TARGET_CLASS, 100L);
        ObjectIdentity middleParentOid = new ObjectIdentityImpl(TARGET_CLASS, 101L);
        ObjectIdentity childOid = new ObjectIdentityImpl(TARGET_CLASS, 102L);
        ObjectIdentity middleParent2Oid = new ObjectIdentityImpl(TARGET_CLASS, 103L);

        // Retrieve the child
        Map<ObjectIdentity, Acl> map = this.strategy.readAclsById(Arrays.asList(childOid), null);

        // Check that the child and all its parents were retrieved
        Assert.assertNotNull(map.get(childOid));
        Assert.assertEquals(childOid, map.get(childOid).getObjectIdentity());
        Assert.assertNotNull(map.get(middleParentOid));
        Assert.assertEquals(middleParentOid, map.get(middleParentOid).getObjectIdentity());
        Assert.assertNotNull(map.get(topParentOid));
        Assert.assertEquals(topParentOid, map.get(topParentOid).getObjectIdentity());

        // The second parent shouldn't have been retrieved
        Assert.assertNull(map.get(middleParent2Oid));
    }

    /**
     * Test created from SEC-590.
     */
    @Test
    @Transactional
    public void testReadAllObjectIdentitiesWhenLastElementIsAlreadyCached() throws Exception {
        String query = "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (4,2,104,null,1,1);"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (5,2,105,4,1,1);"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (6,2,106,4,1,1);"
                + "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (7,2,107,5,1,1);"
                + "INSERT INTO acl_entry(ID,ACL_OBJECT_IDENTITY,ACE_ORDER,SID,MASK,GRANTING,AUDIT_SUCCESS,AUDIT_FAILURE) VALUES (5,4,0,1,1,1,0,0)";
        jdbcTemplate.execute(query);

        ObjectIdentity grandParentOid = new ObjectIdentityImpl(TARGET_CLASS, 104L);
        ObjectIdentity parent1Oid = new ObjectIdentityImpl(TARGET_CLASS, 105L);
        ObjectIdentity parent2Oid = new ObjectIdentityImpl(TARGET_CLASS, 106L);
        ObjectIdentity childOid = new ObjectIdentityImpl(TARGET_CLASS, 107L);

        // First lookup only child, thus populating the cache with grandParent, parent1 and child
        List<Permission> checkPermission = Arrays.asList(BasePermission.READ);
        List<Sid> sids = Arrays.asList(BEN_SID);
        List<ObjectIdentity> childOids = Arrays.asList(childOid);

        setBatchSize(6);
        Map<ObjectIdentity, Acl> foundAcls = strategy.readAclsById(childOids, sids);

        Acl foundChildAcl = foundAcls.get(childOid);
        Assert.assertNotNull(foundChildAcl);
        Assert.assertTrue(foundChildAcl.isGranted(checkPermission, sids, false));

        // Search for object identities has to be done in the following order: last element have to be one which
        // is already in cache and the element before it must not be stored in cache
        List<ObjectIdentity> allOids = Arrays.asList(grandParentOid, parent1Oid, parent2Oid, childOid);
        try {
            foundAcls = strategy.readAclsById(allOids, sids);
            Assert.assertTrue(true);
        } catch (NotFoundException notExpected) {
            Assert.fail("It shouldn't have thrown NotFoundException");
        }

        Acl foundParent2Acl = foundAcls.get(parent2Oid);
        Assert.assertNotNull(foundParent2Acl);
        Assert.assertTrue(foundParent2Acl.isGranted(checkPermission, sids, false));
    }

    private void setBatchSize(final int size) {
        try {
            MethodUtils.invokeMethod(strategy, "setBatchSize", size);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

    @Test(expected=IllegalArgumentException.class)
    @Transactional
    public void nullOwnerIsNotSupported() {
        String query = "INSERT INTO acl_object_identity(ID,OBJECT_ID_CLASS,OBJECT_ID_IDENTITY,PARENT_OBJECT,OWNER_SID,ENTRIES_INHERITING) VALUES (4,2,104,null,null,1);";

        jdbcTemplate.execute(query);

        ObjectIdentity oid = new ObjectIdentityImpl(TARGET_CLASS, 104L);

        strategy.readAclsById(Arrays.asList(oid), Arrays.asList(BEN_SID));
    }

}
