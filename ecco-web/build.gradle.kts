/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

dependencies {
    implementation(project(":ecco-dom"))
    implementation(project(":ecco-security"))
    implementation(project(":ecco-service"))
    implementation(project(":ecco-service-config"))

    implementation("com.github.jknack:handlebars-springmvc:4.3.0")
    implementation("commons-fileupload:commons-fileupload")
    implementation("joda-time:joda-time:2.10.8")
    implementation("org.springframework.boot:spring-boot-autoconfigure")
    implementation("org.springframework:spring-web")
    implementation("org.springframework:spring-webmvc")
    implementation("org.springframework:spring-tx")
    implementation("org.springframework.security:spring-security-web")
    implementation("com.google.guava:guava")
}

description = "ecco-web"
