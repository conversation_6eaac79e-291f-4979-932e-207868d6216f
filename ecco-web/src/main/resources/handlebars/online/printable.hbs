{{#> partials/material-ui-page
        bootstrapRequired="false"
        calendarRequired="false"
        jqplotRequired="false"
        title="referral"
        importRequireJsModules=requireJsModules
}}
    <style>
        /* allow sensible page breaks */
        .MuiFormControl-root {
            display: block;
        }
        /* allow sensible page breaks - avoid one after h5 titles */
        .MuiBox-root-2 {
            margin-top: 0px;
            margin-bottom: 0px;
        }
        .MuiInputLabel-outlined.MuiInputLabel-shrink {
            transform: translate(14px, -6px) scale(0.95); /* make shrink text fill parent more (was .075) */
            font-size: 75%; /* make shrink text bigger, make 75% of original */
        }
        .form-footer {
            display: none;
        }
        .no-print {
            display: none;
        }
    </style>

    <div class="container text-center">

        {{! COPIED SECTION ALSO IN clean_notiles.jsp }}
        <div class="">
            <div style="margin-top: 50px;">&nbsp;</div>

            <div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">

                {{#if footerImageUrl}}
                    <img style="float: left; margin-bottom:-50px; max-height: 75px;" src="{{footerImageUrl}}" alt="${organisationName}"/>
                {{else}}
                    <img style="float: left; margin-bottom:-50px;" src="{{applicationProperties.resourceRootPath}}themes/ecco/images/logo_greyscale.png" alt="logo"/>
                {{/if}}

                <span style="float: left; text-align: left; margin-left: 140px; margin-top: 7px;">{{siteTitle}}</span>

                <div style="float: right;">
                    at {{generatedDateTime}}
                    <br/>
                    by {{loggedInUsername}}
                </div>
                <div style="clear: both;">&nbsp;</div>
            </div>
            <div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">
                <span style="font-size: 1.2em; font-weight: bold;">{{message "organisationName"}}</span>
                <br/>
            </div>
        </div>
        {{! COPIED SECTION ALSO IN clean_notiles.jsp }}

        {{> partials/referralPrintPage}}

        <div style="clear: both;">&nbsp;</div>

        <br/>

        <div style="text-align: center; margin-top: 5px; font-size: 0.9em;">
            <a href="#" class="printable-link" style="display: none;">print</a>
        </div>

    </div>


    <div id="main-content-wrapper" style="padding-left: 50px; padding-right: 50px;">
        <div class="printable" id="main-content">&nbsp;</div>
    </div>

    <div id="snackbar"></div>

    <script type="text/javascript">

        document.body.style.width = '210mm';
        // elsewhere we also use $(".container").css({'width': '210mm'});

    </script>
{{/partials/material-ui-page}}