package com.ecco.dom;

import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.Instant;

@MappedSuperclass
public abstract class BaseComment extends AbstractLongKeyedEntity implements Created {

    /** NOTE: This is defaulted to now by {@link com.ecco.infrastructure.hibernate.CreatedInterceptor} if null */
    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    private DateTime created;

    // was ContactImpl - but that caused issues instantiating an abstract class
    // an author of a comment is in fact always an individual
    // many to one since we can have many riskActionComments to one contact
    // eager for the project comment
    @ManyToOne(fetch=FetchType.EAGER) // TODO: A projection object just for first/lastname/orgName/id
    @JoinColumn(name="contactId")
    private Individual author;

    @Lob
    @Column(name = "BC_COMMENT")
    private String comment;
    private int minutesSpent = 0;

    /**
     * @deprecated may still have some use when people edit support history, but that should then become that
     * we show edit history for a piece of work via CommentCommand history or GoalUpdateCommand history
     */
    @Deprecated
    public Individual getAuthor() {
        return author;
    }

    @Override
    public DateTime getCreated() {
        return created;
    }

    public String getComment() {
        return comment;
    }
    public void setMinutesSpent(int minutesSpent) {
        this.minutesSpent = minutesSpent;
    }
    public int getMinutesSpent() {
        return minutesSpent;
    }
    @Override
    public void setCreated(DateTime created) {
        this.created = created;
    }
    public void setAuthor(Individual author) {
        this.author = author;
    }
    public void setComment(String comment) {
        this.comment = comment;
    }

    /**
     * @return The most relevant instant we can ascertain for this comment.  In some cases it will be the start time
     * of a support visit.  In other cases it may just be the instant that the end user recorded the data
     * (which should preferably be the remote creation time).
     */
    public Instant getRelevantInstant() {
        return created == null ? null : Instant.ofEpochMilli(created.toInstant().getMillis());
    }
}
