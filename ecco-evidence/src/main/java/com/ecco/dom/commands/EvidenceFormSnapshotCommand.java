package com.ecco.dom.commands;

import lombok.NoArgsConstructor;
import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Form update command which holds changes required as a JsonPatch in the body of the command,
 * which updates or creates a EvidenceSnapshotJson entry for a particular record (taskName).
 */
@Entity
@DiscriminatorValue("formUpdate")
@NoArgsConstructor
public class EvidenceFormSnapshotCommand extends ServiceRecipientEvidenceCommand {

    public EvidenceFormSnapshotCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                       long userId, @NonNull String body, int serviceRecipientId,
                                       String taskName, @NonNull String evidenceTaskGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceTaskGroupKey);
    }

}
