package com.ecco.dom.commands;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("deleteEvidence")
public class DeleteEvidenceCommand extends ServiceRecipientEvidenceCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public DeleteEvidenceCommand() {
    }

    public DeleteEvidenceCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull String body, int serviceRecipientId,
                                 @NonNull String evidenceGroupKey, @NonNull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
    }

}
