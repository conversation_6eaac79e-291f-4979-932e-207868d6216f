package com.ecco.data.client.dataimport.csv.mappers

import org.springframework.beans.NotWritablePropertyException
import org.springframework.beans.PropertyAccessorFactory
import org.springframework.core.convert.ConversionService

class CsvBeanMapper<T : Any>
@JvmOverloads
constructor(
    type: Class<T>,
    private val conversionService: ConversionService? = null,
    private val onNotWritableProperty: (
    (
        exception: NotWritablePropertyException,
        value: Any?,
    ) -> Unit
    ) = { exception, _ -> throw exception },
) : (List<Pair<String, Any?>>) -> T {
    private val ctor = type.getDeclaredConstructor().apply { isAccessible = true }

    override fun invoke(keyValues: List<Pair<String, Any?>>): T {
        val instance = ctor.newInstance()
        val wrapper = PropertyAccessorFactory.forBeanPropertyAccess(instance)
        wrapper.conversionService = conversionService
        wrapper.isAutoGrowNestedPaths = true

        keyValues
            .filter { (key, value) ->
                key != "" || value != ""
            }.forEach { (key, value) ->
                try {
                    wrapper.setPropertyValue(key, value)
                } catch (exception: NotWritablePropertyException) {
                    onNotWritableProperty(exception, value)
                }
            }

        return instance
    }
}