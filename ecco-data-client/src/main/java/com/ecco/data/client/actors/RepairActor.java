package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.dom.repairs.Repair;
import com.ecco.webApi.repairs.CreateRepairCommandViewModel;
import com.ecco.webApi.repairs.RepairController;
import com.ecco.webApi.repairs.RepairViewModel;
import com.ecco.webApi.viewModels.Result;
import org.hamcrest.Matchers;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


public class RepairActor extends BaseActor {
    private static final String repairUri = WebApiSettings.APPLICATION_URL + "/api/repairs/";

    public RepairActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /*public ResponseEntity<RepairViewModel[]> findAll() {
        return restTemplate.getForEntity(
                apiBaseUrl + "repairs/all/",
                RepairViewModel[].class);
    }*/

    public RepairViewModel getRepairById(int id) {
        String uri = repairUri + id + "/";
        ResponseEntity<RepairViewModel> response = restTemplate.getForEntity(uri, RepairViewModel.class);
        return response.getBody();
    }

    public int createRepair(String name, LocalDate received) {
        var vm = new RepairViewModel();
        //vm.setName(name);
        vm.setReceivedDate(received);

        vm.setServiceAllocationId(Repair.DEFAULT_SERVICE_ALLOCATION_ID);

        CreateRepairCommandViewModel createVm = new CreateRepairCommandViewModel(vm);
        ResponseEntity<Result> response = this.executeCommand(createVm);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()));
        Assert.state(response.getBody().getId() != null);

        return RepairController.EXTRACT_ID_FN.apply(response.getBody().getLink(SELF.value()).getHref());

    }

}
