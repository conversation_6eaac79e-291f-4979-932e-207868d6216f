package com.ecco.data.client.model;

import com.ecco.webApi.contacts.address.AddressViewModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * A building import view model.
 * We've previously attempted to import commands directly from csv files, at d619e257, but it required
 * 'public constructors and getter/setters' - although the client-side code must do the same, so probably means still valid approach
 * except that manipulating the spreadsheet into command's 'to' structure is possibly more hassle than a creating this simple view model
 * given imports tend to be simple.
 */
@Getter
@Setter
@ToString(includeFieldNames = false)
public class BuildingImportViewModel {

    public Integer id; // allow a reference to the ecco-id for updates
    public String externalRef;
    public String name;
    public Integer resourceTypeId; // building/resource etc
    public Integer parentExternalRef; // allows us to lookup a parent by code not parentId when we don't know the id
    public AddressViewModel address;

}
