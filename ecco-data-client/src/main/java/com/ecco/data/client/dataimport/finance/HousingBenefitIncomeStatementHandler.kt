package com.ecco.data.client.dataimport.finance

import com.ecco.data.client.dataimport.support.AbstractHandler
import com.ecco.data.client.dataimport.support.ImportOperation
import com.ecco.webApi.finance.HousingBenefitIncomeStatementCommandViewModel
import com.ecco.webApi.finance.HousingBenefitIncomeStatementViewModel
import org.springframework.web.client.RestTemplate

class HousingBenefitIncomeStatementHandler(restTemplate: RestTemplate) :
    AbstractHandler<HousingBenefitIncomeStatementViewModel>(restTemplate) {
    override fun processEntity(entity: ImportOperation<HousingBenefitIncomeStatementViewModel>?) {
        if (entity != null) {
            commandActor.executeCommand(HousingBenefitIncomeStatementCommandViewModel(entity.record))
        }
    }
}