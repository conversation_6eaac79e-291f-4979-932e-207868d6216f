package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.webApi.managedvoids.CreateManagedVoidCommandViewModel;
import com.ecco.webApi.managedvoids.ManagedVoidController;
import com.ecco.webApi.managedvoids.ManagedVoidViewModel;
import com.ecco.webApi.viewModels.Result;
import org.hamcrest.Matchers;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


public class ManagedVoidActor extends BaseActor {
    private static final String managedVoidUri = WebApiSettings.APPLICATION_URL + "/api/managedvoids/";

    public ManagedVoidActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /*public ResponseEntity<RepairViewModel[]> findAll() {
        return restTemplate.getForEntity(
                apiBaseUrl + "repairs/all/",
                RepairViewModel[].class);
    }*/

    public ManagedVoidViewModel getManagedVoidById(int id) {
        String uri = managedVoidUri + id + "/";
        ResponseEntity<ManagedVoidViewModel> response = restTemplate.getForEntity(uri, ManagedVoidViewModel.class);
        return response.getBody();
    }

    public int createManagedVoid(String name, LocalDate received) {
        var vm = new ManagedVoidViewModel();
        //vm.setName(name);
        vm.setReceivedDate(received);

        vm.setServiceAllocationId(ManagedVoid.DEFAULT_SERVICE_ALLOCATION_ID);

        CreateManagedVoidCommandViewModel createVm = new CreateManagedVoidCommandViewModel(vm);
        ResponseEntity<Result> response = this.executeCommand(createVm);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()));
        Assert.state(response.getBody().getId() != null);

        return ManagedVoidController.EXTRACT_ID_FN.apply(response.getBody().getLink(SELF.value()).getHref());

    }

}
