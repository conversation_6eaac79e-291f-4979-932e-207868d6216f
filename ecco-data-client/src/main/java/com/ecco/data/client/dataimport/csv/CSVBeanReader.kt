package com.ecco.data.client.dataimport.csv

import com.ecco.data.client.dataimport.csv.conversion.DataImportConversionService
import com.ecco.data.client.dataimport.csv.mappers.CsvBeanMapper
import com.ecco.data.client.dataimport.csv.mappers.CsvSynonymMapper
import com.ecco.data.client.dataimport.support.AbstractHandler
import com.ecco.data.client.dataimport.support.ImportOperation
import com.google.common.io.CharSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.NotWritablePropertyException
import java.io.IOException
import java.util.function.Function

/**
 * Iterates over a CSV text file creating beans of the given type using first row as bean property paths
 * which can act as aliases in the supplied [Map].
 *
 */
class CSVBeanReader<T : Any>(
    private val source: CharSource,
    beanType: Class<T>,
    aliases: Map<String, String>,
    synonyms: Map<String, String>,
) {
    private val log: Logger = LoggerFactory.getLogger(javaClass)

    private val onNotWritableProperty = { exception: NotWritablePropertyException, value: Any? ->
        // TODO: Allow configurable handler to append data to something like a referral comment
        System.err.println("Couldn't write field: ${exception.propertyName}")
        System.err.println("              object: ${exception.beanClass.javaClass.simpleName}")
        System.err.println("               value: $value")
        System.err.println("ensure you have getters and setters on the view model")
        log.trace("error: ", exception)
    }

    private val dataMapper = CsvBeanMapper(beanType, DataImportConversionService.getInstance(), onNotWritableProperty)
    private val aliasMapper = { keys: List<String> -> keys.map { aliases[it] ?: it } }
    private val synonymMapper = CsvSynonymMapper(synonyms)

    /**
     * Handles the file, reading batches of rows and delegating the submission to the dataHandler.
     * TODO introduce a 'batchType' parameter to determine where the batches are split
     * (but for now the whole file is assumed).
     */
    @Throws(IOException::class)
    fun readBatchUsing(dataHandler: AbstractHandler<T>) {
        source.openBufferedStream().use { reader ->
            dataHandler.apply(MappingCSVParser(dataMapper, aliasMapper, synonymMapper).parse(reader))
        }
    }

    /**
     * Handles the file, reading each row and delegating the submission to the dataHandler
     */
    @Throws(IOException::class)
    fun readUsing(dataHandler: Function<ImportOperation<T>?, Void?>) {
        source.openBufferedStream().use { reader ->
            MappingCSVParser(dataMapper, aliasMapper, synonymMapper).parse(reader).forEach {
                dataHandler.apply(it)
            }
        }
    }
}