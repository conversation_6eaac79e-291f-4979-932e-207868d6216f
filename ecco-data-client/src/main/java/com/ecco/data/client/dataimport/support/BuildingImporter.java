package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.data.client.model.BuildingImportViewModel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Map;

public class BuildingImporter {
    private final Map<String, String> aliases = ImmutableMap.<String, String>builder()
            .put("line1", "address.address[0]")
            .put("line2", "address.address[1]")
            .put("line3", "address.address[2]")
            .put("town", "address.town")
            .put("county", "address.county")
            .put("postcode", "address.postcode")
            .build();

    protected final RestTemplate restTemplate;

    public BuildingImporter(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void read(CharSource source) {
        final Map<String, String> allSynonyms = ImmutableMap.<String, String>builder().
                putAll(Synonyms.synonymsToNormalizedMap)
                .build();

        try {
            new CSVBeanReader<>(source, BuildingImportViewModel.class, aliases, allSynonyms)
                    .readUsing(new BuildingHandler(restTemplate));
        } catch (IOException e) {
            throw Throwables.propagate(e);
        }
    }
}
