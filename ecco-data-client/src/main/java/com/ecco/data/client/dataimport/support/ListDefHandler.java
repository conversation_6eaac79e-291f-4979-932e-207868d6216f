package com.ecco.data.client.dataimport.support;

import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;

import org.springframework.web.client.RestTemplate;

public class ListDefHandler extends AbstractHandler<ListDefinitionEntryViewModel> {
    private static final String apiPath = "/api/listdefinitions/";

    public ListDefHandler(RestTemplate template) {
        super(template);
    }

    @Override
    protected void processEntity(ImportOperation<ListDefinitionEntryViewModel> operation) {
        String uri = operation.baseUri + apiPath + operation.record.listName + "/?appendable=true";
        postAcceptingCreatedOrUnprocessableEntity(uri, operation.record);
    }

}
