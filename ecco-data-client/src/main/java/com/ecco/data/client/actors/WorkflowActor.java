package com.ecco.data.client.actors;

import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.WorkflowTaskViewModel;
import com.ecco.webApi.viewModels.WorkflowViewModel;
import com.google.common.collect.ImmutableMap;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.equalTo;

public class WorkflowActor extends ServiceRecipientActor {

    public WorkflowActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /** GET service-recipients/{serviceRecipientId}/workflow/ */
    public ResponseEntity<WorkflowViewModel> getTasksForRecipient(int serviceRecipientId) {
        return restTemplate.getForEntity(apiBaseUrl + "service-recipients/{serviceRecipientId}/workflow/",
                WorkflowViewModel.class, ImmutableMap.of("serviceRecipientId", serviceRecipientId));
    }

    public Map<String, WorkflowTaskViewModel> getTasksByTaskName(int serviceRecipientId) {
        WorkflowViewModel wvm = getTasksForRecipient(serviceRecipientId).getBody();
        return wvm.tasks.stream()
                .collect(Collectors.toMap(task -> task.taskName, Function.identity()));
    }

    public Map<String, WorkflowTaskViewModel> getTasksByTaskName(ReferralViewModel rvm) {
        return getTasksByTaskName(rvm.serviceRecipientId);
    }

    public ResponseEntity<String> claimTask(WorkflowTaskViewModel task) {
        Assert.notNull(task);

        ResponseEntity<String> result = restTemplate.postForEntity(task.getRequiredLink("self").getHref() + "/claimTask",
                Collections.emptyMap(),
                String.class);
        Assert.state(equalTo(HttpStatus.CREATED).matches(result.getStatusCode()));
        return result;
    }

    public ResponseEntity<String> markTaskCompleted(WorkflowTaskViewModel task) {
        ResponseEntity<String> result = restTemplate.postForEntity(task.getRequiredLink("self").getHref() + "/markCompleted",
                Collections.emptyMap(),
                String.class);
        Assert.state(equalTo(HttpStatus.CREATED).matches(result.getStatusCode()));
        return result;
    }

}
