package com.ecco.security.ldap.service;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class LdapGroupAggregate {

    @NotNull @Size(min = 3, max = 255)
    private String ldapGroup;
    @NotNull @Size(min = 1)
    private Set<String> localGroups;
    private List<LdapGroupLocalAcl> localAcls;

    public LdapGroupAggregate() {}
    public LdapGroupAggregate(String ldapGroup) {
        this.ldapGroup = ldapGroup;
        this.localGroups = new HashSet<>();
        this.localAcls = new ArrayList<>();
    }
    public void update(String localGroup, String localClass, Long localId) {
        if (StringUtils.isNotEmpty(localGroup))
            getLocalGroups().add(localGroup);
        if (StringUtils.isNotEmpty(localClass) && (localId != null))
            getLocalAcls().add(new LdapGroupLocalAcl(localId, localClass));
    }

    public String getLdapGroup() {
        return ldapGroup;
    }

    public void setLdapGroup(String ldapGroup) {
        this.ldapGroup = ldapGroup;
    }

    public Set<String> getLocalGroups() {
        return localGroups;
    }

    public void setLocalGroups(Set<String> localGroups) {
        this.localGroups = localGroups;
    }

    public List<LdapGroupLocalAcl> getLocalAcls() {
        return localAcls;
    }

    public void setLocalAcls(List<LdapGroupLocalAcl> localAcls) {
        this.localAcls = localAcls;
    }

}
