package com.ecco.calendar.cosmo;

import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.calendar.core.Attendee;
import com.ecco.calendar.core.DaysOfWeek;
import com.ecco.calendar.core.RecurringEntry;
import net.fortuna.ical4j.model.Parameter;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.Recur;
import net.fortuna.ical4j.model.WeekDay;
import net.fortuna.ical4j.model.component.VEvent;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Duration;
import org.joda.time.LocalDate;
import org.osaf.cosmo.model.BaseEventStamp;
import org.osaf.cosmo.model.EventStamp;
import org.osaf.cosmo.model.NoteItem;
import org.osaf.cosmo.model.StampUtils;
import org.springframework.util.Assert;

import java.net.URI;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Adapter between cosmo and a recurring entry interface.
 * The conversions here share similarities with Entry - as per CosmoConverter#convertItemToEntry
 *
* @since 11/09/2013
*/
public final class NoteItemToRecurringEntryAdapter implements RecurringEntry {
    private final NoteItem noteItem;
    private final EventStamp eventStamp;
    private final CosmoConverter converter;

    /**
     * Indication that this event is itself setting the schedule for recurrences.
     * @see RecurringEntry
     * Determined using BaseEventStamp#isRecurring which looks for RRULE's - and we exclude recurrences
     * Also see NoteItemRecurrenceAdapter which avoids ':" tests.
     */
    public static boolean isRecurringEntry(NoteItem noteItem) {
        Assert.isTrue(noteItem != null, "Item must not be null");
        EventStamp eventStamp = StampUtils.getEventStamp(noteItem);
        boolean recurrence = CosmoCalendarService.isRecurrenceUid(noteItem.getUid());
        return eventStamp != null && eventStamp.isRecurring() && !recurrence;
    }

    public NoteItemToRecurringEntryAdapter(NoteItem noteItem, CosmoConverter converter) {
        Assert.isTrue(noteItem != null, "Item must not be null");
        this.noteItem = noteItem;
        this.eventStamp = StampUtils.getEventStamp(noteItem);
        this.converter = converter;
        Assert.isTrue(this.eventStamp != null, "Must have an EventStamp");
        Assert.isTrue(this.eventStamp.isRecurring(), "Must be a recurring event");
    }

    @Override
    public RecurringEntryHandle getHandle() {
        return RecurringEntryHandle.fromString(noteItem.getUid());
    }

    @Override
    public String getTitle() {
        // noteItem.name is the same (see CosmoCalendarRecurringService#createRecurringEntry)
        // stamp.summary is the same
        return noteItem.getDisplayName();
    }

    @Override
    public String getDescription() {
        // stamp.description is also description
        return noteItem.getBody();
    }

    @Override
    public URI getLocationUri() {
        VEvent event = eventStamp.getEvent();
        if (event != null) {
            final Property location = event.getProperty(Property.LOCATION);
            if (location != null && location.getParameter(Parameter.ALTREP) != null) {
                return URI.create(location.getParameter(Parameter.ALTREP).getValue());
            }
        }
        return null;
    }

    @Override
    public DateTime getStart() {
        return new DateTime(eventStamp.getStartDate());
    }

    @Override
    public Duration getDuration() {
        // Normally a recurring entry is the schedule, with a duration set elsewhere
        // but on cloning for a series, we don't have anything but the calendar to use
        // although this should be perfectly possible
        if (eventStamp.getEndDate() != null) {
            return new Duration(getStart().getMillis(), eventStamp.getEndDate().getTime());
        }

        // duration is not always available, but we use the logic seen around recurring items (not NoteOccurrence's)
        if (getScheduleEndDate() != null) {
            if (eventStamp.isAnyTime()) {
                // return the number of days for the all-day event
                return Duration.standardDays(Days.daysBetween(getStart().toLocalDate(),
                        Objects.requireNonNull(getEnd()).toLocalDate()).getDays());
            } else {
                // we can't know the duration when the start is a datetime and the end is a date?
                return null;
            }
        }
        return null;
    }

    private DateTime getEnd() {
        // end is not always available, but if so its also on recurrenceRules 'setUntil'
        return eventStamp.getEndDate() != null
                ? new DateTime(eventStamp.getEndDate().toInstant())
                : null;
    }

    @Override
    public LocalDate getScheduleEndDate() {
        return eventStamp.getRecurrenceRules().stream()
                .findFirst()
                .filter(r -> r.getUntil() != null)
                .map(r -> JodaToJDKAdapters.localDateToJoda(r.getUntil().toInstant().atZone(ZoneId.of("UTC")).toLocalDate()))
                .orElse(null);
    }

    @Override
    public DaysOfWeek getDays() {
        return eventStamp.getRecurrenceRules().stream()
            .findFirst()
            .map(r -> {
                var dow = r.getDayList();
                return dow == null
                        ? null
                        : (DaysOfWeek) dayOfWeekISO -> dow.contains(WeekDay.getDay(DaysOfWeek.dayOfWeekISOToJavaCalendar(dayOfWeekISO)));
            })
            .orElse(null);
    }

    @Override
    public String getIntervalType() {
        return eventStamp.getRecurrenceRules().stream()
                .findFirst()
                .map(r -> {
                    var i = r.getInterval();
                    if (i == 1 && r.getFrequency().equals(Recur.Frequency.MONTHLY)) {
                        return "MTH";
                    }
                    if (i == 3 && r.getFrequency().equals(Recur.Frequency.MONTHLY)) {
                        return "QTR";
                    }
                    if (i == 6 && r.getFrequency().equals(Recur.Frequency.MONTHLY)) {
                        return "BI";
                    }
                    if (i == 12 && r.getFrequency().equals(Recur.Frequency.MONTHLY)) {
                        return "YR";
                    }
                    return "WK";
                })
                .orElse(null);
    }

    @Override
    public Integer getIntervalFrequency() {
        return eventStamp.getRecurrenceRules().stream()
                .findFirst()
                .map(Recur::getInterval) // if > 1, otherwise null
                .orElse(null);
    }

    @Override
    public URI getManagedByUri() {
        VEvent event = eventStamp.getEvent();
        if (event != null) {
            final Property managedBy = event.getProperty(CosmoConverter.MANAGED_BY_PROPERTY_NAME);
            if (managedBy != null && managedBy.getValue() != null) {
                return URI.create(managedBy.getValue());
            }
        }
        return null;
    }

    @Override
    public URI getUpdatedByUri() {
        VEvent event = eventStamp.getEvent();
        if (event != null) {
            final Property updatedBy = event.getProperty(CosmoConverter.UPDATED_BY_PROPERTY_NAME);
            if (updatedBy != null && updatedBy.getValue() != null) {
                return URI.create(updatedBy.getValue());
            }
        }
        return null;
    }

    @Override
    public String getOwnerCalendarId() {
        return CosmoCalendarService.getOwnerParent(noteItem).getUid();
    }

    @Override
    public Set<Attendee> getAttendees() {
        // Taken from CosmoConverter#convertItemToEntry and NoteOccurrenceToRecurrenceAdapter
        BaseEventStamp stamp = StampUtils.getBaseEventStamp(this.noteItem);
        VEvent event = stamp.getEvent();
        final List<net.fortuna.ical4j.model.property.Attendee> attendees = event.getProperties(Property.ATTENDEE);
        return attendees.stream()
                .map(a -> new AttendeeAdapter(a, converter.getCalendarIdFromAttendee(a)))
                .collect(Collectors.toSet());

        // NB this is from NoteItemToRecurrenceAdapter which assumes a concrete entry (an exception)
        //eventStamp.exceptionEvent.getProperties<net.fortuna.ical4j.model.property.Attendee>(Property.ATTENDEE).stream()
        //        .map { attendee -> AttendeeAdapter(attendee) }
        //    .collect(Collectors.toSet())
    }

}
