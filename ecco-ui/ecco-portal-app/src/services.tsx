import {ApiClient, isOffline, setGlobalApiClient} from "ecco-dto";
import {askUserForCredentials} from "ecco-components";
import {formThenOfflineLogin, getFeatureConfigRepository} from "ecco-offline-data";

export function initApiClient(remoteRoot: string): ApiClient {
    console.debug(`initialising ApiClient with path ${remoteRoot}/api/portal/`);
    const client = new ApiClient(
        remoteRoot + "/api/portal/",
        askUserForCredentials,
        isOffline, // If true prevents API client login, so that offline-data can handle it
        {
            attemptReAuth: true,
            credentials: window.global_credentials
        },
        formThenOfflineLogin
    );

    setGlobalApiClient(client, true);

    return client;
}

export const sessionDataFn = () => getFeatureConfigRepository().getSessionData();
