import {
    Button,
    <PERSON>alog,
    <PERSON>alogA<PERSON>,
    DialogContent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogContent,
    WithMobileDialog,
    withMobileDialog
} from "@eccosolutions/ecco-mui";
import {ActionState, FormContent, withCloseCancel} from "@eccosolutions/ecco-common";
import * as React from "react";
import {
    Component,
    ComponentType,
    ReactElement,
    ReactNode,
    useEffect,
    useRef,
    useState
} from "react";
import {Button as BsButton} from "react-bootstrap";
import {PropInjector} from "@material-ui/types";
import {DialogTitleWithClose} from "./DialogTitleWithClose";
import {useReactToPrint} from "react-to-print";
import {isPromise} from "ecco-commands";

export type Style = "default" | "primary" | "info" | "link" | "warning" | "danger" | "success";

function bsStyle(actionState: ActionState): Style {
    switch (actionState.style) {
        case "secondary":
            return "info";
        case "warning":
            return "danger";
        case "closeIcon": // fallthru
        case "nextIcon": // fallthru
        case "backIcon":
            return "link"; // TODO: probably null or render an icon - let's see
        default:
            return actionState.style || "primary";
    }
}

type ActionStates = ActionState[] | null;

function ActionButtons(props: {
    actionStates: ActionStates;
    onFinished: () => void;
    withCloseOrCancel?: boolean | undefined;
}) {
    const actions = props.withCloseOrCancel
        ? withCloseCancel(props.actionStates, props.onFinished)
        : props.actionStates;
    return (
        <>
            {actions &&
                actions
                    .filter(action => !action.style || !action.style.endsWith("Icon"))
                    .map(action => (
                        <BsButton
                            id={`action-${action.label}`}
                            key={action.label}
                            onClick={() => action.onClick(action.label)}
                            bsStyle={bsStyle(action)}
                        >
                            {action.label}
                        </BsButton>
                    ))}
        </>
    );
}

export interface EccoModalProps {
    children: ReactNode;
    footer?: ReactNode | undefined;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | undefined;
    minHeight?: number | undefined;
    onEscapeKeyDown: () => void;
    title: ReactNode;
    show?: boolean | undefined;
    isLegacy?: true | undefined;
    helpTitle?: ReactNode | undefined;
    helpCallback?: (() => void) | undefined;
    helpContent?: ReactNode | Promise<ReactNode> | undefined;
    showPrint?: true | undefined;
}

function _EccoModal(props: EccoModalProps & WithMobileDialog) {
    const [showHelp, setShowHelp] = useState(false);
    const contentRef = useRef<HTMLDivElement | null>(null);
    const handlePrint = useReactToPrint({content: () => contentRef.current});
    const [helpContentResolved, setHelpContentResolved] = useState<ReactNode>(null);

    useEffect(() => {
        if (showHelp && props.helpCallback) {
            // reset showHelp so we can click it again
            setShowHelp(false);
            props.helpCallback();
        } else if (showHelp && props.helpContent) {
            const q = isPromise(props.helpContent)
                ? props.helpContent
                : Promise.resolve(props.helpContent);
            q.then(content => setHelpContentResolved(content));
        }
    }, [props.helpContent, showHelp]);

    return (
        <>
            <Dialog
                className={`dialog ecco-modal ${props.isLegacy !== undefined ? "" : "m-ui"}`}
                scroll="paper"
                fullScreen={props.fullScreen}
                maxWidth={props.maxWidth || "md"}
                fullWidth={true}
                open={props.show !== undefined ? props.show : true}
                onEscapeKeyDown={props.onEscapeKeyDown}
            >
                <DialogTitleWithClose
                    id="modal-title"
                    onClose={props.onEscapeKeyDown}
                    back={!props.footer}
                    onPrint={props.showPrint === undefined ? undefined : handlePrint}
                    onHelp={
                        props.helpContent || props.helpCallback
                            ? () => setShowHelp(true)
                            : undefined
                    }
                >
                    {props.title}
                </DialogTitleWithClose>
                <DialogContent
                    ref={contentRef}
                    style={
                        props.minHeight !== undefined
                            ? {minHeight: props.minHeight}
                            : {minHeight: 420}
                    }
                >
                    {props.children}
                </DialogContent>
                {props.footer && (
                    <DialogActions className="modal-footer">{props.footer}</DialogActions>
                )}
            </Dialog>
            {helpContentResolved && (
                <EccoModal
                    title={props.helpTitle ?? ""}
                    show={showHelp}
                    onEscapeKeyDown={() => setShowHelp(false)}
                >
                    {helpContentResolved}
                </EccoModal>
            )}
        </>
    );
}

export const EccoModal = withMobileDialog<EccoModalProps>()(_EccoModal);

export type ModalFormProps = {
    form: FormContent;
    actionStates: ActionStates;
    setActionStates: (actionStates: ActionState[]) => void;
    onFinished: () => void;
    readOnly: boolean;
    children: ReactElement;
    withCloseOrCancel?: boolean | undefined;
    isLegacy?: true | undefined;
    onHelp?: () => void | undefined;
    showPrint?: true | undefined;
};

function _ModalForm(props: ModalFormProps & WithMobileDialog) {
    // Only register on the first call
    const {form, actionStates, setActionStates} = props;
    if (actionStates == null && !props.readOnly) {
        // Only do this on first render, and only if not readOnly (for readOnly, we'll just get close button)
        form.registerActionsChangeListener(setActionStates);
        form.setOnFinished(props.onFinished);
    }

    const closeCallback = actionStates?.find(
        as =>
            as.style == "closeIcon" ||
            as.addIcon == "closeIcon" ||
            as.label == "cancel" ||
            as.label == "close"
    )?.onClick;

    const modalStyle = form.getModalStyle();
    const fullScreen = props.fullScreen || modalStyle == "modal-full";
    const maxWidth =
        !modalStyle || modalStyle == "modal-full"
            ? "md"
            : (modalStyle.substr(6) as "sm" | "md" | "lg");
    const onCloseCallback = () => {
        if (closeCallback) {
            closeCallback("closeIcon").then(keepOpen => !keepOpen && props.onFinished());
        } else {
            props.onFinished();
        }
    };
    const componentRef = useRef<HTMLDivElement | null>(null);
    const handlePrint = useReactToPrint({content: () => componentRef.current});

    return (
        <Dialog
            className={`dialog modalform  ${props.isLegacy == true ? "" : "m-ui"}`}
            scroll="paper"
            fullScreen={fullScreen}
            maxWidth={maxWidth}
            fullWidth={true}
            open={true}
            // onEscapeKeyDown={this.props.onCancel}
        >
            <DialogTitleWithClose
                id="modal-title"
                onClose={onCloseCallback}
                onHelp={props.onHelp}
                onPrint={props.showPrint === undefined ? undefined : handlePrint}
            >
                {form.getTitle()}
            </DialogTitleWithClose>
            <MUIDialogContent ref={componentRef} style={{minHeight: 420}}>
                {props.children}
            </MUIDialogContent>
            <DialogActions className="modal-footer">
                <ActionButtons
                    actionStates={actionStates}
                    onFinished={props.onFinished}
                    withCloseOrCancel={props.withCloseOrCancel}
                />
            </DialogActions>
        </Dialog>
    );
}

/** withMobileDialog to ensure we force fullscreeen on mobile */
export const ModalForm = withMobileDialog<ModalFormProps>()(_ModalForm);

export interface UseStateProps {
    readonly actionStates: ActionStates;
    setActionStates: (actionStates: ActionState[]) => void;
}

/** Wrap a component to allow it to have managed action states via React.useState()
 */
export function withActionStates<P>(): PropInjector<UseStateProps> {
    return ((ComponentToWrap: ComponentType<P & UseStateProps>) =>
        function WithActionStates(props: P) {
            const [actionStates, setActionStates] = React.useState<ActionStates>(null);
            return (
                <ComponentToWrap
                    actionStates={actionStates}
                    setActionStates={setActionStates}
                    {...props}
                />
            );
        }) as PropInjector<UseStateProps>;
}

export const ModalFormWithActionStates = withActionStates<ModalFormProps>()(ModalForm);

/** cancel is for things like a wizard in a dialog where we haven't got the wizard actions in the footer yet */
export type DialogAction =
    | "cancel"
    | "save"
    | "finish"
    | "close"
    | "login"
    | "update"
    | "delete"
    | "continue"
    | "add"
    | "send"
    | "search"
    | "none";

export interface FooterProps {
    action: DialogAction;
    actionWithoutCancel?: boolean | undefined; // assumed false/undefined, set to true to hide cancel
    /** Required unless action == "none" */
    onCancel?: React.MouseEventHandler<any> | undefined;
    onSave?: React.MouseEventHandler<any> | undefined; // TODO: Rename to onAction
    saveEnabled?: boolean | undefined; // TODO: Rename to actionEnabled
}

export class Footer extends Component<FooterProps> {
    override render() {
        const {onSave, onCancel, action, actionWithoutCancel, saveEnabled} = this.props;

        switch (action) {
            case "cancel": // FALLTHRU
            case "close":
                return (
                    <Button onClick={onCancel} color="default">
                        {action}
                    </Button>
                );
            case "none":
                return null;
            default:
                return (
                    <>
                        {!actionWithoutCancel && (
                            <Button id="action-cancel" onClick={onCancel} color="default">
                                cancel
                            </Button>
                        )}
                        <Button
                            id={"action-" + action}
                            onClick={onSave}
                            disabled={!saveEnabled}
                            name={"_eventId_save"}
                            variant="contained"
                            color="primary"
                        >
                            {action}
                        </Button>
                    </>
                );
        }
    }
}
