/* Copied from housestyle_themestandard CSS for JSP based calendar events (Temporary!) */
#eventForm textarea { width: 90%; }

/* some css to define a calendar colour by index */
.fc-event {
    font-size: 0.75em;
}

.calendar1,
.fc-agenda .calendar1 .fc-event-time,
.calendar1 a {
    background-color: #3C83CA !important;
    border-color: #3C83CA !important;
    color: white !important;
}
.calendar2,
.fc-agenda .calendar2 .fc-event-time,
.calendar2 a {
    background-color: #872187 !important;
    border-color: #872187 !important;
    color: white !important;
}
.calendar3,
.fc-agenda .calendar3 .fc-event-time,
.calendar3 a {
    background-color: #D568FD !important;
    border-color: #D568FD !important;
    color: white !important;
}
.calendar4,
.fc-agenda .calendar4 .fc-event-time,
.calendar4 a {
    background-color: #03EBA6 !important;
    border-color: #03EBA6 !important;
    color: black !important;
}
.calendar5,
.fc-agenda .calendar5 .fc-event-time,
.calendar5 a {
    background-color: #DFE32D !important;
    border-color: #DFE32D !important;
    color: black !important;
}
.calendar6,
.fc-agenda .calendar6 .fc-event-time,
.calendar6 a {
    background-color: #2966B8 !important;
    border-color: #2966B8 !important;
    color: white !important;
}

.fc-event-location {
    font-style: italic;
    font-size: 0.8em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

ul.fc-event-attendees {
    display: inline;
    list-style: none;
    font-weight: lighter;
    padding: 0;
    margin: 0;
}

/** css provides the '(with ' on attendees */
ul.fc-event-attendees:before {
    content: ' (with ';
}

ul.fc-event-attendees:after {
    content: ')';
}

li.fc-event-attendee {
    display: inline;
}

li.fc-event-attendee:after {
    content: ', ';
}

li.fc-event-attendee:last-child:after {
    content: '';
}
