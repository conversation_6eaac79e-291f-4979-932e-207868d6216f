import {
    AppointmentDto,
    ApiClient,
    EventResourceDto,
    getRelation,
    getRelations,
    SessionData
} from "ecco-dto";
import {EccoDateTime, HateoasResource} from "@eccosolutions/ecco-common";
import * as React from "react";
import {FC, ReactElement} from "react";
import {useServicesContext} from "../ServicesContext";
import {usePromise} from "../data/entityLoadHooks";
import {
    Activity,
    calendarHrefToCalendarId,
    REL_CALENDAR_EVENTS,
    REL_DEMAND_SCHEDULE_ADDITIONAL,
    REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS,
} from "ecco-rota";
import {getCalendarRepository} from "ecco-offline-data";
import {attendeeNotSame} from "../data/careSingleVisitDataLoader";

export interface AdditionalStaffData {
    activityRef?: string | undefined;
    additionalStaffEntries: number;
    additionalStaffNames: string[];
}

/**
 * Find the additional staff for an activity.
 * Additional staff are additional appointments, not attendees on existing appointments.
 * So we query (hateoas links) the attendees to get matching appointments.
 * However, attendees can actually be care runs, which adds the complication to get its assigned worker.
 */
export const additionalStaffPromise = (
    _sessionData: SessionData,
    apiClient: ApiClient,
    activity: HateoasResource,
    activityRef?: string | undefined
): Promise<AdditionalStaffData | undefined> => {
    if (!activity || !getRelation(activity, REL_DEMAND_SCHEDULE_ADDITIONAL)) {
        return Promise.resolve(undefined);
    }

    // fetch many demand-schedule-additional-events which are provided as links to...
    //      'link to the additionalStaff appointments for the same date/time to get the status and worker attendee'
    // NB the links assume the additionalStaff events returned are at the same time - which is the whole point,
    // but we could show the start/end of the worker attendee - it could be they help halfway through etc
    return apiClient
        .fetchRelations<AppointmentDto>([activity], REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS)
        .then(additionalStaffAppointments => {
            // if nothing to process, then return
            if (additionalStaffAppointments.length == 0) {
                return Promise.resolve(undefined);
            }

            // get the number of additionalStaff of the main event, which may or may not be allocated to workers
            const additionalStaffEntries = getRelations(
                [activity],
                REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS
            ).length;

            // get the owner of the main event
            // NB an owner of a rota event can be a client or run (not a worker)
            // (but the SRRotaDecorator only supplies additionalStaff links for isAppointment
            // - so the owner here is not a care run)
            const ownerCalendarId = calendarHrefToCalendarId(
                getRelation(activity, REL_CALENDAR_EVENTS)!.href
            );

            // determine workers from attendees
            // determine workers from care runs by getting attendee events in the range and find the care run attendee
            // when it comes to care runs we are left finding unrelated but overlapping events, not tied-in events
            // because the client owns the appointment which the care run is an attendee, and the care run owns the
            // shift which the worker is the attendee - so there is no link between client and worker directly
            // however, we can be confident because the additionalStaff link means the calendarId is already a rota activity
            // and the events are filtered for care runs that overlap the shared appointment time
            // NB the server side could provide more info on the activity attendee to help OR we could somehow get the run directly
            const workersFromAttendeeEvents = additionalStaffAppointments
                .map(apt => {
                    // find the attendees which are care runs or workers (not the owner, which is the client/building appointment)
                    // see also attendee logic in careSingleVisitDataLoader
                    const attendees = (apt.attendees || []).filter(
                        attendee => attendee.calendarId != ownerCalendarId
                    );

                    // load events for attendees (runs/workers) during the appointment time
                    // to determine if the attendee is the worker, or find the worker in the run
                    return attendees.map(a =>
                        getCalendarRepository()
                            .fetchEventsByCalendarId(
                                [a.calendarId],
                                EccoDateTime.parseIso8601(apt.start),
                                EccoDateTime.parseIso8601(apt.end!)
                            )
                            .then(events => {
                                const careRuns = events.filter(e => Activity.isCareRun(e));
                                if (careRuns.length == 0) {
                                    return a.name; // directly assigned worker since no care run is found
                                } else {
                                    const w = careRuns
                                        .map(e => e.attendees || [])
                                        .reduce((r, x) => r.concat(x), []) // flatMap
                                        // a care run is the owner, therefore the other must be the worker
                                        .filter(attendee => attendeeNotSame(attendee, a))
                                        .map(attendee => `${attendee.name} on run ${a.name}`)
                                        .pop();
                                    return w || `unassigned on run ${a.name}`;
                                }
                            })
                    );
                })
                .reduce((r, x) => r.concat(x), []); // flatMap after promises

            return Promise.all(workersFromAttendeeEvents).then(workers => {
                const data: AdditionalStaffData = {
                    activityRef: activityRef,
                    additionalStaffEntries: additionalStaffEntries,
                    additionalStaffNames: workers
                };
                return data;
            });
        });
};

// LOAD the appointments on the additionalStaff schedules at the date/time of this activity
// NB for offline this solution needs moving server-side
export function useAdditionalStaff(activity: HateoasResource, activityRef: string) {
    const {apiClient, sessionData} = useServicesContext();
    // noinspection JSUnusedLocalSymbols
    const {resolved, error, loading} = usePromise(() => {
        return additionalStaffPromise(sessionData, apiClient, activity, activityRef);
    }, [activityRef]);
    return {additionalStaff: resolved, error, loading};
}

export const AdditionalStaffText = (props: {additionalStaff: AdditionalStaffData | undefined}) => {
    return !props.additionalStaff ? null : (
        <>
            {`with ${props.additionalStaff.additionalStaffEntries} additional staff`}
            {props.additionalStaff.additionalStaffNames.length > 0 &&
                `: ${props.additionalStaff.additionalStaffNames.join(",")}`}
        </>
    );
};

export const AdditionalStaff: FC<{event: EventResourceDto}> = ({event}) => {
    // LOAD the appointments on the additionalStaff schedules at the date/time of this activity
    // NB for offline this solution needs moving server-side
    const {additionalStaff} = useAdditionalStaff(event, event.uid);
    return additionalStaff ? <AdditionalStaffText additionalStaff={additionalStaff} /> : null;
};

/**
 * Loads and renders additional staff relation of the supplied Activity Hateoas resource
 * @param resource - Hateoas resource which may have "risk flags" relation
 * @param ref - Unique ref for the resource
 * @param renderer - function to turn ListDefinitionEntry[] into the ReactElement to render (e.g. could include flag icon)
 * @constructor
 */
export function AdditionalStaffRenderer({
    resource,
    activityRef,
    renderer
}: {
    resource: HateoasResource;
    activityRef: string;
    renderer: (data: AdditionalStaffData) => ReactElement;
}) {
    if (!resource) return null;

    const {additionalStaff} = useAdditionalStaff(resource, activityRef);
    return additionalStaff ? renderer(additionalStaff) : null;
}
