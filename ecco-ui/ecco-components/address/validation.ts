
export function isValidPostcode(postcode: string): boolean {
    return isValidPostcodeUk(postcode) ? true : isValidPostcodeIreland(postcode);
}
export function isValidPostcodeUk(postcode: string): boolean {
    // uk regex should be from https://stackoverflow.com/a/51885364 as per https://regex101.com/r/ajQHrd/15
    const regexUk = /^([Gg][Ii][Rr] ?0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z])))) ?[0-9][A-Za-z]{2})$/;
    return regexUk.test(postcode);
}
export function isValidPostcodeIreland(postcode: string): boolean {
    // but UK doesn't include for Ireland - see z3833
    // regex Ireland from https://stackoverflow.com/a/33408964 as per https://regex101.com/r/dU6eQ7/5
    const regexIreland = /^(?:^[AC-FHKNPRTV-Y][0-9]{2}|D6W)[ -]?[0-9AC-FHKNPRTV-Y]{4}$/;
    return regexIreland.test(postcode.toUpperCase());
}

export function formatPostcode(postcode: string | null): string {
    if (!postcode) {
        return "";
    }
    postcode = postcode.toUpperCase();

    // If not long enough, or already has a string, don't format it
    if (postcode.length < 5 || postcode.indexOf(" ") > 0) {
        return postcode;
    }
    if (isValidPostcodeUk(postcode)) {
        return postcode.substr(0, postcode.length - 3) + " " + postcode.substr(postcode.length - 3);
    } else {
        if (isValidPostcodeIreland(postcode)) {
            return postcode.substr(0, 3) + " " + postcode.substr(3);
        }
        // or isn't valid
        return postcode;
    }
}
