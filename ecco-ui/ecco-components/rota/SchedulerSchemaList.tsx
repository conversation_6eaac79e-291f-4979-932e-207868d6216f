import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {useState} from "react";
import {useHistory} from "react-router";
import {SchemaList} from "../json-schema-form/SchemaList";
import {ScheduleRowProps} from "./SchedulerView";

export function SchedulerSchemaList() {
    const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});

    // something here
    const params = new URLSearchParams();
    params.append("from", EccoDate.todayLocalTime().formatIso8601());
    if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key].forEach(val => params.append(key, val)));
    const paramStr = params.toString();
    const src = paramStr.length ? `scheduler/?${paramStr}` : "scheduler/";

    const history = useHistory();

    return (
        <Box m={1}>
            <SchemaList
                title="scheduler"
                src={src}
                displayFields={["from"]}
                filterFields={[]}
                filters={filters}
                onFilter={filters => {
                    setPage(0);
                    setFilters(filters);
                }}
                page={page}
                onPage={setPage}
                onSearch={search => {
                    setPage(0);
                    setSearch(search);
                }}
                searchText={search}
                onRowClick={snapshot =>
                    history.push("./scheduler/" + (snapshot as any as ScheduleRowProps).srId)
                }
            />
        </Box>
    );
}
