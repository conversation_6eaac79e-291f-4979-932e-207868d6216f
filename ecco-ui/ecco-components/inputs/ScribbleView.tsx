import {
    FC,
    useEffect,
    useRef,
    useState
} from "react";
import * as React from "react";
import "./ScribbleView.css";
import {mapNullable} from "@softwareventures/nullable";
import {useResizeCounter} from "../hooks/useResizeCounter";

export interface Props {
    /** Either a URL or data: for the image of the scribble. Use null if no scribble exists */
    readonly scribbleUrl: string | null;
    readonly width?: number | undefined;
    readonly height?: number | undefined;
}

/**
 * View stuff captured using ScribbleCanvas and ScribbleBackgroundCanvas.
 * To see overlaid on background, provide the background as child element in same way as is done with
 * ScribbleBackgroundCanvas
 */
export const ScribbleView: FC<Props> = (props) => {
    const container = useRef<HTMLDivElement | null>(null);
    const background = useRef<HTMLDivElement | null>(null);
    const backgroundResize = useResizeCounter(background.current);
    const [width, setWidth] = useState(props.width ?? undefined);
    const [height, setHeight] = useState(props.height ?? undefined);

    useEffect(() => {
        setWidth(props.width ?? background.current?.clientWidth ?? undefined);
        setHeight(props.height ?? background.current?.clientHeight ?? undefined);
    }, [background.current, backgroundResize, props.width, props.height]);

    useEffect(() => {
        if (container.current != null) {
            container.current.style.width = mapNullable(width, width => `${width}px`) ?? "";
            container.current.style.height = mapNullable(height, height => `${height}px`) ?? "";
        }
    }, [container.current, width, height]);

    return (
        <div className="ScribbleView">
            <div ref={container} className="container">
                {mapNullable(props.children, children => (
                    <div ref={background} className="background">
                        {children}
                    </div>
                ))}
                {props.scribbleUrl && (
                    <img
                        alt="hand drawn lines"
                        className={props.children ? "overlay" : undefined}
                        src={props.scribbleUrl}
                        width={width}
                        height={height}
                    />
                )}
            </div>
        </div>
    );
};
