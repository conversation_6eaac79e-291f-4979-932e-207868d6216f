import {mount} from "cypress/react";
import * as React from "react";
import {carer1, carer2, sessionData, testStaff} from "../../__tests__/testUtils";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {EccoTheme} from "ecco-components-core";
import {SchedulerView} from "../../rota/SchedulerView";
import {Command, CommandAjaxRepository, CommandRepository, MergeableCommand} from "ecco-commands";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {EventSnapshotDto, DemandScheduleDto, WorkersAjaxRepository} from "ecco-dto";
import {RotaAjaxRepository, RotaRepository} from "ecco-rota";

import {
    FormGroup,
    FormLabel,
    FormControl,
    ListItemText,
    TextField,
    Checkbox,
    FormControlLabel,
    Select,
    InputLabel,
    MenuItem,
    MUIDataTable
} from "@eccosolutions/ecco-mui";
import {MUIDataTableColumnDef, MUIDataTableOptions} from "mui-datatables";

/* for the EvidenceView approach, before we used the snapshot approach
const invoicesRepository = getFailAllMethodsMock<InvoicesRepository>(InvoicesAjaxRepository);
invoicesRepository.findAllUninvoicedLines = (
    startDate: EccoDate,
    endDate: EccoDate,
    buildingId: number | null
) => {
    return Promise.resolve(uninvoiced);
};*/

/* for the schema related loading of snapshots - this was server-side and could be brought back
const apiClient = getFailAllMethodsMock(ApiClient);
apiClient.get = (
    path: string | ToStringable,
    options?: RequestOptions,
    requestBody?: Object | null
) => {
    return Promise.resolve({});
};
apiClient.fetchRelation = (resource: HateoasResource, relation: string, allowNotFound?: true) => {
    const schema = {
        type: "object",
        id: "http://localhost:8899/scheduler/$schema/",
        $schema: "http://json-schema.org/draft-03/schema#",
        links: [
            {
                href: "http://localhost:8899/scheduler/",
                rel: "instances",
                method: "GET",
                schema: {
                    type: "object",
                    id: "http://localhost:8899/scheduler/",
                    properties: {
                        from: {type: "string", id: "order:005_from", format: "date"},
                        search: {type: "string", id: "order:010_search"},
                        postCode: {type: "string", id: "order:030_postCode"},
                        page: {type: "integer", id: "order:999_page", default: "0"},
                        pageSize: {
                            type: "integer",
                            id: "order:999_pageSize",
                            readonly: true,
                            default: "50"
                        }
                    }
                }
            }
        ],
        properties: {
            contactId: {type: "integer", id: "order:999_contactId"},
            demandContact: {
                type: "object",
                id: "order:999_demandContact",
                properties: {
                    address: {
                        type: "object",
                        id: "order:999_address",
                        properties: {
                            address: {
                                type: "array",
                                id: "order:999_address",
                                items: {type: "string"}
                            },
                            addressId: {type: "integer", id: "order:999_addressId"},
                            county: {type: "string", id: "order:999_county"},
                            postcode: {type: "string", id: "order:999_postcode"},
                            town: {type: "string", id: "order:999_town"}
                        }
                    },
                    addressedLocationId: {type: "integer", id: "order:999_addressedLocationId"},
                    archived: {type: "string", id: "order:999_archived"},
                    avatarId: {type: "integer", id: "order:999_avatarId"},
                    calendarId: {type: "string", id: "order:999_calendarId"},
                    code: {type: "string", id: "order:999_code"},
                    contactId: {type: "integer", id: "order:999_contactId"},
                    discriminator: {type: "string", id: "order:999_discriminator"},
                    email: {type: "string", id: "order:999_email"},
                    firstName: {type: "string", id: "order:999_firstName"},
                    isUser: {type: "boolean", id: "order:999_isUser"},
                    jobTitle: {type: "string", id: "order:999_jobTitle"},
                    lastName: {type: "string", id: "order:999_lastName"},
                    mobileNumber: {type: "string", id: "order:999_mobileNumber"},
                    organisation: {
                        type: "object",
                        id: "order:999_organisation",
                        properties: {
                            address: {
                                type: "object",
                                id: "order:999_address",
                                $ref: "urn:jsonschema:com:ecco:webApi:contacts:address:AddressViewModel"
                            },
                            addressedLocationId: {
                                type: "integer",
                                id: "order:999_addressedLocationId"
                            },
                            agencyCategory: {type: "string", id: "order:999_agencyCategory"},
                            archived: {type: "string", id: "order:999_archived"},
                            avatarId: {type: "integer", id: "order:999_avatarId"},
                            code: {type: "string", id: "order:999_code"},
                            companyName: {type: "string", id: "order:999_companyName"},
                            contactId: {type: "integer", id: "order:999_contactId"},
                            contextId: {type: "integer", id: "order:999_contextId"},
                            discriminator: {type: "string", id: "order:999_discriminator"},
                            email: {type: "string", id: "order:999_email"},
                            outOfArea: {type: "boolean", id: "order:999_outOfArea"},
                            phoneNumber: {type: "string", id: "order:999_phoneNumber"}
                        }
                    },
                    organisationId: {type: "integer", id: "order:999_organisationId"},
                    phoneNumber: {type: "string", id: "order:999_phoneNumber"},
                    preferredContactMethod: {
                        type: "string",
                        id: "order:999_preferredContactMethod",
                        enum: ["Unknown", "Email", "Mobile", "Landline", "Letter", "Sms"]
                    },
                    title: {type: "string", id: "order:999_title"},
                    user: {type: "boolean", id: "order:999_user"},
                    userLastLoggedIn: {
                        type: "string",
                        id: "order:999_userLastLoggedIn",
                        format: "date-time"
                    }
                }
            },
            endInstant: {type: "string", id: "order:999_endInstant"},
            eventUid: {type: "string", id: "order:999_eventUid"},
            links: {
                type: "array",
                id: "order:999_links",
                items: {
                    type: "object",
                    id: "urn:jsonschema:org:springframework:hateoas:Link",
                    properties: {
                        deprecation: {type: "string", id: "order:999_deprecation"},
                        href: {type: "string", id: "order:999_href"},
                        hreflang: {type: "string", id: "order:999_hreflang"},
                        media: {type: "string", id: "order:999_media"},
                        name: {type: "string", id: "order:999_name"},
                        profile: {type: "string", id: "order:999_profile"},
                        rel: {type: "string", id: "order:999_rel"},
                        title: {type: "string", id: "order:999_title"},
                        type: {type: "string", id: "order:999_type"}
                    }
                }
            },
            location: {
                type: "object",
                id: "order:999_location",
                properties: {
                    errorCode: {type: "integer", id: "order:999_errorCode"},
                    lat: {type: "number", id: "order:999_lat"},
                    lon: {type: "number", id: "order:999_lon"},
                    time: {type: "integer", id: "order:999_time"}
                }
            },
            plannedEndInstant: {type: "string", id: "order:999_plannedEndInstant"},
            plannedLocation: {
                type: "object",
                id: "order:999_plannedLocation",
                $ref: "urn:jsonschema:com:ecco:webApi:contacts:address:AddressViewModel"
            },
            plannedStartInstant: {type: "string", id: "order:999_plannedStartInstant"},
            resourceContact: {
                type: "object",
                id: "order:999_resourceContact",
                $ref: "urn:jsonschema:com:ecco:webApi:contacts:IndividualViewModel"
            },
            serviceRecipientId: {type: "integer", id: "order:999_serviceRecipientId"},
            startInstant: {type: "string", id: "order:999_startInstant"},
            type: {type: "integer", id: "order:999_type"},
            workUuid: {type: "string", id: "order:999_workUuid", format: "uuid"}
        }
    };
    //schema['loaded'] = Promise.resolve(null);
    return Promise.resolve(schema);
};*/

const commandRepository = getFailAllMethodsMock<CommandRepository>(CommandAjaxRepository);
commandRepository.sendCommand<any> = (command: Command | MergeableCommand) => {
    return Promise.resolve({});
};

const demandScheduleDto1: DemandScheduleDto = {
    agreementId: 99,
    intervalType: "WK",
    calendarDays: [1,2,3]
} as DemandScheduleDto;
const demandScheduleDto2: DemandScheduleDto = {
    agreementId: 100,
    intervalType: "MTH"
} as DemandScheduleDto;

const snapshots = [
    {
        eventUid: "a6507a5e-00f2-4e63-b457-283d9818a305:20230718T180000",
        demandScheduleId: 99,
        demandScheduleDto: demandScheduleDto1,
        serviceRecipientId: 200220,
        demandContact: {
            contactId: 100877,
            discriminator: "individual",
            address: {
                addressId: null,
                address: [null, null, null],
                town: null,
                county: null,
                postcode: null,
                disabled: false
            },
            firstName: "Mickey",
            lastName: "Mouse",
            calendarId: "180c2997-224e-4bc4-8428-2c63802fcc96",
            isUser: true,
            user: true
        },
        resourceContact: null,
        plannedStartInstant: "2023-07-18T18:00:00Z",
        plannedEndInstant: "2023-07-18T18:10:00Z",
        type: "3 weekly",
        plannedLocation: {address: ["1 The Road", "Town", "CO16 0ED"], postCode: "CO16 0ED"},
        location: null,
        //startInstant: null,
        startInstant: "2023-07-19T18:10:00Z",
        endInstant: null,
        contactId: null,
        workUuid: null,
        links: []
    },
    {
        eventUid: "c3d43122-d1c8-4b82-b923-d696d2a9f12d:20230718T180000",
        demandScheduleId: 100,
        demandScheduleDto: demandScheduleDto2,
        serviceRecipientId: 200220,
        demandContact: {
            contactId: 100877,
            discriminator: "individual",
            address: {
                addressId: null,
                address: [null, null, null],
                town: null,
                county: null,
                postcode: null,
                disabled: false
            },
            firstName: "Mickey",
            lastName: "Mouse",
            calendarId: "180c2997-224e-4bc4-8428-2c63802fcc96",
            isUser: true,
            user: true
        },
        resourceContact: {firstName: "Jo", lastName: "Eaton"},
        plannedStartInstant: "2023-07-21T18:00:00Z",
        plannedEndInstant: "2023-07-21T18:10:00Z",
        type: "fire, flush & lint",
        plannedLocation: {address: ["41 The Road", "Town", "CO16 0ED"], postCode: "CO16 0ED"},
        location: null,
        startInstant: null,
        endInstant: null,
        contactId: null,
        workUuid: null,
        links: []
    }
] as any as EventSnapshotDto[];

const rotaRepository = getFailAllMethodsMock<RotaRepository>(RotaAjaxRepository);
rotaRepository.findEventSnapshotsFromToUtc = (fromUtc: EccoDateTime, toUtc: EccoDateTime): Promise<EventSnapshotDto[]> => {
    return Promise.resolve(snapshots);
};

rotaRepository.findRotaDemands = (
    startDate: EccoDate,
    endDate: EccoDate | null,
    resourceFilter: string,
    demandFilter: string | null
): Promise<number[]> => {
    return Promise.resolve([]);
};

const workersRepository = getFailAllMethodsMock(WorkersAjaxRepository);
workersRepository.findWorkersWithAccessTo = (
    serviceId: number,
    projectId?: number | undefined,
    role = "ROLE_CARER"
) => {
    return Promise.resolve([carer1, carer2]);
};
workersRepository.findWorkersEmployedAtByIndividualIds = (
        individualIds: number[],
        employedAt: EccoDate
) => {
    return Promise.resolve(testStaff);
};

const overrides = {
    //apiClient: apiClient,
    sessionData: sessionData,
    //invoicesRepository: invoicesRepository,
    workersRepository: workersRepository,
    rotaRepository: rotaRepository,
    getCommandRepository: () => commandRepository
} as any as EccoAPI;

describe("Scheduler tests", () => {
    it("render page", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <EccoTheme prefix="app">
                    {/*<Example />*/}
                    <SchedulerView />
                </EccoTheme>
            </TestServicesContextProvider>
        );
        cy.viewport(1250, 750);
    });
});


class Example extends React.Component {
    state = {
        ageFilterChecked: false
    };

    override render() {
        const columns: MUIDataTableColumnDef[] = [
            {
                name: "Name",
                options: {
                    filter: true,
                    filterOptions: {
                        renderValue: v => (v ? v.replace(/^(\w).* (.*)$/, "$1. $2") : "")
                    },
                    //display: 'excluded',
                    filterType: "dropdown"
                }
            },
            {
                label: "Modified Title Label",
                name: "Title",
                options: {
                    filter: true,
                    customFilterListOptions: {
                        render: v => v.toLowerCase()
                    }
                }
            },
            {
                label: "Location",
                name: "Location",
                options: {
                    filter: true,
                    display: "true",
                    filterType: "custom",
                    customFilterListOptions: {
                        render: v => v.map(l => l.toUpperCase()),
                        update: (filterList, filterPos, index) => {
                            console.log("update");
                            console.log(filterList, filterPos, index);
                            filterList[index].splice(filterPos, 1);
                            return filterList;
                        }
                    },
                    filterOptions: {
                        logic: (location, filters) => {
                            if (filters.length) return !filters.includes(location);
                            return false;
                        },
                        display: (filterList, onChange, index, column) => {
                            const optionValues = ["Minneapolis", "New York", "Seattle"];
                            return (
                                <FormControl>
                                    <InputLabel htmlFor="select-multiple-chip">Location</InputLabel>
                                    <Select
                                        multiple
                                        value={filterList[index]}
                                        renderValue={selected => (selected as any[]).join(", ")}
                                        onChange={event => {
                                            filterList[index] = event.target.value as string;
                                            onChange(filterList[index], index, column);
                                        }}
                                    >
                                        {optionValues.map(item => (
                                            <MenuItem key={item} value={item}>
                                                <Checkbox
                                                    color="primary"
                                                    checked={filterList[index].indexOf(item) > -1}
                                                />
                                                <ListItemText primary={item} />
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            );
                        }
                    }
                }
            },
            {
                name: "Age",
                options: {
                    filter: true,
                    filterType: "custom",

                    // if the below value is set, these values will be used every time the table is rendered.
                    // it's best to let the table internally manage the filterList
                    //filterList: [25, 50],

                    customFilterListOptions: {
                        render: v => {
                            if (v[0] && v[1] && this.state.ageFilterChecked) {
                                return [`Min Age: ${v[0]}`, `Max Age: ${v[1]}`];
                            } else if (v[0] && v[1] && !this.state.ageFilterChecked) {
                                return `Min Age: ${v[0]}, Max Age: ${v[1]}`;
                            } else if (v[0]) {
                                return `Min Age: ${v[0]}`;
                            } else if (v[1]) {
                                return `Max Age: ${v[1]}`;
                            }
                            return [];
                        },
                        update: (filterList, filterPos, index) => {
                            console.log("customFilterListOnDelete: ", filterList, filterPos, index);

                            if (filterPos === 0) {
                                filterList[index].splice(filterPos, 1, "");
                            } else if (filterPos === 1) {
                                filterList[index].splice(filterPos, 1);
                            } else if (filterPos === -1) {
                                filterList[index] = [];
                            }

                            return filterList;
                        }
                    },
                    filterOptions: {
                        names: [],
                        logic(age, filters) {
                            if (filters[0] && filters[1]) {
                                return age < filters[0] || age > filters[1];
                            } else if (filters[0]) {
                                return age < filters[0];
                            } else if (filters[1]) {
                                return age > filters[1];
                            }
                            return false;
                        },
                        display: (filterList, onChange, index, column) => (
                            <div>
                                <FormLabel>Age</FormLabel>
                                <FormGroup row>
                                    <TextField
                                        label="min"
                                        value={filterList[index][0] || ""}
                                        onChange={event => {
                                            filterList[index][0] = event.target.value as string;
                                            onChange(filterList[index], index, column);
                                        }}
                                        style={{width: "45%", marginRight: "5%"}}
                                    />
                                    <TextField
                                        label="max"
                                        value={filterList[index][1] || ""}
                                        onChange={event => {
                                            filterList[index][1] = event.target.value as string;
                                            onChange(filterList[index], index, column);
                                        }}
                                        style={{width: "45%"}}
                                    />
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={this.state.ageFilterChecked}
                                                onChange={event =>
                                                    this.setState({
                                                        ageFilterChecked: event.target.checked
                                                    })
                                                }
                                            />
                                        }
                                        label="Separate Values"
                                        style={{marginLeft: "0px"}}
                                    />
                                </FormGroup>
                            </div>
                        )
                    },
                    print: false
                }
            },
            {
                name: "Salary",
                options: {
                    filter: true,
                    filterType: "checkbox",
                    filterOptions: {
                        names: ["Lower wages", "Average wages", "Higher wages"],
                        logic(salary, filterVal) {
                            salary = salary.replace(/[^\d]/g, "");
                            const show =
                                (filterVal.indexOf("Lower wages") >= 0 &&
                                    parseInt(salary) < 100000) ||
                                (filterVal.indexOf("Average wages") >= 0 &&
                                    parseInt(salary) >= 100000 &&
                                    parseInt(salary) < 200000) ||
                                (filterVal.indexOf("Higher wages") >= 0 &&
                                    parseInt(salary) >= 200000);
                            return !show;
                        }
                    },
                    sort: false
                }
            }
        ];

        const data = [
            ["Gabby George", "Business Analyst", "Minneapolis", 30, "$100,000"],
            ["Aiden Lloyd", "Business Consultant", "Dallas", 55, "$200,000"],
            ["Jaden Collins", "Attorney", "Santa Ana", 27, "$500,000"],
            ["Franky Rees", "Business Analyst", "St. Petersburg", 22, "$50,000"],
            ["Aaren Rose", "Business Consultant", "Toledo", 28, "$75,000"],
            ["Blake Duncan", "Business Management Analyst", "San Diego", 65, "$94,000"],
            ["Frankie Parry", "Agency Legal Counsel", "Jacksonville", 71, "$210,000"],
            ["Lane Wilson", "Commercial Specialist", "Omaha", 19, "$65,000"],
            ["Robin Duncan", "Business Analyst", "Los Angeles", 20, "$77,000"],
            ["Mel Brooks", "Business Consultant", "Oklahoma City", 37, "$135,000"],
            ["Harper White", "Attorney", "Pittsburgh", 52, "$420,000"],
            ["Kris Humphrey", "Agency Legal Counsel", "Laredo", 30, "$150,000"],
            ["Frankie Long", "Industrial Analyst", "Austin", 31, "$170,000"],
            ["Brynn Robbins", "Business Analyst", "Norfolk", 22, "$90,000"],
            ["Justice Mann", "Business Consultant", "Chicago", 24, "$133,000"],
            ["Addison Navarro", "Business Management Analyst", "New York", 50, "$295,000"],
            ["Jesse Welch", "Agency Legal Counsel", "Seattle", 28, "$200,000"],
            ["Eli Mejia", "Commercial Specialist", "Long Beach", 65, "$400,000"],
            ["Gene Leblanc", "Industrial Analyst", "Hartford", 34, "$110,000"],
            ["Danny Leon", "Computer Scientist", "Newark", 60, "$220,000"],
            ["Lane Lee", "Corporate Counselor", "Cincinnati", 52, "$180,000"],
            ["Jesse Hall", "Business Analyst", "Baltimore", 44, "$99,000"],
            ["Danni Hudson", "Agency Legal Counsel", "Tampa", 37, "$90,000"],
            ["Terry Macdonald", "Commercial Specialist", "Miami", 39, "$140,000"],
            ["Justice Mccarthy", "Attorney", "Tucson", 26, "$330,000"],
            ["Silver Carey", "Computer Scientist", "Memphis", 47, "$250,000"],
            ["Franky Miles", "Industrial Analyst", "Buffalo", 49, "$190,000"],
            ["Glen Nixon", "Corporate Counselor", "Arlington", 44, "$80,000"],
            ["Gabby Strickland", "Business Process Consultant", "Scottsdale", 26, "$45,000"],
            ["Mason Ray", "Computer Scientist", "San Francisco", 39, "$142,000"]
        ];

        const options: MUIDataTableOptions = {
            filter: true,
            filterType: "multiselect",
            responsive: "standard",
            setFilterChipProps: (colIndex, colName, data) => {
                //console.log(colIndex, colName, data);
                return {
                    color: "primary",
                    variant: "outlined",
                    className: "testClass123"
                };
            }
        };

        return (
            <MUIDataTable
                title={"ACME Employee list - customizeFilter"}
                data={data}
                columns={columns}
                options={options}
            />
        );
    }
}

export default Example;