import {
    createStyles,
    IconButton,
    makeStyles,
    Table,
    TableBody,
    TableCell,
    TableRow,
    Theme
} from "@eccosolutions/ecco-mui";
import {RateCardDto, RateCardEntryDto} from "ecco-dto";
import * as React from "react";
import {useServicesContext} from "../ServicesContext";
import {IdName, SelectListOption} from "@eccosolutions/ecco-common";
import EditIcon from "@material-ui/icons/Edit";

interface RateCardEntryProps {
    rateCardEntry: RateCardEntryDto;
    rateCard: RateCardDto;
    chargeTypesFixedTemporal: SelectListOption[];
    editRateCardEntryCallback: (
        onSave: () => void,
        rateCard: RateCardDto,
        rateCardEntry: RateCardEntryDto
    ) => void;
    reloadRateCardEntry: (rateCardEntryId: number) => void;
}

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        table: {
            minWidth: 800,
            width: "100%"
        },
        muted: {
            opacity: 0.5
        }
    })
);
function RateCardEntryTableRow(props: RateCardEntryProps): React.ReactElement {
    const classes = useStyles();
    const {rateCardEntry} = props;

    const {sessionData} = useServicesContext();
    const unitOfMeasurements: IdName[] = sessionData?.getDto().unitOfMeasurements;
    const unitOfMeasurement =
        unitOfMeasurements.find(u => u.id === rateCardEntry.unitMeasurementId)?.name ?? "unit";

    function openEditRateCardEntryDto() {
        props.editRateCardEntryCallback(
            () => props.reloadRateCardEntry(rateCardEntry.rateCardEntryId),
            props.rateCard,
            rateCardEntry
        );
    }

    /*function deleteRateCardEntryDto() {
        if (window.confirm("Are you sure you want to delete this rateCardEntry?")) {
            // const cmd = new ServiceRecipientAppointmentRateCardEntryDtoCommand(
            //     "remove", Uuid.randomV4(),props.srId, rateCardEntry.eventRef, props.rateCard.getRateCardDtoId());
            // commandRepository.sendCommand(cmd).then( () => props.reloadRateCardEntryDto(rateCardEntry.eventRef)); // TODO: This should cause it to disappear.
        }
    }*/

    const fixedChargeDisplay = () => {
        return "fixed £" + rateCardEntry.fixedCharge?.toFixed(2);
    };
    const temporalChargeDisplay = () => {
        return (
            "£" +
            rateCardEntry.unitCharge?.toFixed(2) +
            " for " +
            rateCardEntry.units +
            " " +
            unitOfMeasurement +
            (rateCardEntry.units > 1 ? "s" : "") +
            (rateCardEntry.unitsToRepeatFor ? " up to " + rateCardEntry.unitsToRepeatFor : "")
        );
    };

    //const muiTableOpacity = 0.75;

    return (
        <>
            <TableRow className={rateCardEntry?.disabled ? classes.muted : ""}>
                <TableCell>
                    {rateCardEntry.matchingChargeCategoryId &&
                        sessionData
                            .getListDefinitionEntryById(rateCardEntry.matchingChargeCategoryId)
                            ?.getDisplayName()}
                </TableCell>
                {/*<TableCell>{rateCardEntry.matchingFactors}</TableCell>*/}
                <TableCell>
                    {rateCardEntry.chargeTypeFixedTemporal === "FIXED"
                        ? fixedChargeDisplay()
                        : rateCardEntry.chargeTypeFixedTemporal === "TEMPORAL"
                          ? temporalChargeDisplay()
                          : fixedChargeDisplay() + " and " + temporalChargeDisplay()}
                </TableCell>
                {/*<TableCell>{rateCardEntry.childRateCardEntryId}</TableCell>*/}
                {/*<TableCell>{rateCardEntry.unitMeasurementId}</TableCell>*/}
                <TableCell>{rateCardEntry.defaultEntry && "default"}</TableCell>
                <TableCell>
                    <IconButton
                        color="primary"
                        size="small"
                        onClick={() => openEditRateCardEntryDto()}
                    >
                        <EditIcon />
                    </IconButton>
                </TableCell>
            </TableRow>
        </>
    );
}

export function RateCardEntriesTable(props: {
    rateCard: RateCardDto;
    chargeTypesFixedTemporal: SelectListOption[];
    editRateCardEntryCallback: (
        onSave: () => void,
        rateCard: RateCardDto,
        rateCardEntry?: RateCardEntryDto | undefined
    ) => void;
    reloadRateCardEntry: (rateCardEntryId: number) => void;
}) {
    const classes = useStyles();
    const {rateCard} = props;

    return (
        <Table className={classes.table}>
            <TableBody>
                {rateCard.rateCardEntries.map(e => (
                    <RateCardEntryTableRow
                        key={e.rateCardEntryId}
                        rateCardEntry={e}
                        rateCard={rateCard}
                        chargeTypesFixedTemporal={props.chargeTypesFixedTemporal}
                        editRateCardEntryCallback={props.editRateCardEntryCallback}
                        reloadRateCardEntry={props.reloadRateCardEntry}
                    />
                ))}
            </TableBody>
        </Table>
    );
}