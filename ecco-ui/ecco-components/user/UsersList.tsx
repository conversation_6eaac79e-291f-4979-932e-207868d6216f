import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box, Button} from "@eccosolutions/ecco-mui";
import {UserDto} from "ecco-dto";
import * as React from "react";
import {FC, useState} from "react";
import {useHistory} from "react-router";
import {SchemaList} from "../json-schema-form/SchemaList";
import {UserForm} from "./UserForm";
import {useAppBarOptions} from "../AppBarBase";


export const UsersSelectList: FC<{selected: (user: UserDto) => void}> = props => {
    const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});

    const params = new URLSearchParams();
    if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key].forEach(val => params.append(key, val)));
    /*if (search || filterKeys.length) {
        params.append("enabled", "false");
    }*/
    const paramStr = params.toString();
    const src = paramStr.length ? `users/?${paramStr}` : "users/";

    return (
        <Box m={1}>
            <SchemaList
                title="users"
                src={src}
                displayFields={["username", "firstName", "lastName", "enabled", "lastLoggedIn"]}
                filterFields={["enabled", "group", "mfaRequired"]}
                filters={filters}
                onFilter={filters => {
                    setPage(0);
                    setFilters(filters);
                }}
                page={page}
                onPage={setPage}
                onSearch={search => {
                    setPage(0);
                    setSearch(search);
                }}
                searchText={search}
                onRowClick={user => props.selected(user as UserDto)}
            />
        </Box>
    );
};

export function UsersList () {
    useAppBarOptions("manage users");
    const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});
    const [editing, setEditing] = useState(false);
    const [username, setUsername] = useState<string | undefined>();

    const params = new URLSearchParams();
    if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key]!.forEach(val => params.append(key, val)));
    if (search || filterKeys.length) {
        params.append("enabled", "false")
    }
    const paramStr = params.toString();
    const src = paramStr.length ? `users/?${paramStr}` : "users/";

    const history = useHistory();

    return (
        <Box m={1}>
            <Box my={1}>
                {/*TODO: Move button into top bar + as on rota*/}
                <Button
                    key="new-user"
                    variant="outlined"
                    onClick={() => {
                        setEditing(true);
                        setUsername(undefined);
                    }}
                >
                    new user
                </Button>
            </Box>
            <SchemaList
                title="users"
                src={src}
                displayFields={[
                    "username",
                    "firstName",
                    "lastName",
                    "enabled",
                    "lastLoggedIn",
                    "mfaRequired"
                ]}
                filterFields={["enabled", "group", "mfaRequired"]}
                filters={filters}
                onFilter={filters => {
                    setPage(0);
                    setFilters(filters);
                }}
                page={page}
                onPage={setPage}
                onSearch={search => {
                    setPage(0);
                    setSearch(search);
                }}
                searchText={search}
                onRowClick={user => history.push("./users/" + (user as UserDto).username)}
            />
            <UserForm
                show={editing}
                setShow={setEditing}
                username={username}
                notifyNewUsername={setSearch} // Changes the search, so we don't need afterSave={reload}. Props change does it
            />
        </Box>
    );
}