import * as React from "react";
import {Component} from "react";
import {EccoDate, EccoTime} from "@eccosolutions/ecco-common";
import {FormControl, FormGroup} from "react-bootstrap";
import {
    calcValidationState,
    createPathUpdate,
    resolvePath,
    stringFromHtmlInput
} from "ecco-components-core";

const halfRow = {width: "50%", display: "inline-block"};


interface DT_STATE {
    date: string | null; // dd/MM/yyyy
    time: string | null; // hh:mm TODO Check these
}

/** Do same as a dateTimeInput but using two input fields */
export class DateAndTime<STATE> extends Component<
    {
        propertyKey: string;
        label: string;
        stateSetter: (state: STATE) => void;
        state: STATE;
        disabled: boolean;
        required: boolean;
    },
    DT_STATE
> {
    override state = {date: null, time: null} as DT_STATE;

    private onChangeDate = (date: string | null) => {
        this.setState({date});
        const datetime =
            this.state.time == null || date == null
                ? null
                : EccoDate.parseIso8601(date)!!.toDateTime(EccoTime.parseIso8601(this.state.time));
        this.props.stateSetter(
            createPathUpdate(
                this.props.state,
                this.props.propertyKey,
                datetime && datetime.formatIso8601()
            )
        );
    };

    private onChangeTime = (time: string | null) => {
        this.setState({time});
        const datetime =
            this.state.date == null || time == null
                ? null
                : EccoDate.parseIso8601(this.state.date)!!.toDateTime(
                      EccoTime.parseIso8601(time)!!
                  );
        this.props.stateSetter(
            createPathUpdate(
                this.props.state,
                this.props.propertyKey,
                datetime && datetime.formatIso8601()
            )
        );
    };

    override render() {
        const {propertyKey, state, required, label, disabled} = this.props;
        const currValue = resolvePath<string>(state, propertyKey);
        const validationState = calcValidationState(required, currValue);
        const validationClass = "control-label ".concat(
            validationState == "error"
                ? "has-error"
                : validationState == "success"
                ? "has-success"
                : ""
        );

        return (
            <FormGroup label={label} validationState={validationState}>
                <label className={validationClass}>{label}</label>
                <div>
                    <div style={halfRow}>
                        <FormControl
                            name={propertyKey + "_date"}
                            type="date"
                            onChange={e => this.onChangeDate(stringFromHtmlInput(e.target))}
                            value={this.state.date || ""}
                            disabled={disabled}
                        />
                    </div>
                    <div style={halfRow}>
                        <FormControl
                            name={propertyKey + "_time"}
                            type="time"
                            onChange={e => this.onChangeTime(stringFromHtmlInput(e.target))}
                            value={this.state.time || ""}
                            disabled={disabled}
                        />
                    </div>
                </div>
            </FormGroup>
        );
    }
}