{
  "extends": "../tsconfig.ecco-module.json",
  "compilerOptions": {
    "baseUrl": "",
    "outDir": "./build-tsc",
    "declarationDir": "./build-tsc"
  },
  "references": [
    {"path": "../ecco-commands/tsconfig.json"},
    {"path": "../ecco-components/tsconfig.json"},
    {"path": "../ecco-dto/tsconfig.json"}
  ],
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["cypress", "debug", "dist", "build-tsc"] // Technically shouldn't need to specify debug and dist as they don't have *.tsx? in
}
