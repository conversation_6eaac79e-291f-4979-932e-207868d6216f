import * as React from "react";
import {FC, useState} from "react";
import {Paper} from "@eccosolutions/ecco-mui";
import {
    SessionDataDto,
    FeatureSetDto,
    EvidencePageType,
    EvidenceGroup,
    SessionData
} from "ecco-dto";
import {
    EvidenceDef,
    QuestionAnswer,
    QuestionAnswerData,
    QuestionAnswerInit,
    QuestionAnswerState
} from "ecco-evidence";
import {configResolver, question1} from "./testUtils";
import {Uuid} from "@eccosolutions/ecco-crypto";

/**
 * Layout of the QuestionAnswer with debug info
 */
const QuestionAnswerTestLayout: FC = () => {
    // create/hold the props
    const init: QuestionAnswerInit = testQuestionAnswerInit();
    const [state, setState] = useState<QuestionAnswerState>(init.initState);
    const stateSetter = (update: Partial<QuestionAnswerState>) => {
        setState({...state, ...update});
    };

    return (
        <>
            <Paper elevation={2}>
                <QuestionAnswer init={init} state={state} stateSetter={stateSetter} />
            </Paper>
            <Paper elevation={2}>
                <p>state: {JSON.stringify(state)}</p>
            </Paper>
        </>
    );
};

export const QuestionAnswerTest: FC = () => {
    return (
        <>
            <QuestionAnswerTestLayout />
        </>
    );
};

export const testQuestionAnswerInit = () => {
    const sessionDataDto: SessionDataDto = {} as SessionDataDto;
    sessionDataDto.featureSets = {global: {} as FeatureSetDto};
    sessionDataDto.listDefinitions = {};

    const srId = 99;

    const taskName = "generalQuestionnaire";
    const evidenceDef = new EvidenceDef(
        taskName,
        EvidencePageType.questionnaireOnly,
        EvidenceGroup.fromName(taskName),
        undefined
    );

    const data: QuestionAnswerData = {
        sessionData: new SessionData(sessionDataDto),
        snapshot: null,
        questionDefId: 1,
        serviceRecipientId: srId,
        evidenceDef: evidenceDef,
        configResolver: configResolver(),
        getWorkUuid: () => Uuid.randomV4()
    };

    const state: QuestionAnswerState = {
        questionDefId: question1.id,
        answer: null
    };

    const ref: QuestionAnswerInit = {
        initData: data,
        initState: state,
        getWorkUuid: () => Uuid.randomV4(),
        questionDef: question1
    };
    return ref;
};
