import * as React from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>, <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    CardHeader,
    CartesianGrid,
    createStyles,
    Grid, Legend, LinearProgress,
    makeStyles, Menu, MenuItem,
    Theme, Tooltip, Typography,
    XAxis,
    YAxis
} from "@eccosolutions/ecco-mui";
import {ArrowDropDownIcon} from "@eccosolutions/ecco-mui-controls";


//************
// Based on article: https://blog.statsbot.co/material-ui-dashboard-with-react-3fef78061733
// which integrates an independent engine from the db to visuals - cube.js
// but we just take the building of the dashboard
//************


// BARCHART
const data = [
    {
        name: 'Page A', uv: 4000, pv: 2400, amt: 2400,
    },
    {
        name: 'Page B', uv: 3000, pv: 1398, amt: 2210,
    },
    {
        name: '<PERSON> C', uv: 2000, pv: 9800, amt: 2290,
    },
    {
        name: '<PERSON> D', uv: 2780, pv: 3908, amt: 2000,
    },
    {
        name: '<PERSON> E', uv: 1890, pv: 4800, amt: 2181,
    },
    {
        name: '<PERSON> F', uv: 2390, pv: 3800, amt: 2500,
    },
    {
        name: 'Page G', uv: 3490, pv: 4300, amt: 2100,
    },
];

const useStylesBar = makeStyles(() => ({
    root: {},
    headerButton: {
        letterSpacing: '0.4px',
    },
    chartContainer: {
        position: "relative",
        padding: "19px 0"
    }
}));

const BarChartWrapper = (props: any) => {
    const { className, ...rest } = props;
    const classes = useStylesBar();
    const [anchorEl, setAnchorEl] = React.useState(null);
    const defaultDates = ['This week', 'This month', 'Last 7 days', 'Last month'];
    const handleClick = (event: any) => {
        setAnchorEl(event.currentTarget);
        console.log("click");
    };
    const handleClose = (date: any) => {
        //props.setDateRange(date);
        setAnchorEl(null);
        console.log("close")
    };
    return (
            <Card {...rest} className={classes.root}>
                <CardHeader title="Latest xxx" action={
                    <div>
                        <Button
                                className={classes.headerButton}
                                size="small"
                                variant="text"
                                aria-controls="simple-menu"
                                aria-haspopup="true"
                                onClick={() => handleClick}
                        >
                            last 3 months <ArrowDropDownIcon />
                        </Button>
                        <Menu
                                id="simple-menu"
                                anchorEl={anchorEl}
                                keepMounted
                                open={Boolean(anchorEl)}
                                onClose={() => handleClose(""/*props.dateRange*/)}
                        >
                            {defaultDates.map((date) => (
                                <MenuItem key={date} onClick={() => handleClose(date)}>{date}</MenuItem>
                            ))}
                        </Menu>
                    </div>
                }
                />
                <CardContent>
                    <div className={classes.chartContainer}>
                        <BarChart
                                width={500}
                                height={300}
                                data={data}
                                margin={{
                                    top: 5, right: 30, left: 20, bottom: 5,
                                }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="pv" fill="#8884d8" />
                            <Bar dataKey="uv" fill="#82ca9d" />
                        </BarChart>
                    </div>
                </CardContent>
            </Card>
    )
};

// BADGE
// react-countup
const useStylesKPI = makeStyles((theme) => ({
    root: {
        height: '100%',
    },
    content: {
        alignItems: 'center',
        display: 'flex',
    },
    title: {
        fontWeight: 500,
    },
    progress: {
        marginTop: theme.spacing(3),
        height: '8px',
        borderRadius: '10px',
    },
    difference: {
        marginTop: theme.spacing(2),
        display: 'flex',
        alignItems: 'center',
    },
    differenceIcon: {
        color: theme.palette.error.dark,
    },
    differenceValue: {
        marginRight: theme.spacing(1),
    },
    green: {
        color: theme.palette.success.dark,
    },
    red: {
        color: theme.palette.error.dark,
    },
}));

const KPIChart = (props:any): any => {
    const classes = useStylesKPI();
    const { title, value, difference, progress, duration, ...rest } = props;
    /*
    const { className, title, progress, query, difference, duration, ...rest } = props;
    const { resultSet, error, isLoading } = useCubeQuery(query);
    const differenceValue = useCubeQuery(differenceQuery);
    if (isLoading || differenceValue.isLoading) {
        return (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress color="secondary" />
                </div>
        );
    }
    if (error || differenceValue.error) {
        return <pre>{(error || differenceValue.error).toString()}</pre>;
    }
    */
    return (
            /*<Card {...rest} className={clsx(classes.root, className)}>*/
            <Card {...rest} className={classes.root}>
                <CardContent>
                    <Grid container justify="space-between">
                        <Grid item>
                            <Typography className={classes.title} color="textSecondary" gutterBottom variant="body2">
                                {title}
                            </Typography>
                            <Typography variant="h4">
                                {/*<CountUp
                                        end={fullValue}
                                        duration={duration}
                                        separator=","
                                        decimals={0}
                                />*/}
                                {value}
                            </Typography>
                        </Grid>
                    </Grid>
                    {progress ? (
                            <LinearProgress
                                    className={classes.progress}
                                    value={Number(value)}
                                    variant="determinate"
                            />
                    ) : null}
                    {difference ? (
                            <div className={classes.difference}>
                                <Typography className={classes.differenceValue} variant="body2">
                                    {Number(value) > 1 ? (
                                            <span className={classes.green}>{value.toFixed(1)}%</span>
                                    ) : (
                                            <span className={classes.red}>{value.toFixed(1)}%</span>
                                    )}
                                </Typography>
                                {/*<Typography className={classes.caption} variant="caption">*/}
                                <Typography variant="caption">
                                    since last month
                                </Typography>
                            </div>
                    ) : null}
                </CardContent>
            </Card>
    );
};

// DASHBOARD
const cards = [{title: 'referrals', value: 100, difference: -10, progress: false, duration: 1.5},
    {title: 'tasks', value: 340, difference: -10, progress: true, duration: 1.5},
    {title: 'item 3', value: 140, difference: -10, progress: false, duration: 1.5},
    {title: 'item 4', value: 40, difference: -10, progress: false, duration: 1.5}];

const useStylesMain = makeStyles((theme: Theme) => // NOTE: Must always have root:
        createStyles({
            root: {
                backgroundColor: 'lightgrey',
                padding: '5px'
            }
        })
);
export const DashboardDemo = () => {
    const classes = useStylesMain();
    return <>
            <div>
                <Grid className={classes.root} container spacing={4}>
                    {cards.map((item, index) => {
                        return (
                                <Grid key={item.title + index} item lg={3} sm={6} xl={3} xs={12}>
                                    <KPIChart {...item}/>
                                </Grid>
                        )
                    })
                    }
                    <Grid item lg={8} md={12} xl={9} xs={12}>
                        <BarChartWrapper/>
                    </Grid>
                    <Grid item lg={4} md={6} xl={3} xs={12}>
                        {/*<BarChartWrapper/> donut*/}
                    </Grid>
                </Grid>
            </div>
    </>
};
