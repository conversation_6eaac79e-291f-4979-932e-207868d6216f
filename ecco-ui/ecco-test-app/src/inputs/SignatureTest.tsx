import * as React from "react";
import {ScribbleBackgroundCanvas} from "ecco-components/inputs/ScribbleBackgroundCanvas";
import front from "ecco-components/images/body-front.png"
import back from "ecco-components/images/body-back.png";
import {Button, Grid} from "@eccosolutions/ecco-mui";
import {useMemo, useRef, useState} from "react";
import {ScribbleView} from "ecco-components/inputs/ScribbleView";
import {TabsBuilder} from "ecco-components";
import {useStorage} from "ecco-components-core/hooks";

export const SignatureTest = () => {
    const canvasFront = useRef<SignaturePad>();
    const canvasBack = useRef<SignaturePad>();
    const [frontPng, setFrontPng] = useState<string | null>(null);
    const [backPng, setBackPng] = useState<string | null>(null);
    const [savedFront, setSavedFront] = useStorage("sessionStorage", "front", "");
    const [savedBack, setSavedBack] = useStorage("sessionStorage", "back", "");

    const frontScribble = useMemo(
        () => (
            <ScribbleBackgroundCanvas
                key="front"
                padRef={canvasFront}
                content={savedFront}
                setContent={setSavedFront}
            >
                <img src={front} width={298} height={489} alt="Diagram of human body, anterior" />
            </ScribbleBackgroundCanvas>
        ),
        [savedFront, setSavedFront]
    );
    const backScribble = useMemo(
        () => (
            <ScribbleBackgroundCanvas
                key="back"
                padRef={canvasBack}
                content={savedBack}
                setContent={setSavedBack}
            >
                <img src={back} width={298} height={489} alt="Diagram of human body, posterior" />
            </ScribbleBackgroundCanvas>
        ),
        [savedBack, setSavedBack]
    );
    const tabs = new TabsBuilder()
        .addTab("front", () => frontScribble)
        .addTab("back", () => backScribble)
        .build();
    return (
        <Grid container>
            <Grid item>{tabs}</Grid>
            <Grid item>
                <Button
                    color="primary"
                    onClick={() => {
                        setFrontPng(canvasFront.current?.toDataURL() ?? savedFront);
                        setBackPng(canvasBack.current?.toDataURL() ?? savedBack);
                        setSavedFront(""); // TODO Doesn't clear existing scribble
                        setSavedBack("");
                    }}
                >
                    save -&gt;
                </Button>
            </Grid>
            <Grid item>
                <ScribbleView scribbleUrl={frontPng}>
                    <img
                        src={front}
                        width={298}
                        height={489}
                        alt="Diagram of human body, anterior"
                    />
                </ScribbleView>
            </Grid>
            <Grid item>
                <ScribbleView scribbleUrl={backPng}>
                    <img
                        src={back}
                        width={298}
                        height={489}
                        alt="Diagram of human body, posterior"
                    />
                </ScribbleView>
            </Grid>
            <Grid>
                <ScribbleView scribbleUrl={frontPng} width={298} height={489} />
            </Grid>
        </Grid>
    );
};