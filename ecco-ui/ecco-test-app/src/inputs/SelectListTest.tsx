import * as React from "react";
import {SelectList} from "ecco-components-core/SelectList";
import {useState} from "react";

interface OptType { label: string; value: number }

export const SelectListTest = () => {
    const [week, setWeek] = useState<number | null>();
    const options: OptType[] = [1, 2, 3, 4].map(o => {return {label: o.toString(), value: o}});
    return (
        <>
            <SelectList
                placeholder={"every (weeks)"}
                createNew={false}
                isClearable={true}
                options={options}
                value={week ? options.filter(o => o.value === week).pop() : undefined}
                onChange={v => setWeek(v ? (v as OptType).value : null)}
            />
            <p>Select value: {JSON.stringify(week)}</p>
        </>
    );
};