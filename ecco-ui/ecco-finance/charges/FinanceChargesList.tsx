import {createStyles, Grid, makeStyles, Theme} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC} from "react";
import {DataTable, useFinanceCharges} from "ecco-components";
import {MUIDataTableColumnDef} from "mui-datatables";
import {InvoiceLineDto} from "ecco-dto";

export const FinanceChargesList: FC<{serviceRecipientId: number}> = props => {
    const classes = useStyles();
    const {charges} = useFinanceCharges(props.serviceRecipientId);

    if (!charges) {
        return null;
    }

    return (
        <Grid item={true} xs={12}>
            <div className={classes.tableWrapper}>
                <DataTableWrapper data={charges} />
            </div>
        </Grid>
    );
};

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        filterElement: {
            margin: 20
        },
        button: {
            margin: 20
        },
        table: {
            verticalAlign: "top",
            minWidth: 800,
            width: "100%"
        },
        tableWrapper: {
            overflowX: "auto"
        },
        tableCell: {
            padding: "5px"
        }
    })
);

const DataTableWrapper: FC<{
    data: InvoiceLineDto[];
}> = ({data}) => {
    const columns: MUIDataTableColumnDef[] = [
        {label: "amount", name: "netAmount", options: undefined},
        {label: "description", name: "description", options: undefined}
    ];

    return <DataTable title={"charges"} columns={columns} data={data} toolbar={() => {}} />;
};
