// This should be specified in Configurations -> Templates -> Jest as the config file
module.exports = {
    testEnvironment: "jsdom",
    testEnvironmentOptions: {
        // url: 'https://jestjs.io/',
    },
    transform: {
        "^.+\\.tsx?$": [
            "ts-jest",
            {
                tsconfig: "tsconfig.json"
            }
        ]
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(tsx?)$",
    setupFiles: ["./__tests__/setupJest.ts"],
    moduleFileExtensions: ["ts", "tsx", "js", "json", "node"],
    moduleNameMapper: {
        bowser: "<rootDir>/__mocks__/bowser"
    },
    modulePaths: ["<rootDir>"],
    // collectCoverage: true,
    // mapCoverage: true
};
