import {UserSessionDto} from "./UserSessionDto";
import {UserSessionRepository} from "./UserSessionRepository";
import {AuthenticationException} from "./security/AuthenticationException";
import {RECENT_USER_CHANGE} from "ecco-dto";

const KEY = "offline-session";

export class SessionStorageUserSessionRepository implements UserSessionRepository {

    public findUserSession(): Promise<UserSessionDto> {
        const session = sessionStorage.getItem(KEY);
        if (session) {
            return Promise.resolve(JSON.parse(session));
        }
        else {
            return Promise.reject(new AuthenticationException("No offline session found"));
        }
    }

    public login(username: string, credentialsKey: string, userDeviceId: string): Promise<UserSessionDto> {
        sessionStorage.setItem(RECENT_USER_CHANGE, username)
        const userSession: UserSessionDto = {
            username: username,
            credentialsKey: credentialsKey,
            userDeviceId: userDeviceId
        };

        sessionStorage.setItem(KEY, JSON.stringify(userSession));

        return Promise.resolve(userSession);
    }

    public logout(): Promise<void> {
        sessionStorage.removeItem(KEY);
        return Promise.resolve();
    }
}
