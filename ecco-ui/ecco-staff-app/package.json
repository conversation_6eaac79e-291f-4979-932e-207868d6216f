{"name": "ecco-staff-app", "version": "0.1.0", "private": true, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "0.0.0", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.11", "@types/react-router-dom": "^5.1.7", "ecco-components": "0.0.0", "ecco-components-core": "0.0.0", "ecco-dto": "0.0.0", "ecco-offline": "0.0.0", "ecco-rota": "0.0.0", "ecco-spa-global": "0.0.0", "font-awesome": "^4.3.0", "jquery": "3.6.0", "jquery-migrate": "3.3.2", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0"}, "devDependencies": {"@jest/globals": "29.7.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/user-event": "^12.1.10", "ecco-webpack-config": "0.0.0", "eslint": "^7.14.0", "eslint-config-react-app": "^6.0.0", "rimraf": "5.0.10", "typescript": "5.8.3", "workbox-core": "^5.1.4", "workbox-expiration": "^5.1.4", "workbox-precaching": "^5.1.4", "workbox-routing": "^5.1.4", "workbox-strategies": "^5.1.4"}, "scripts": {"analyze": "ecco-webpack --env visualize", "build": "ecco-webpack", "clean": "<PERSON><PERSON><PERSON> build", "emit": "ecco-webpack", "fix": "tsc -p src -p service-worker && eslint --fix src service-worker", "lint": "tsc -p src -p service-worker && eslint src service-worker", "lint-strict": "tsc -p src -p service-worker && eslint --max-warnings 0 src service-worker", "start": "ecco-webpack serve --env publicUrl=/ --open", "start-headless": "ecco-webpack serve --env publicUrl=/ --env clientEnv.REACT_APP_REMOTE_ROOT=http://localhost:8899", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Should be ecco-jest but no tests", "test-sequential": "echo Nothing to do"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}