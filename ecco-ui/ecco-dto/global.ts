import {ApiClient} from "./web-api";
const {applicationRootPath, remoteHost} = window.applicationProperties
import {isOffline} from "./offline/offline-state";

let globalApiClient: ApiClient | null = null;

export function setGlobalApiClient(eccoAPI: ApiClient, override = false) {
    if (globalApiClient && !override) {
        console.info("setGlobalApiClient() call ignored as a client is already set");
        return;
    }
    globalApiClient = eccoAPI;
}

/** @deprecated For React components you should be using useServicesContext() or withServicesContext to wrap old style code */
export function getGlobalApiClient() {
    if (!globalApiClient) {
        const apiUrl = new URL(`${applicationRootPath}api/`, remoteHost || window.location.href).href;
        globalApiClient = new ApiClient(apiUrl, () => Promise.reject("Cannot login using this ApiClient"), isOffline,
            {attemptReAuth: false});
        // Should call setGlobalApiClient first - this could just be `import "ecco*components"` to get environment.ts initialised
        console.error(
            "           ** ERROR: AUTH FAIL WILL NOT SHOW LOGIN FORM ** \n\n getGlobalApiClient is setting a default client which requires pre-existing auth.\n\n" +
                "Do not ignore this message.\n\n" +
                "Find the source and ensure we're not calling getGlobalApiClient() as part of loading .js (e.g. in a static instance)\n\n"
        );
    }

    return globalApiClient;
}
