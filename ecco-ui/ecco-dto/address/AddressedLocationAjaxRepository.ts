
import {AddressedLocationRepository} from "./AddressedLocationRepository"
import {Address} from "../contact-dto";
import {ApiClient} from "../web-api";


export class AddressedLocationAjaxRepository implements AddressedLocationRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findOneAddress(addressId: number): Promise<Address> {
        return this.apiClient.get<Address>(`addresses/${addressId}/`);
    }

    public findAllAddressByIds(addressIds: number[]): Promise<Address[]> {
        return this.apiClient.get<Address[]>("addresses/byIds/",
            {query: {ids: addressIds.join(',')}}
        );
    }

    public findAllAddressByPostCode(postcode: string): Promise<Address[]> {
        return this.apiClient.get<Address[]>("addresses/", {query: {postcode}});
    }

    public saveAddress(address: Address): Promise<any> {
        return this.apiClient.post<any>("addresses/", address);
    }

}
