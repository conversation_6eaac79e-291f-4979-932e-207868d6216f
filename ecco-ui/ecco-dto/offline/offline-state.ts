import * as bowser from "bowser"
// TODO error without hard reload
const {applicationRootPath} = window.applicationProperties

// Are we on a page we know is a fallback page?
const forceOffline = document.querySelector("html.offline-only") !== null;

// remove app root path (e.g. /uat/ from the path part of the URL
const relativePath = window.location.pathname.substr(applicationRootPath.length);
const pathParts = relativePath.split('/');

const _isOffline = forceOffline
    || pathParts[0] == 'offline'
    || pathParts[2] == 'login.html' // special case pages that need to do offline stuff (or will all pages be special!)
    || pathParts[2] == 'logoutSuccess.html'
    || pathParts[3] == 'manage'
    || pathParts[2] == 'offline-debug.html';

// Firefox has supported events on network connection since v41
export const canDetectOnline = navigator.onLine !== undefined && (bowser.chrome || bowser.chromium || bowser.firefox);

/** Detect if we are working offline either intentionally or because we have no network connection */
export function isOffline() {
    const allowAutoOffline = localStorage["allowAutoOffline"] == '"y"'; // NOTE: It's stored as JSON.stringify() when we use useLocalStorage()
    // we are offline if we are of the correct url, or we can detect we are offline
    return _isOffline ||  isForcedOffline() || canDetectOnline && !navigator.onLine
        && (window.location.hostname !== "localhost" || allowAutoOffline); // so we can switch wifi off e.g. on train
}

/** Detect if this is an offline page or not irrespective of whether we detect navigator.onLine */
export function isOfflinePage() {
    return _isOffline;
}

const key = "force-offline-db";
export function toggleForceOffline() {
    if (sessionStorage.getItem(key)) {
        sessionStorage.removeItem(key);
    }
    else {
        sessionStorage.setItem(key, "y");
    }
}

export function isForcedOffline() {
    return sessionStorage.getItem(key) !== null;
}

