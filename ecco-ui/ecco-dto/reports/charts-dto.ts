import {IdSourceKey, RelatedEntitiesKey} from "./ReportCriteriaDto";

/**
 * This maps from our json report definition for the "columns" we want to show
 * as defined in "stages" -> "tableRepresentation" (for "stageType": "TABLE") -> "className", "columns"
 */
export interface ColumnSpecification {
    /** Key for column representation to use */
    representation: string;
    /** Override title */
    title: string;
    /** Override property path */
    path: string[];
}
export type ColumnSelector = string | ColumnSpecification;

/**
 * The options that can be defined in a report and requested from the server.
 * Some can be changed by the user. Such properties are:
 *      serviceId
 *      from / to
 *      referralStatus
 *
 * The hierarchy of settings that is currently obeyed is:
 *
 "  ServiceRecipient (produces SequenceAnalysis<ServiceRecipientDemandDto>)
 *      serviceRecipientFilter - eg "buildings:1002"
 *      from
 *      to
 *
 *  Referral (produces SequenceAnalysis<ReferralAggregate>)
 *      selectionPropertyPath - the main data to operate the query on (eg referrals / evidence)
 *          as blank means receivedDate (see ReferralPredicates.referralPredicate)
 *          as other things gives a hint as what to do in reportDataSourceFactory.ts
 *      selectorType - the type of timeframe to query by (eg byStartOfWeekMonday)
 *          absoluteFromDate - start date for the report, with relativeStartIndex adjustments
 *          absoluteToDate - end date for the report
 *          relativeStartDate - the start date base to count relative indexes from
 *          relativeStartIndex - the period around today (or relativeStartDate) to start
 *          relativeEndIndex - the period to extend for
 *      fetchRelatedEntities - additional data to load
 *          eg "supportWork", "client", "singleValueHistory", "questionnaire"
 *       questionnaireEvidenceGroup - used with fetch entities "questionnaire" to get the right questionnaire data
 *
 */
export interface SelectionCriteriaDto {
    /**
     * GENERIC CRITERIA (non-referral)
     */

    /** 0 = 'current', positive numbers are into future, negative into past */
    relativeStartIndex?: number | undefined;
    /** 0 = 'current', positive numbers are into future, negative into past */
    relativeEndIndex?: number | undefined;

    /*
     * Base date to work out where the relativeStartIndex starts from.
     * eg if a report is ran today and defined with:
     *     relativeStartIndex: 0
     *     relativeEndIndex: 3
     *     relativeStartDate: 2016-01-04
     * then its a 4 weekly report with the current period starting 25th April 2016.
     */
    relativeStartDate?: string | undefined;

    /*
     * Absolute from report date, which moves according to the relativeStartIndex.
     * Probably not useful to persist - also see reports/charts/domain.withDateRange()
     */
    absoluteFromDate?: string | null | undefined;

    /*
     * Absolute to report date, which moves according to the relativeEndIndex.
     * Probably not useful to persist - also see reports/charts/domain.withDateRange()
     * THIS WOULD .. APPEAR .. to be INCLUSIVE, so specify 2 dates the same to get single day
     */
    absoluteToDate?: string | null | undefined;

    /** The root type that will be returned
     *  e.g. GroupSupportActivity, Referral, Worker, User
     *  Currently Referral and ServiceRecipient which outputs SequenceAnalysis<ReferralAggregate>
     *  or SequenceAnalysis<ServiceRecipientDemandDto> respectively
     */
    selectionRootEntity: string;

    /** related entities to also fetch
     *  e.g. ["supportWork", "activityInterest"] */
    fetchRelatedEntities: RelatedEntitiesKey[];

    /**  "byStartOfQuarter", "byStartOfWeekMonday", "byStartOfDay"
     * TODO make this a dateSelectorType and remove 'byReferralStatus' - relying on referralStatus alone
     */
    selectorType?: string | null | undefined;

    /**
     * The property that report dates are applicable for.
     * /referral/ -> use status to determine how dates are used to filter
     *  /supportWork/ -> filter on date of support work
     *  groupActivityDate -> all referrals, but filter groupActivities to date range
     */
    selectionPropertyPath?: string | null | undefined;

    /**
     * Field specification for grouping on server when using grouped analysis query
     * which becomes part of the url, not the criteria
     */
    groupBy?: string | undefined;

    /**
     * The idea that this selectionCriteria uses an alternative
     * source to load data from (but could also use some of the
     * selectionCriteria defined alongside it)
     */
    selectionCriteriaSource?: SelectionCriteriaSource | undefined;

    /** Whether to load the HACT session data.
     * The sessionData of service types etc is always loaded
     * TODO could this not just be fetchRelatedEntities?
     */
    hactSessionData?: boolean | undefined;

    /**
     * A criteria to be applied to the selectionRootEntity.
     * Status of the entity - similar to referralStatus, except meaningful for the entity
     * eg liveAsEnd for a review, means get all the reviews latest status (don't use dates)
     */
    entityStatus?: string | undefined;

    /**
     * FILTERS on the criteria.
     * NB these perhaps shouldn't be hard-coded, and could be:
     *  - [operand, filterPropertyPath, filterPropertyValue]
     */
    /**
     * Filter: e.g. buildings:1234, referrals:all, all
     */
    serviceRecipientFilter: string | null;

    /**
     * Filter: reports (where obeyed) to a specific user
     */
    userId: number | null;
    username: string | null;

    /**
     * Filter: the evidence group name to retrieve for questionnaires involved in this report
     * (we use questionnaire prefix to show this doesn't apply to other work)
     */
    questionnaireEvidenceGroup?: string | string[] | undefined;

    /** Filter the command name to retrieve for audits involved in this report */
    commandNameArr?: string[] | undefined;

    taskDefName?: string | undefined;

    /** The evidence group name to retrieve for support involved in this report (eg supportStaffNotes).
     * If not provided, then 'needs' is assumed.
     */
    supportEvidenceGroup?: string | undefined;

    /**
     * Filter: the evidence group name to retrieve for custom forms involved in this report
     * (we use questionnaire prefix to show this doesn't apply to other work)
     */
    customFormEvidenceGroup?: string | undefined;

    /**
     * REFERRAL CRITERIA
     */

    /**
     * A criteria to be applied to the referral.
     * referralStatus (e.g. "allNoDates", "ongoing", "live" (see ReferralStatusName.java)
     * TODO rename this calculatedReferralStatus - since simple properties should use selectionPropertyPath
     * TODO requires changing server-side, see ReferralStatusName.java
     */
    referralStatus?: string | null | undefined;

    /** To differentiate between the meaning of the status - eg:
     * 1) get referrals who were 'live' during the period (which could bring back the entire database)
     * OR with this setting set:
     * 2) get referrals who were 'live' during the period who were also received during the period */
    newReferralsOnly?: boolean | null | undefined;

    /**
     * Includes the primary referrals - default false
     * So reports (eg export reports) can include the related referrals to a primary referral
     */
    includeRelated?: boolean | undefined;

    /** serviceId where the selectorType supports a serviceId */
    serviceId?: number | null | undefined;

    companyId?: number | undefined;

    clientGroupId?: number | undefined;

    serviceGroupId?: number | undefined;

    /** projectId where the selectorType supports a projectId */
    projectId?: number | null | undefined;

    /** If set, filters for is or is not a child referral. null means get children and parents */
    isChild?: boolean | null | undefined;

    /** The item selected e.g. the id of the ward for a single ward within a district.
     * Refers to Referral.srcGeographicArea */
    geographicAreaIdSelected?: number | null | undefined;

    /** To match on all referrals whose srcGeographicArea's matches an id. This allows us to include all the children
     * of a parent recursively - so all districts and wards within a county. */
    geographicAreaIds?: Array<number> | null | undefined;
}

export interface SelectionCriteriaSource {
    analyserType: string;
    entityIdType: IdSourceKey;
    inheritDatesOfIndex: number;
}

export interface AnalyserConfig {
    analyserType: string;
    analyserFilters?: string[] | undefined;
}

export interface TableRepresentationDto {
    /** Key for column representations defined in predefined-table-representations.ts */
    className: string;
    columns: Array<ColumnSelector>;
    /** eg ["questionGroupId"] - when we know what we want to render */
    columnSourceDefIds: string[];
    renderType: string;
}

export interface MatrixRepresentation {
    /** Key for column representations defined in predefined-table-representations.ts */
    recordRepresentationClassName: string;
    // the matrix determines the columns from the data, but we might want to specify some
    // additional properties such as summary columns, or sorting algorithms
}

export interface AuditRepresentation {}

export interface CalendarEventRepresentation {}

export interface BadgeRepresentation {
    badgeIconCssClasses?: string | undefined;

    /** Key for column representations defined in predefined-table-representations.ts */
    recordRepresentationClassName: string;

    /** defines the value from the table representation that gives the main indicator value */
    mainIndicatorValue: ColumnSelector;

    upIndicatorValue?: ColumnSelector | undefined;
    downIndicatorValue?: ColumnSelector | undefined;

    renderType: string;

    /** target for percentage KPI. If specified, will divide mainIndicatorValue by this value to
     * give a % indicator */
    targetValue?: number | undefined;
}

export interface ReportStageDto {
    /** A description of this stage to be shown with the stage */
    description: string;

    /** What will be used to render this stage - See StageType enum */
    stageType: string;

    analyserConfig?: AnalyserConfig | undefined;

    /** Key for on click analyser from those available */
    selectionAnalyser?: string | undefined; // This fits with a CHART or TABLE report stage

    /** Key for on 'select: many' click analyser from those available */
    selectionAnalyserMany?: string | undefined; // This fits with a CHART or TABLE report stage

    /** Key for on 'select all' click analyser from those available */
    selectionAnalyserAll?: string | undefined; // This fits with a CHART or TABLE report stage

    /** If selection key is specified, then this is used as to automatically open the next report stage
     * as though the user has selected this key */
    selectionKeys?: string[] | null | undefined;

    /** Output of analyser is fed to the dataSeries */
    seriesDefs?: SeriesDefinitionDto[] | undefined; // should be chartRepresentation

    badgeRepresentation?: BadgeRepresentation | undefined;

    tableRepresentation?: TableRepresentationDto | undefined;

    matrixRepresentation?: MatrixRepresentation | undefined;

    auditRepresentation?: AuditRepresentation | undefined;

    /** Tricky yes/no for show of 'select all' link on chart - which we would like to later derive by detecting that
     * input to analysis followed by selectionAnalyser of chart, give us the same Analysis class
     */
    canSkip?: boolean | undefined;

    /** Indication whether to allow click-through interactions - eg, when this stage doesn't have anything after it */
    canClickThrough?: boolean | undefined;

    /** Feature (e.g. "project") that needs to be relevant for this stage to be activated.
     *  In the case of project, if no projects are defined, then project stages should be filtered out */
    activeFor?: string | undefined;
}

/** Matches Java class: com.ecco.webApi.report.ReportDefinitionToViewModel */
export interface ChartDefinitionDto {
    uuid: string;

    /** Name of this chart instance e.g. referrals by worker in current quarter */
    name: string;

    /** User-friendly report name, such as "staff utilisation - monthly" - allowed to be edited by managers */
    friendlyName: string;

    /**
     * Whether this report shows on the manager dashboard
     */
    showOnDashboardManager: boolean;

    /**
     * Whether this report shows on the file dashboard
     */
    showOnDashboardFile: boolean;

    orderby: number;

    deleted?: boolean | undefined;

    userId?: number | undefined;

    definition: {
        /** Long description giving details of exactly what the report covers - only as accurate as the person who
         * wrote the description. */
        description: string;

        selectionCriteria?: SelectionCriteriaDto | null | undefined;
        selectionMultiCriteria?: SelectionCriteriaDto[] | undefined;

        stages: ReportStageDto[];
    };
}

export interface SeriesDefinitionDto {
    label: string;

    /** Path to value within subclass of KeyedResultItem */
    valuePath: string;

    /** e.g. "bar", "line", "point" - defaults to "bar" (ignored for PieChart) */
    renderMode?: string | undefined;

    /** Options used in rendering the stage - JSON expected (see PieChartDataRepresentation) */
    renderOptions?: object | undefined;
}

export interface DashboardDefinition {
    uuid: string;

    name: string;

    /** Uuids of reports to show on this dashboard */
    reportUuids: string[];

    /** Metrics (e.g. 1/10 10% failing [red] badge thingy) */
    // metricUuids: string[];
}
