import {ReferralSummaryDto} from "./referral-dto";
import {HateoasResource, IdNameDisabled} from "@eccosolutions/ecco-common";
import {FormEvidence} from "./evidence-dto";

/**
 * See com.ecco.webApi.groupSupport.DailyAttendanceViewModel
 */
export interface DailyClientAttendanceDto {
    /** ISO-8601 date */
    date: string;
    attending: boolean;
    cancelled: boolean;
    attendedAllDay: boolean;
}

/** The existence of this indicates that the person is invited */
export interface ClientAttendanceDto {
    parentActivity?: GroupActivityDto | undefined;
    serviceRecipientId: number;

    invited: boolean;
    attending: boolean;
    attended: boolean;
    comment?: string | undefined;
    typeId?: number | undefined;
    supportWorkUuid?: string | undefined;
    dailyAttendances: DailyClientAttendanceDto[];

    // populated client side
    referralSummary: ReferralSummaryDto;
    customFormWorkLatest?: FormEvidence<any> | null | undefined;
    // populated client side
}

/* This interface must match the Java class com.ecco.webApi.groupSupport.GroupSupportActivitySummaryRowResource. */
export interface GroupActivityDto extends HateoasResource {
    id: number;
    serviceRecipientId: number;
    uuid: string;
    description: string;
    capacity: number;
    duration: number;
    startDateTime: string; // ISO-8601
    endDate?: string | undefined; // ISO-8601
    activityTypeId: number;
    course?: boolean | undefined;
    parentId?: number | undefined;
    childrenCount?: number | undefined;
    venueId?: number | undefined;
    venueName?: string | undefined;
    serviceId?: number | undefined;
    projectId?: number | undefined;
    clientsInvited?: number | undefined;
    clientsAttending?: number | undefined;
    clientsAttended?: number | undefined;
}

export interface ActivityTypeDto extends IdNameDisabled {
    numReferrals: number; // for use where count of related referrals makes sense
    linkedQuestionGroups: ActivityTypeGroupSupportDto[];
}

export interface ActivityTypeGroupSupportDto {
    questionGroupId: number;
    evidenceGroupKey: string;
}
