import {Agency, Contact, Individual} from "./contact-dto";
import {
    ReferralDto,
    ReferralSummaryDto} from "./referral-dto";
import {ConfigResolver, Service} from "./service-config-domain";
import {
    ServiceRecipient,
} from "./service-recipient-dto";
import {SessionData} from "./session-data/feature-config-domain";


export type AcceptedState = "ACCEPTED" | "SIGNPOSTED" | "UNSET";

/** This matches com.ecco.dto.DelegateResponse */
export interface DelegateResponse<P> {
    payload: P;

    statusCode: number;

    statusText: string;
}

/** React update addon updaters for Referral dto */
export const setReferralReferrerUpdater = (individual: Individual) => ({
    referrerIndividualId: {$set: individual && individual.contactId},
    referrerAgencyId: {$set: individual && individual.organisationId}
});
export const setReferralAgencyUpdater = (agency: Agency) => ({
    referrerAgencyId: {$set: agency && agency.contactId}
});



export type SourceType = 'professional' | 'selfReferral' | 'individual';


export interface ServiceRecipientWithEntities extends ServiceRecipient {
    features: SessionData;
    configResolver: ConfigResolver;
}

export interface ReferralWithEntities extends ReferralDto {
    features: SessionData;
    configResolver: ConfigResolver;
}

export interface ReferralSummaryWithEntities extends ReferralSummaryDto {
    features: SessionData;
    configResolver: ConfigResolver;
}

/** Mask values for setting or testing daysAttending bitfield */
export const DAYS_AS_BITS = { SUN: 1, MON: 2, TUE: 4, WED: 8, THUR: 16, FRI: 32, SAT: 64};

export function isAgency(contact: Contact): contact is Agency {
    return contact.discriminator == "agency" || (<Agency>contact).companyName != undefined;
}
export function isIndividual(contact: Contact): contact is Individual {
    return contact.discriminator == "individual" || (<Individual>contact).firstName != undefined;
}


