import {NumberChangeOptional, StringChangeOptional} from "ecco-dto/command-dto";
import {BaseServiceRecipientCommandDto} from "ecco-dto/evidence-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {LinearTaskHandle} from "ecco-dto/workflow-dto";
import {BaseServiceRecipientTaskUpdateCommand, Operation} from "../referral/commands";


export interface EditTaskCommandDto extends BaseServiceRecipientCommandDto {
    operation: Operation;
    dueDate: StringChangeOptional;
    assignee: StringChangeOptional;
    relevantGroup: NumberChangeOptional;
    completed: StringChangeOptional;
}

/**
 * Linear workflow task - ie TaskStatus with taskHandle {serviceRecipientId}-{taskDefId}-{uuid} where taskDefId is referralAspectId
 * This expects a serviceRecipientId and taskName for the audit, optionally with a taskInstanceUuid.
 * See LinearWorkflowService.java#toHandle.
 */
export class EditTaskCommand extends BaseServiceRecipientTaskUpdateCommand {
    public static TASK_NAME = "taskInstance"; // as per ReferralTaskStatusCommandViewModel

    private assigneeChange: StringChangeOptional;
    private dueDateChange: StringChangeOptional;
    private completedChange: StringChangeOptional;
    private relevantGroupChange: NumberChangeOptional;

    constructor(
        operation: Operation,
        serviceRecipientId: number,
        taskInstanceId: string,
        taskHandleWithInstance: string
    ) {
        super(operation, serviceRecipientId, EditTaskCommand.TASK_NAME);
        this.taskInstanceId = taskInstanceId;
        this.taskHandle = taskHandleWithInstance;
    }

    public static isLinearTaskHandle(taskHandle: string): boolean {
        const parts = taskHandle.split("-");
        return parts.length > 1;
    }

    // See LinearWorkflowService.java#toHandle
    public static fromLinearTaskHandle(taskHandle: string): LinearTaskHandle {
        const parts = taskHandle.split("-");

        return parts.length > 2
            ? {
                  serviceRecipientId: parseInt(parts[0]),
                  taskDefId: parseInt(parts[1]),
                  taskInstanceUuid: parts.slice(2).join("-")
              }
            : {serviceRecipientId: parseInt(parts[0]), taskDefId: parseInt(parts[1])};
    }

    /*
    public changeDescription(from: string, to: string) {
        this.descriptionChange = this.asStringChange(from, to);
        return this;
    }
    */

    public changeDueDate(from: EccoDateTime | null, to: EccoDateTime | null) {
        this.dueDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeCompletion(from: EccoDateTime | null, to: EccoDateTime | null) {
        this.completedChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeAssignee(from: string, to: string) {
        this.assigneeChange = this.asStringChange(from, to);
        return this;
    }

    public changeRelevantGroup(from: number, to: number) {
        this.relevantGroupChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.completedChange != null ||
            this.assigneeChange != null ||
            this.relevantGroupChange != null ||
            this.dueDateChange != null
        );
    }

    public toDto(): EditTaskCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: this.getOperation(),
            serviceRecipientId: this.serviceRecipientId, // required for parent
            taskName: this.taskName, // 'taskInstance' required for parent
            taskInstanceId: this.taskInstanceId,
            taskHandle: this.taskHandle,
            assignee: this.assigneeChange,
            relevantGroup: this.relevantGroupChange,
            completed: this.completedChange,
            dueDate: this.dueDateChange
        };
    }
}
