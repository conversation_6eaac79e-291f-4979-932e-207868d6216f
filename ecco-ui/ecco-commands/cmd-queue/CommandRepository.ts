import {Command} from "../commands";
import { MergeableCommand } from './MergeableCommand';
import {bus, HateoasResource, Result} from "@eccosolutions/ecco-common";

export interface CommandRepository {

    sendCommand<T extends HateoasResource = Result>(command: Command | MergeableCommand): Promise<T | undefined>

    exchangeCommand<T extends HateoasResource>(command: Command | MergeableCommand): Promise<T>
}

export const commandSentResultBus = bus<HateoasResource>();