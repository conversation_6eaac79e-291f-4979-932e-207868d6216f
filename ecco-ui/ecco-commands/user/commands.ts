import {
    asAddedRemoved,
    Boolean<PERSON>hange,
    Mergeable,
    NumberAddedRemoved,
    StringAddedRemoved,
    StringChangeOptional,
    UpdateCommandDto
} from "ecco-dto";
import {Command} from "../commands";
import {Uuid} from "@eccosolutions/ecco-crypto";

export interface UserChangeDto extends UpdateCommandDto {
    userIdSubject?: number;

    username: StringChangeOptional;

    firstName: StringChangeOptional;

    lastName: StringChangeOptional;

    email: StringChangeOptional;

    enabled: BooleanChange;

    groups: StringAddedRemoved;

    mfaRequired: BooleanChange;
}

/**
 * Command to add, remove or update
 */
export class UserChangeCommand extends Command {
    public static discriminator = "core";

    private usernameChange: StringChangeOptional;

    private firstNameChange: StringChangeOptional;

    private lastNameChange: StringChangeOptional;

    private emailChange: StringChangeOptional;

    private enabledChange: BooleanChange;

    private groupsChange: StringAddedRemoved;

    private mfaRequiredChange: <PERSON>oleanChange;

    /** userId is omitted if creating a new one */
    constructor(private operation: "add" | "update" | "remove", private userId?: number) {
        super(
            Uuid.randomV4(),
            [],
            userId ? `users/${userId}/commands/` : "users/-/commands/",
            "userChange"
        );
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    /** Add username change data, but only if to != from */
    public changeUsername(from: string | null | undefined, to: string | null | undefined) {
        this.usernameChange = this.asStringChange(from, to);
        return this;
    }

    public changeFirstName(from: string | null | undefined, to: string | null | undefined) {
        this.firstNameChange = this.asStringChange(from, to);
        return this;
    }

    public changeLastName(from: string | null | undefined, to: string | null | undefined) {
        this.lastNameChange = this.asStringChange(from, to);
        return this;
    }

    /** Note: This may not take immediate effect as it could trigger a workflow to not change the email until
     * the new address has been validated */
    public changeEmail(from: string | null | undefined, to: string | null | undefined) {
        this.emailChange = this.asStringChange(from, to);
        return this;
    }

    public changeEnabled(from: boolean, to: boolean) {
        this.enabledChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeMfaRequired(from: boolean, to: boolean) {
        this.mfaRequiredChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeGroups(from: string[], to: string[]) {
        this.groupsChange = asAddedRemoved(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return (
            this.usernameChange != null ||
            this.enabledChange != null ||
            this.mfaRequiredChange != null ||
            this.firstNameChange != null ||
            this.lastNameChange != null ||
            this.emailChange != null ||
            this.groupsChange != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    protected getCommandDto(): UserChangeDto {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            userIdSubject: this.userId,
            operation: this.operation,
            username: this.usernameChange,
            enabled: this.enabledChange,
            mfaRequired: this.mfaRequiredChange,
            firstName: this.firstNameChange,
            lastName: this.lastNameChange,
            email: this.emailChange,
            groups: this.groupsChange
        };
    }
}

export interface UserAclChangeDto extends UpdateCommandDto {

    userIdSubject: number

    services: NumberAddedRemoved

    projects: NumberAddedRemoved
}

export class UserAclChangeCommand extends Command {
    private servicesChange: NumberAddedRemoved;

    private projectsChange: NumberAddedRemoved;

    constructor(private userId: number) {
        super(Uuid.randomV4(), [], `users/${userId}/commands/acls/`, "userChange");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public changeServices(from?: number[], to?: number[]) {
        this.servicesChange = asAddedRemoved(from, to);
        return this;
    }

    public changeProjects(from?: number[], to?: number[]) {
        this.projectsChange = asAddedRemoved(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.servicesChange != null || this.projectsChange != null;
    }

    protected getCommandDto(): UserAclChangeDto {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: "update",
            userIdSubject: this.userId,
            services: this.servicesChange,
            projects: this.projectsChange
        };
    }
}

export interface UserMfaResetCommandDto extends UpdateCommandDto {
    userIdSubject: number;
}

export class UserMfaResetCommand extends Command {
    public static discriminator = "mfaReset";

    constructor(private userId: number) {
        super(Uuid.randomV4(), [], `users/${userId}/commands/resetMfa/`, "mfaReset");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public override hasChanges(): boolean {
        return true;
    }

    protected getCommandDto(): UserMfaResetCommandDto {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: "reset",
            userIdSubject: this.userId
        };
    }
}