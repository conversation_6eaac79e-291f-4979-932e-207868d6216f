import {Observable} from "rxjs";

export function everyObservablePair<T, U>(
    a: Observable<T>,
    b: Observable<U>,
    predicate: (a: T, b: U) => boolean
): Observable<boolean> {
    const aShared = a.share();
    const bShared = b.share();
    const aEnded = ended(aShared);
    const bEnded = ended(bShared);
    const aCount = runningCount(aShared);
    const bCount = runningCount(bShared);
    const equalLength = Observable.combineLatest(aCount, bCount, aEnded, bEnded)
        .takeWhile(
            ([aCount, bCount, aEnd, bEnd]) =>
                (!aEnd && !bEnd) ||
                (aEnd && !bEnd && bCount <= aCount) ||
                (bEnd && !aEnd && aCount <= bCount)
        )
        .takeLast(1)
        .map(([aCount, bCount]) => aCount === bCount);
    const satisfies = Observable.zip(aShared, bShared).every(([a, b]) => predicate(a, b));
    return Observable.merge(equalLength, satisfies).every(condition => condition);
}

export function ended(observable: Observable<unknown>): Observable<boolean> {
    return Observable.of(false).concat(observable.takeLast(1).mapTo(true));
}

export function runningCount(observable: Observable<unknown>): Observable<number> {
    return Observable.of(0).concat(observable.map((_, i) => i + 1));
}


export function everyPromisedPair<T, U>(
    a: Promise<ReadonlyArray<[string, T]>>,
    b: Promise<ReadonlyArray<U>>,
    predicate: (a: T, b: U) => boolean
): Promise<boolean> {
    return Promise.all([a, b]).then(([aResolved, bResolved]) => {
        const aCount = aResolved.length;
        const bCount = bResolved.length;
        const equalLength = aCount === bCount;
        if (!equalLength) return false;

        for (let i = 0; i < aResolved.length; i++) {
            if (!predicate(aResolved[i][1], bResolved[i])) return false;
        }
        return true;
    });
}
