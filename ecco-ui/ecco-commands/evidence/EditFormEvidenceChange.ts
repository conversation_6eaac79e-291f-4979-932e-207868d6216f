import {Uuid} from "@eccosolutions/ecco-crypto";
import {Change} from "../commands";
import {FormEvidenceWork} from "./domain";
import {JsonPatch} from "ecco-dto/evidence-dto";
import {FormEvidence} from "ecco-dto";
import {apply_patch} from "jsonpatch";

export class EditFormEvidenceChange extends Change<FormEvidenceWork> {
    constructor(formWorkUuid: Uuid, formSnapshotKey: string, jsonPatch: JsonPatch) {
        super(formSnapshotKey, work => {
            const form = work.getDto().form;
            const updatedForm = apply_patch(form, jsonPatch);
            const update: FormEvidence<any> = {...work.getDto(), form: updatedForm};
            return new FormEvidenceWork(formWorkUuid, update, formSnapshotKey);
        });
    }
}
