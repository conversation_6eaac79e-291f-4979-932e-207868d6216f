// noinspection ES6PreferShortImport

import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {EvidenceGroup} from "ecco-dto";
import {FormEvidenceCommandDto, JsonPatch} from "ecco-dto/evidence-dto";
import {EvidenceCommand} from "./EvidenceCommand";
import {CommandEffect} from "../commands";
import {EditFormEvidenceChange} from "./EditFormEvidenceChange";

export class FormEvidenceUpdateCommand extends EvidenceCommand {
    // EvidenceCommand applies the modern effects
    public static discriminator = "formUpdate"; // ?? as per DeleteEvidenceCommand.discriminator

    private readonly serviceRecipientId: number;
    private readonly workUuid: Uuid;
    private readonly workDate: EccoDateTime; // ISO-8601 LocalDateTime  i.e. could be EccoDateTime.nowLocalTime()
    private readonly evidenceGroup: EvidenceGroup;
    private readonly taskName: string;
    private readonly taskHandle: string;
    private readonly jsonPatch: JsonPatch;
    private readonly formDefinitionUuid: string;

    constructor(
        uuid: Uuid,
        serviceRecipientId: number,
        workUuid: Uuid,
        workDate: EccoDateTime,
        evidenceGroup: EvidenceGroup,
        taskName: string,
        taskHandle: string,
        jsonPatch: JsonPatch,
        formDefinitionUuid: string
    ) {
        const effects: CommandEffect<any>[] = [
            new EditFormEvidenceChange(uuid, `${serviceRecipientId}-${taskName}`, jsonPatch)
        ];

        const uri = `service-recipients/${serviceRecipientId}/evidence/form/${evidenceGroup.name}/${taskName}/`;

        super(uuid, effects, uri, FormEvidenceUpdateCommand.discriminator);

        this.serviceRecipientId = serviceRecipientId;
        this.workUuid = workUuid;
        this.workDate = workDate;
        this.evidenceGroup = evidenceGroup;
        this.taskName = taskName;
        this.taskHandle = taskHandle;
        this.jsonPatch = jsonPatch;
        this.formDefinitionUuid = formDefinitionUuid;
    }

    public static create(
        serviceRecipientId: number,
        workUuid: Uuid,
        workDate: EccoDateTime,
        evidenceGroup: EvidenceGroup,
        taskName: string,
        taskHandle: string,
        jsonPatch: JsonPatch,
        formDefinitionUuid: string
    ) {
        return new FormEvidenceUpdateCommand(
            Uuid.randomV4(),
            serviceRecipientId,
            workUuid,
            workDate,
            evidenceGroup,
            taskName,
            taskHandle,
            jsonPatch,
            formDefinitionUuid
        );
    }

    public static fromDto(dto: FormEvidenceCommandDto): FormEvidenceUpdateCommand {
        return new FormEvidenceUpdateCommand(
            Uuid.parse(dto.uuid),
            dto.serviceRecipientId,
            Uuid.parse(dto.workUuid),
            EccoDateTime.parseIso8601(dto.workDate!!.to)!!,
            EvidenceGroup.fromName(dto.evidenceGroup),
            dto.taskName!!,
            dto.taskHandle!!,
            dto.jsonPatch,
            dto.formDefinitionUuid
        );
    }

    public override hasChanges() {
        const empty = Array.isArray(this.jsonPatch) && this.jsonPatch.length == 0;
        return !empty;
    }

    protected getCommandDto(): FormEvidenceCommandDto {
        return {
            commandName: FormEvidenceUpdateCommand.discriminator,
            commandUri: this.commandUri,
            uuid: this.uuid.toString(),
            operation: "add", // Possibly allow this to be specified. "update" would be to amend this patch (not sure how we do that!)
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            evidenceGroup: this.evidenceGroup.name,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            workUuid: this.workUuid.toString(),
            workDate: {from: null, to: this.workDate.formatIso8601()}, // TODO: pull this out as a StringChange
            jsonPatch: this.jsonPatch,
            formDefinitionUuid: this.formDefinitionUuid
        };
    }
}
