package com.ecco.web.upload;

// value object (immutable)
public final class UploadErrorResult {

    public static UploadErrorResult error(String filename, String type, long size, String error) {
        return new UploadErrorResult(filename, type, size, error);
    }
    public static UploadErrorResult error(String error) {
        return new UploadErrorResult(error);
    }

    private UploadErrorResult(String filename, String type, long size, String error) {
        this.filename = filename;
        this.type = type;
        this.size = size;
        this.message = error;
    }
    private UploadErrorResult(String message) {
        this.message = message;
        this.errorGlobal = true;
        //this.information = !error;
    }

    private String filename;
    private String type;
    private long size;
    private String message;
    //private boolean information = false;
    private boolean errorGlobal = false;

    public String getFilename() {
        return filename;
    }
    public String getType() {
        return type;
    }
    public long getSize() {
        return size;
    }

    public String toJson() {
        if (errorGlobal)
            return "{\"error\":\"" + message + "\"}";
        else
            return "{\"filename\":\"" + getFilename() + "\",\"type\":\"" + getType() + "\",\"size\":\"" + getSize() + "\",\"error\":\"" + message + "\"}";
    }

}
