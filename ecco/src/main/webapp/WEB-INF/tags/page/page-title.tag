<%@tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@attribute name="title" type="java.lang.String" required="false"%>
<%@attribute name="titleMessageKey" type="java.lang.String" required="false"%>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="spring" uri="http://www.springframework.org/tags" %>

<title>
    <spring:message code="domain.friendlyName"/>
    <c:choose>
        <c:when test="${not empty titleMessageKey}">
            - <fmt:message key="${titleMessageKey}"/>
        </c:when>
        <c:when test="${not empty title}">
            - <c:out value="${title}"/>
        </c:when>
    </c:choose>
</title>
