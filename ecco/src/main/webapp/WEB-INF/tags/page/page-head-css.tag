<%@tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@attribute name="setTableStyles" type="java.lang.Boolean" required="false"%>

<%@include file="/WEB-INF/views/includes.jsp"%>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>

<c:if test="${setTableStyles}">
    <style type="text/css">
        <!--
        table.view th {
            text-align: center;
        }

        table.view td {
            text-align: center;
        }

        table.view td {
            border: 1px solid #878787;
            padding: 5px;
        }

        -->
    </style>
</c:if>


<%-- themed css - at the end to overwrite anything included above (eg fullcalendar) --%>
<link rel="stylesheet" href="${cssBase}/main-styles.css">
<link rel="stylesheet" href="${cssBase}/jqueryui/jqueryui.css" type="text/css" media="screen, projection">
<link rel="stylesheet" href="${cssBase}/jqueryui/jquery-popbox.css" type="text/css" media="screen, projection">


