<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>
<%@include file="/WEB-INF/views/reference-data.jsp" %>

<div class="clearFind">
    <c:if test="${searchOn == 'true'}">
        <spring:url var="tmp" value="${viewEntityUrl}"><spring:param name="searchOff" value="true"/></spring:url>
        <a href="${tmp}"><spring:message code="clearsearch"/></a>
    </c:if>
</div>

<div style="display: none;">
(<a href="#" onclick="jQuery('#commandBody').text('');")>clear</a>):
<span id="commandBody"></span>
</div>

<table class="view">

<tr class="view-header">
<th>id</th>
<th>submitted</th>
<th>by</th>
<th>key</th>
<th>command</th>
<th>status</th>
<th>action</th>
</tr>

<c:forEach var="entity" items="${entities}" varStatus="loopIndex">
    <tr>
        <td>
            <c:out value="${entity.id}"/>
        </td>
        <td>
            <c:out value="${medFn:userFriendlyDateTimeFromLocal_usingJodaDateTime(entity.queueTime)}"/>
        </td>
        <td>
            <c:out value="${entity.userDevice.user.username}"/>
        </td>
        <td>
            <c:out value='${entity.valid? "valid" : "invalid"}'/>
        </td>
        <td>
            <a href="#" onclick="popup(${entity.id})"><c:out value='${entity.commandRequest.method} ${entity.commandRequest.url}'/></a>
        </td>
        <td>
            <fmt:message key="command_queue.status.${entity.status}"/>
        </td>
        <td>
            <form class='actionHidden' method="POST" action="<c:url value="/api/commands/${entity.id}"/>" style="display: none;">
                <c:if test="${entity.dismissable}">
                    <input class="button" type="submit" name="action" value="dismiss"/>
                </c:if>
                <c:if test="${entity.executable}">
                    <input class="button" type="submit" name="action" value="execute"/>
                </c:if>
                <c:if test="${entity.archivable}">
                    <input class="button" type="submit" name="action" value="archive"/>
                </c:if>
            </form>
        </td>
    </tr>
</c:forEach>

</table>

<script type="text/javascript">
    function popup(id) {
        require(["jquery"], function(jQuery){
            jQuery.ajax({
                type: 'GET',
                url: "${applicationProperties.applicationRootPath}api/commands/"+id+"/",
                processData: false,
                contentType: "application/json",
                dataType: 'json',
                success: function(data) {jQuery('#commandBody').text(data.commandRequest.body); jQuery("#commandBody").parent().show()},
                error: function (jqXHR, textStatus, errorThrown) {
                    <%-- TODO: Better error handling --%>
                }
            });
        });
    }
    require(["jquery"], function(jQuery){
        jQuery("form.actionHidden>input[type='submit']:not(:last-child)").after(" | ");
        jQuery("form.actionHidden>input[type='submit']").click(function(ev) {
            jQuery(this).attr("data-submit-clicked", 1);
            return true;
        });
        jQuery("form.actionHidden").show().submit(function(ev) {
            ev.preventDefault();
            var f = jQuery(this),
                submitter = f.find("input[data-submit-clicked='1']")
                    url = f.attr("action");
            submitter.removeAttr("data-submit-clicked");
            var actionData = {
                'dismiss': { 'dismissed': true },
                'execute': { 'executed': true },
                'archive': { 'archived': true },
                'failed': { 'failed': true }
            };
            var reloadFn = function() {
                jQuery("input[name='search']").click();
            };
            var hideRowFn = function() {
                f.closest("tr").children("td").css("text-decoration", "line-through");
                f.closest("tr").hide("slow");
            };
    
            var successFn = {
                'dismiss':
                    <c:choose>
                        <c:when test="${param.includeDismissed}">
                            reloadFn
                        </c:when>
                        <c:otherwise>
                            hideRowFn
                        </c:otherwise>
                    </c:choose>
                ,
                'execute':
                    <c:choose>
                        <c:when test="${param.includeExecuted}">
                            reloadFn
                        </c:when>
                        <c:otherwise>
                            hideRowFn
                        </c:otherwise>
                    </c:choose>
                ,
                'archive':
                    <c:choose>
                        <c:when test="${param.includeArchived}">
                            reloadFn
                        </c:when>
                        <c:otherwise>
                            hideRowFn
                        </c:otherwise>
                    </c:choose>
            };
    
            jQuery.ajax({
                type: 'POST',
                url: url,
                data: JSON.stringify(actionData[submitter.val()]),
                processData: false,
                contentType: "application/json",
                dataType: 'json',
                success: successFn[submitter.val()],
                error: function (jqXHR, textStatus, errorThrown) {
                    <%-- TODO: Better error handling --%>
                }
            });
        })
    });
</script>

<div style="text-align: center;">
    <%@ include file="/WEB-INF/views/content/page.jsp" %>
</div>
