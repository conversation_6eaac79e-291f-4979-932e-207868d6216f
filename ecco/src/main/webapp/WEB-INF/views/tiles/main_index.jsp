<%@page isErrorPage="true" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>
<%@taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>

<%-- session = false is to ensure the jsp doesn't default to creating a session --%>
<%-- the code may have already done so - and has by testing for authentication --%>
<%-- @ page session="false"
--%>

<page:page-doctype/>
<html>
<head>
    <page:page-head-meta/>

<page:page-head applicationProperties="${applicationProperties}"
        titleRaw="${titleRaw}"
        titleMessageKey="${titleMessageKey}"/>

<page:page-head-css setTableStyles="${not empty head}"/>

<script type="text/javascript" src="${scriptsBase}/jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="${scriptsBase}/jquery/jquery-migrate.min.js"></script>

<%@ include file="/WEB-INF/views/tiles/main_head_jsbase.jsp" %>

<c:if test="${not empty head}">
    <c:import url="/WEB-INF/views/heads/${head}.jsp"/>
</c:if>
</head>

<body data-session-len="${empty sessionLength ? 0 : sessionLength}">
<div id="header" style="margin: 15px 70px 25px">
    <div id="block-top" class="blurBlock">
        <div id="block-top_wrapper" style="margin-left: 0;">
            <page:page-top-menus/>
        </div>
    </div>
</div>

<div id="content" style="margin-right: 0; margin-top: -0px;">

<div style="margin: 0 auto; width: 100%;">
    <div style="width: 10%; float: left;">&nbsp;
    </div>
    <div style="width: 80%; float: left;">
        <div style="text-align: center; padding-top: 5px;" class="nohover">
            <a href="${urlServletBase}<fmt:message key="home.url"/>"><img src="${imagesBase}/logo_index.png" width="252" height="70" alt="ecco" /></a>
        </div>
    </div>
    <div class="clearing">&nbsp;</div>
</div>
<div style="text-align: center; font-style: italic; margin-top:0.4em;">
<%--taking care of your outcomes--%>
evidenced client centred outcomes
</div>

    <c:if test="${empty contentNotBoxed || !contentNotBoxed}">
        <div style="width: 80%; margin: 0 auto;">
            <page:page-content titleRaw="${noTitle ? null : titleRaw}"
                    titleMessageKey="${noTitle ? null : title}"
                    error="${type == 'error'}"
                    errorMessageKey="${message}"
                    exception="${exception}"
                    uitabs="${uitabs}">
                <jsp:attribute name="menu">
                    <c:if test="${not empty menu}">
                        <c:import url="/WEB-INF/views/menus/${menu}.jsp"/>
                    </c:if>
                </jsp:attribute>
                <jsp:attribute name="actionbar" trim="true">
                    <c:if test="${not empty actionbar}">
                        <c:import url="/WEB-INF/views/content/${actionbar}.jsp"/>
                    </c:if>
                </jsp:attribute>
                <jsp:body>
                    <c:import url="/WEB-INF/views/content/${content}.jsp"/>
                </jsp:body>
            </page:page-content>
        </div>
    </c:if>
    <c:if test="${contentNotBoxed}">
        <div class="focusBlock">
            <c:import url="/WEB-INF/views/content/${content}.jsp"/>
        </div>
    </c:if>

</div>

<div id="footer">
    <page:footer imagesBase="${imagesBase}" footerImageUrl="${footerImageUrl}" organisationName="${organisationName}" showFooterLinks="true"/>
</div>

</body>

</html>
