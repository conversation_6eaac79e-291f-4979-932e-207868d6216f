<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

    <span style="font-size: 1.2em;">
                <span id="sid_clientName"><c:out value="${clientDisplayName}"/></span>
    </span>
<br/>

<c:if test="${not hideKnownDetails}">
    <c:set var="dte" value="-"/>
    <c:if test="${not empty clientBirthDate}">
        <c:set var="dte" value="${medFn:userFriendlyDateFromLocal_usingMed(clientBirthDate)}"/>
    </c:if>
    <span style="font-size: 0.9em;">
        dob: ${dte} (c-id <c:out value="${clientIdCode}"/>)<br/>
        age: ${clientAge} gender: ${clientGender}<br/>
        <c:if test="${not empty contactAddress}">
            address: <c:out value="${contactAddress}"/><br/>
        </c:if>
        <%-- TODO keyCode should appear on emergencyDetails instead of here, and be entered there is appropriate? --%>
        <c:if test="${not empty clientKeyCode}">
            key code: <c:out value="${clientKeyCode}"/><br/>
        </c:if>
        <c:if test="${not empty clientKeyWord}">
            keyword: <c:out value="${clientKeyWord}"/><br/>
        </c:if>
        <c:if test="${not empty preferredContactInfo}">
            contact: ${preferredContactInfo}<br/>
        </c:if>
    </span>
</c:if>
