<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>


<c:if test="${not empty clientIdCode}">
    <div style="text-align: center; margin-top: 5px;">
        <%@ include file="/WEB-INF/views/menus/genericDataClient.jsp" %>
    </div>
</c:if>

<c:if test="${(not empty referralId) && (empty hideKnownDetails)}">
    <div style="text-align: center; margin-top: 5px;">

        <span style="font-size: 0.9em;">

            <c:out value="${referralServiceName}"/> (r-id <span id="sid_rid">${referralIdCode}</span>)<br/>
            <c:if test="${not empty keyWorkerDisplayName}">
                worker: <c:out value="${keyWorkerDisplayName}"/>
            </c:if>
        </span>

    </div>
</c:if>
