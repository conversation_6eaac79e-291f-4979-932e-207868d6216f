package com.ecco.web;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.ServletRequestUtils;

public class CheckedErrorsController extends BasicPageController {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Override
    protected void setReferenceData(ModelMap model, HttpServletRequest request) {
        super.setReferenceData(model, request);

        // during development, checkedError* throw the checkedErrorUnhandled onto the url
        // this simply puts it back into the page
        String[] exceptions = ServletRequestUtils.getStringParameters(request, "exception");
        model.put("exception", exceptions);
    }

}
