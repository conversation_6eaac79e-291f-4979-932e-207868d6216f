package com.ecco.web;

// for v3 validator
public class CustomSpringMessageSourceInterpolator {
}

// for v4 validator (if needed - wasn't configured in)
/*
import java.util.Locale;

import javax.validation.MessageInterpolator;

import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.context.NoSuchMessageException;

// this is redundant if we use ValidationMessages.properties for the annotation message codes
// or if we specify validationMessageSource on the global validation configuration (see app context web-common)
public class CustomSpringMessageSourceInterpolator extends ResourceBundleMessageInterpolator implements MessageInterpolator, MessageSourceAware, InitializingBean {

    @Autowired
    private MessageSource messageSource;

    @Override
    public String interpolate(String messageTemplate, Context context) {
        try {
            return messageSource.getMessage(messageTemplate, new Object[]{}, Locale.getDefault());
        } catch (NoSuchMessageException e) {
            return super.interpolate(messageTemplate, context);
        }
    }

    @Override
    public String interpolate(String messageTemplate, Context context, Locale locale) {
        try {
            return messageSource.getMessage(messageTemplate, new Object[]{}, locale);
        } catch (NoSuchMessageException e) {
            return super.interpolate(messageTemplate, context, locale);
        }
    }

    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void afterPropertiesSet() throws Exception {
        if (messageSource == null) {
            throw new IllegalStateException("MessageSource was not injected, could not initialize "
                    + this.getClass().getSimpleName());
        }
    }

}
*/
