package com.ecco.web.pdf;

import java.awt.Color;
import java.util.Locale;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.springframework.web.servlet.view.document.AbstractPdfView;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.ecco.calendar.core.util.DateTimeUtils;

public class DashboardView extends AbstractPdfView {

    // as copied from WebContentGenerator
    private static final String HEADER_PRAGMA = "Pragma";
    private static final String HEADER_EXPIRES = "Expires";
    private static final String HEADER_CACHE_CONTROL = "Cache-Control";

    private static Font title = new Font(Font.TIMES_ROMAN, 18, Font.BOLD);
    private static Font subTitle = new Font(Font.TIMES_ROMAN, 16, Font.BOLD);
    private static Font smallBoldRed = new Font(Font.TIMES_ROMAN, 12, Font.NORMAL, Color.RED);
    private static Font smallBold = new Font(Font.TIMES_ROMAN, 12, Font.BOLD);

    @Override
    protected void buildPdfDocument(Map model, Document doc, PdfWriter writer, HttpServletRequest request, HttpServletResponse response) throws Exception {

        // see med.web.pdf.PrintOutLabelView
        response.setHeader(HEADER_CACHE_CONTROL, "max-age=0");
        response.setHeader(HEADER_PRAGMA, "private");

        // includes userDisplayName, generatedDateTime, organisationName
        Map data = (Map) model.get("data");

        addMetaData(doc, data);
        addTitle(doc, data);
        addContent(doc, data);
    }

    private void addMetaData(Document doc, Map data) {
        doc.addTitle("ecco dashboard");
        //doc.addSubject("");
        doc.addAuthor((String) data.get("userDisplayName"));
        doc.addCreator("ecco solutions ltd");
    }

    private void addTitle(Document doc, Map data) throws DocumentException {

        Paragraph p = new Paragraph();
        p.setAlignment(Element.ALIGN_CENTER);

        String org = (String) data.get("organisationName");
        p.add(new Paragraph(org + " dashboard", title));
        DateTime start = (DateTime) data.get("start");
        DateTime end = (DateTime) data.get("end");
        String startStr = DateTimeUtils.getFormattedDate(start, Locale.UK);
        String endStr = DateTimeUtils.getFormattedDate(end, Locale.UK);
        p.add(new Paragraph("Period: " + startStr + " - " + endStr, subTitle));

        // DateTimeUtils.getNow(DateTimeZone.forID("Europe/London"))));
        p.add(new Paragraph("Generated at: " + data.get("generatedDateTime")));
        p.add(new Paragraph("Generated by: " + data.get("userDisplayName")));
        addEmptyLine(p, 5);

        doc.add(p);
    }

    private void addContent(Document doc, Map data) throws DocumentException {
        /*
        Anchor anchor = new Anchor("dashboard", title);
        anchor.setName("dashboard");

        // Second parameter is the number of the chapter
        Chapter ch = new Chapter(new Paragraph(anchor), 1);
        Paragraph ps = new Paragraph("referrals", subTitle);
        Section s = ch.addSection(ps);
        */
        Paragraph p = new Paragraph();
        //s.add(p);
        // Add a table
        createTable(p, data);

        doc.add(p);
    }

    private void createTable(Paragraph s, Map data) {
        PdfPTable table = new PdfPTable(2);

        // t.setBorderColor(BaseColor.GRAY);
        // t.setPadding(4);
        // t.setSpacing(4);
        // t.setBorderWidth(1);

        /*
        PdfPCell c1 = new PdfPCell(new Phrase(""));
        c1.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.addCell(c1);
        c1 = new PdfPCell(new Phrase("stat"));
        c1.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.addCell(c1);
        table.setHeaderRows(1);
        */

        Long referrals = (Long) data.get("referrals");
        Long dnas = (Long) data.get("dnas");
        table.addCell("referrals received");
        table.addCell(""+referrals);
        String initAssessments = "initialAssessments";
        table.addCell(initAssessments);
        table.addCell(""+(referrals - dnas));
        String accepted = "referralsAccepted";
        table.addCell(accepted);
        table.addCell(""+data.get("accepted"));
        table.addCell("signposted");
        table.addCell(""+data.get("signposted"));
        table.addCell("clients");
        table.addCell(""+data.get("clientslive"));
        table.addCell("exits prior to achieved");
        table.addCell(""+data.get("existNotOk"));
        table.addCell("successful exits");
        table.addCell(""+data.get("exitsOk"));

        s.add(table);
    }

    private void addEmptyLine(Paragraph paragraph, int number) {
        for (int i = 0; i < number; i++) {
            paragraph.add(new Paragraph(" "));
        }
    }

}
