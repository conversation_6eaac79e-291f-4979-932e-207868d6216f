package com.ecco.webapp.config.root;


import com.ecco.buildings.config.BuildingsConfig;
import com.ecco.calendar.sync.CalendarConfig;
import com.ecco.config.config.ConfigConfig;
import com.ecco.config.contracts.ContractsConfig;
import com.ecco.config.finance.FinanceConfig;
import com.ecco.config.managedvoids.ManagedVoidConfig;
import com.ecco.config.notifications.NotificationConfig;
import com.ecco.config.repairs.RepairConfig;
import com.ecco.contacts.config.ContactsConfig;
import com.ecco.groupsupport.config.GroupSupportConfig;
import com.ecco.hr.config.HrConfig;
import com.ecco.config.incidents.IncidentConfig;
import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.infrastructure.config.root.JmxConfig;
import com.ecco.rota.config.RotaConfig;
import com.ecco.security.config.AclConfig;
import com.ecco.service.config.ServiceConfig;
import com.ecco.serviceConfig.config.MessageSourceConfig;
import com.ecco.serviceConfig.config.ServiceConfigConfig;
import com.ecco.submissions.config.SubmissionConfig;
import com.ecco.upload.config.UploadConfig;
import com.ecco.workflow.activiti.WorkflowConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;


@Configuration
@Import({
    InfrastructureConfig.class, JmxConfig.class, LegacyConfig.class, SecurityConfig.class,
    MessageSourceConfig.class, ServiceConfig.class,
    HrConfig.class, ConfigConfig.class, ServiceConfigConfig.class, SubmissionConfig.class, AclConfig.class,
    WorkflowConfig.class, CalendarConfig.class, UploadConfig.class,
    GroupSupportConfig.class, BuildingsConfig.class, RotaConfig.class, ContractsConfig.class, ContactsConfig.class,
    FinanceConfig.class, IncidentConfig.class, RepairConfig.class, ManagedVoidConfig.class, NotificationConfig.class
})
public class RootConfig {
}
