package com.ecco.dom;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;

import org.h2.Driver;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.cfg.Configuration;
import org.hibernate.service.ServiceRegistry;
import org.junit.Test;

/**
 * Demonstrates a problem in with eagerly retrieved persistset. When eagerly retrieved indirectly
 * the persistent set calls hashcode on items before they are populated and thus retrieves and
 * stores them under an incorrect hashcode making them inaccessible by collection operations that
 * rely on hashcode.
 *
 * <AUTHOR>
 */

// a test from https://hibernate.onjira.com/browse/HHH-3799 (modified only for the h2 driver, and to expect an assertion error)
// which demonstrates the problem using equals/hashcode on items which are not the ids
// (even though hibernate's documentation sugget to use non-id for business 'equals'
// we have to avoid this problem in UserManagementServiceImpl.mergeNewGroups
public class HibernateEagerPersistentSetTest
{

    // if this fails, we could then 'fix' the UserManagementServiceImpl.mergeNewGroups to not avoid this problem
    @Test(expected=AssertionError.class)
    public void test()
    {
        final Long containerId;
        final SessionFactory sf = buildSessionFactory();

        { // populate database with a container
            Session session = sf.openSession();
            Transaction txn = session.beginTransaction();

            Container container = new Container();
            container.items.add(new Item(container, "item1"));
            container.items.add(new Item(container, "item2"));

            session.persist(container);
            txn.commit();
            session.close();

            assertNotNull(container.id);
            containerId = container.id;
        }


        { // this passes, when the container is loaded directly
            Session session = sf.openSession();
            Transaction txn = session.beginTransaction();

            Container container = (Container)session.get(Container.class, containerId);
            assertNotNull(container);

            assertEquals(2, container.items.size());
            assertTrue(container.items.contains(new Item("item1")));
            assertTrue(container.items.contains(new Item("item2")));

            txn.rollback();
            session.close();
        }

        { // this fails, when the container is loaded from query
            Session session = sf.openSession();
            Transaction txn = session.beginTransaction();

            Item item1 = (Item)session.createQuery("FROM Item WHERE name=:name").setParameter(
                    "name", "item1").uniqueResult();

            assertNotNull(item1);

            assertEquals(2, item1.container.items.size());

            /*
             * below line fails because persistentset stores the items under incorrect hashcode
             * which it obtained by calling item#hashcode before item's fields were populated . this
             * happens during the execution of the above query.
             */
            assertTrue(item1.container.items.contains(item1));

            txn.rollback();
            session.close();
        }

    }

    private SessionFactory buildSessionFactory() {
        Properties props = new Properties();
        props.put("hibernate.connection.driver_class", Driver.class.getName());
        props.put("hibernate.connection.url", "jdbc:h2:mem:test;DB_CLOSE_DELAY=-1");
        props.put("hibernate.connection.username", "sa");
        props.put("hibernate.connection.password", "");
        props.put("hibernate.hbm2ddl.auto", "create");
        props.put("hibernate.show_sql", "true");

        Configuration cfg = new Configuration();
        cfg.setProperties(props);
        cfg.addAnnotatedClass(Item.class);
        cfg.addAnnotatedClass(Container.class);

        final ServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder()
                .applySettings(props)
                .build();

        return cfg.buildSessionFactory(serviceRegistry);
    }

    /** simple container for items */
    @Entity(name = "Container")
    static class Container
    {
        @Id
        @GeneratedValue
        Long id;

        @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.PERSIST, mappedBy = "container")
        Set<Item> items = new HashSet<>();
    }

    /** simple item that implements a custom equals/hashcode */
    @Entity(name = "Item")
    static class Item
    {
        @Id
        @GeneratedValue
        Long id;

        @ManyToOne
        Container container;

        String name;

        Item()
        {

        }

        Item(String name)
        {
            this.name = name;
        }

        Item(Container container, String name)
        {
            this.container = container;
            this.name = name;
        }

        @Override
        public int hashCode()
        {
            final int prime = 31;
            int result = 1;
            result = prime * result + ((name == null) ? 0 : name.hashCode());
            return result;
        }

        @Override
        public boolean equals(Object obj)
        {
            if (this == obj)
                return true;
            if (obj == null)
                return false;
            if (getClass() != obj.getClass())
                return false;
            Item other = (Item)obj;
            if (name == null)
            {
                if (other.name != null)
                    return false;
            }
            else if (!name.equals(other.name))
                return false;
            return true;
        }
    }

}
