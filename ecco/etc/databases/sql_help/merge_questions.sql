-- merging questions 
-- <questionId> the question to move to
-- <questionIds> the questions to merge into the questionId
-- <questionIdsAll> all the above

-- find the questions
-- select * from questions where name='';
-- find/check duplicates
select id,questionId,created from supportplananswers where questionId in (<questionIdsAll>);
select id,questionId,created from supportplananswers where questionId in (<questionIdsAll>) group by created having count(*) > 1;
-- delete duplicates
delete from supportplananswers where questionId in (<questionIds>) and id in (select i.id from (select * from supportplananswers where questionId in (<questionIdsAll>) group by created having count(*) > 1) i);
-- update the non-dulicates to all be the same question
update supportplananswers set questionId=<questionId> where questionid in (<questionIds>);
-- unlink
select questiongroupId from questiongroups_questions where questionId in (<questionIds>);
delete from questiongroups_questions where questionId in (<questionIds>);
-- update set
select * from questiongroups_questions where questiongroupId in (<questiongroupId>);
update questiongroups_questions set orderby=orderby-1 where questiongroupId=<questiongroupId> and orderby>x;
