import URI = require("URI");
import {EvidenceGroup} from 'ecco-dto';
import * as commands from "ecco-commands";


export abstract class RiskUpdateCommand  extends commands.BaseUpdateCommand {
   /** operation should be either "add", "remove" or "update" */
    constructor(private operation: string, private serviceRecipientId: number, private riskAreaId: number) {
        super(URI("service-recipients/")
            .segmentCoded(serviceRecipientId.toString())
            .segmentCoded("evidence")
            .segmentCoded(EvidenceGroup.threat.name)
            .segmentCoded("TODO")
            .segmentCoded(riskAreaId.toString())
            .segmentCoded("")
            .toString());
        if (operation != "add" && operation != "remove" && operation != "update") {
            throw new Error("Illegal param 'operation': " + operation);
        }
    }
}
