import {AsyncSessionData, handleLazy} from "ecco-components";
import {EccoTheme} from "ecco-components-core";
import {applicationRootPath} from "application-properties";
import * as React from "react";
import {FC} from "react";
import {<PERSON>rowserRouter} from "react-router-dom";
import {SessionDataService} from "../feature-config/SessionDataService";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";

// NOTE: For Tomcat served pages only, not Webpack
export const RouteManagedPage: FC<{subPath?: string | undefined}> = props => {
    const base = applicationRootPath.substr(0, applicationRootPath.length - 1) + (props.subPath || "");
    console.debug(`BrowserRouter basename=${base}`);
    return handleLazy(
        <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
            <ServicesContextProvider>
                <EccoTheme prefix="rmp">
                    <BrowserRouter basename={base}>
                        {props.children}
                    </BrowserRouter>
                </EccoTheme>
            </ServicesContextProvider>
        </AsyncSessionData>
    );
};