import * as React from "react";
import {ReactElement} from "react";
import {Activity, DemandResource} from "ecco-rota";
import {Grid} from "@eccosolutions/ecco-mui";
import {AllocateWorkerJobButton} from "./AllocateWorkerJobButton";

export interface Props {
    readonly activity: Activity;
    readonly onAllocate: (worker: DemandResource) => void;
    readonly showRecurring: boolean;
}

export function AllocateWorkerGridItem({activity, onAllocate}: Props): ReactElement {
    return <Grid item>
        <AllocateWorkerJobButton activity={activity} onSelection={onAllocate}/>
    </Grid>;
}