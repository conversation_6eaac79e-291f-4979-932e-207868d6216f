<!DOCTYPE html>
<html>
<head>
    <title>editable test</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>
    <link rel="stylesheet" href="../../../css/editable/editable.css"/>
    <link rel="stylesheet" href="../../../font-awesome/css/font-awesome.min.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/editable/editable-test"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">

    </div>

    <div class="entityForm" data-entity-type="referral" data-entity-id="100013">
        <div class="e-row">
            <span>editable text: </span><span id="referralReason" class="editable-text" data-path="referralReason">text</span>
        </div>
        <div class="e-row">
            <span>editable date: </span><span id="referralDate" class="editable-date" data-path="referralDate">20/12/2014</span>
        </div>
    </div>
</body>
</html>
