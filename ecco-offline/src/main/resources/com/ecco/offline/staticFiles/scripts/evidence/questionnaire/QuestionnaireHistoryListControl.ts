import BaseAsyncDialogContent = require("../../controls/BaseAsyncDialogContent");
import $ = require("jquery");
import services = require("ecco-offline-data");
import QuestionnaireHistoryItemControl = require("./QuestionnaireHistoryItemControl");
import * as referralDtos from "ecco-dto";
import {EvidenceGroup, QuestionnaireWorkDto} from "ecco-dto";
import * as dtoEvidence from "ecco-dto/evidence-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {showInModalDom} from "ecco-components-core";
import {QuestionnaireWork} from "ecco-commands";

class BackingData {
    constructor(public referral: referralDtos.ServiceRecipientWithEntities,
                public work: QuestionnaireWorkDto[]) {
    }
}

class QuestionnaireHistoryListControl extends BaseAsyncDialogContent<BackingData> {

    public static showInModal(serviceRecipientId: number, taskName: string) {
        const form = new QuestionnaireHistoryListControl(serviceRecipientId, EvidenceGroup.fromName(taskName));
        // we didn't have a header or footer, so just the form
        showInModalDom("history", form.element()[0]);
        form.load();
    }

    private controls: QuestionnaireHistoryItemControl[];

    constructor(private serviceRecipientId: number,
            private evidenceGroup: dtoEvidence.EvidenceGroup,
            private clientId?: number) {
        super("history");
    }

    protected fetchViewData(): Promise<BackingData> {
        return QuestionnaireHistoryListControl.questionnaireHistoryQ(this.serviceRecipientId, this.evidenceGroup, this.clientId, false, undefined, false);
    }
    public static questionnaireHistoryQ(serviceRecipientId: number, evidenceGroup: dtoEvidence.EvidenceGroup, clientId: number, firstPageOnly: boolean, uuidOnly?: string, attachmentsOnly?: boolean): Promise<BackingData> {
        const referral = services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId);

        const questionnaireWork: Promise<QuestionnaireWorkDto[]> = clientId
            ? referral.then((referral) => {
                            return services.questionnaireWorkAjaxRepository().findQuestionnaireEvidenceByClientIdAndEvidenceGroupKey(
                                    clientId, evidenceGroup);
                        })
            : referral.then((referral) => {
                return services.questionnaireWorkAjaxRepository().findQuestionnaireWorkByServiceRecipientId(serviceRecipientId, evidenceGroup, attachmentsOnly);
            });

        return referral
            .then((referral) => questionnaireWork
                .then((questionnaireWork) => new BackingData(referral, questionnaireWork)));
    }

    protected render(data: BackingData): void {
        const serviceType = data.referral.configResolver.getServiceType();

        var $el = $("<ul>").addClass("entry-list list-unstyled");
        if (data.work.length == 0) {
            $el.append( $("<li>").text("no history recorded") );
        }
        else {
            this.controls = data.work.map((questionnaireWork: QuestionnaireWorkDto) =>
                    new QuestionnaireHistoryItemControl(data.referral,
                        new QuestionnaireWork(Uuid.parse(questionnaireWork.id), questionnaireWork)));
            $el.append(this.controls.map( (control) => control.element() ));
        }
        this.element().empty().append($el);
    }

}
export = QuestionnaireHistoryListControl;
