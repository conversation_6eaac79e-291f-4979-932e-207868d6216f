import {useEffect} from "react";
import {EvidenceLinkEvent} from "ecco-dto";
import EvidenceDelegatingForm from "./EvidenceDelegatingForm";

export function useEvidenceDelegatingEventIntegration() {
    // run on first render, and not again
    // this is to register the link button
    useEffect(() => {
        const handler = (event: EvidenceLinkEvent) =>
                EvidenceDelegatingForm.showInReactModalByIds(event.srId, "", event.taskName);

        // we're mounted several times - in 'support' and 'risk' tabs, but we don't want to open several times
        if (!EvidenceLinkEvent.bus.hasHandlers()) {
            EvidenceLinkEvent.bus.addHandler(handler);
            return () => {
                EvidenceLinkEvent.bus.removeHandler(handler);
            }
        }
        return () => {
        }
    }, [])
}