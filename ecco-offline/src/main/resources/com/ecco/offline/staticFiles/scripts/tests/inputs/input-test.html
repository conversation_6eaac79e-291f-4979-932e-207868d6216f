<!DOCTYPE html>
<html>
<head>
    <title>input values test</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>
    <link rel="stylesheet" href="../../../css/editable/editable.css"/>
    <link rel="stylesheet" href="../../../font-awesome/css/font-awesome.min.css"/>

    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/inputs/input-test"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
    </div>

</body>
</html>
