

/* Date time picker fixes to help with touchscreen */
#ui-datepicker-div {
    font-size: 1.1em;
}
.ui-slider .ui-slider-handle {
    width: 1.8em;
    height: 1.8em;
}
.ui-slider-horizontal {
    height: 1.35em;
}
.ui-timepicker-div dl dd {
    margin-bottom: 20px;
}

/* TODO: Make this the control-specific styling but not utility classes, and
 * rename it rich-client.css */


#offline-menu-grid {
    padding-top: 15px;
}

#offline-menu-grid .col-sm-6 {
    float: right;
}

div.content {
    background-color: rgba(0,0,0,0.02);
    margin: 10px 12px;
    border: 1px #A2B7D8 solid;
    border-radius: 10px;
    padding: 5px 20px;
}


.content th {
    margin-top: 10px;
    background: #e0eeee;
    border-radius: 5px;
    padding: 2px 5px;
    text-align: center;
    font-weight: bold;
}
.content td, .content th {
    padding: 2px 8px;
}

.content dt {
    font-weight: bold;
}

.content dd {
    padding-left: 30px;
}

/*#tab_tasks_4 dt, #tab_tasks_4 dd {*/
.v2 #main-control dt, .v2 #main-control dd {
    line-height: 2em;
}
.v2 #main-control dt {
    overflow: visible;
}
.v2 #main-control dd {
}

/* This will keep save button on the page
.v2.new-layout p.form-footer span {
    position: sticky;
    top: 15px;
    z-index: 1060;
}
*/

.mtrl-ui-disabled input {
    color: rgba(0, 0, 0, 0.870588) !important;
}
