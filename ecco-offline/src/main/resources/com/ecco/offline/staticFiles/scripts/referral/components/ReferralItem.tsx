import * as React from "react";
import { FC, useState } from "react";
import {
    Card,
    CardContent,
    Typography,
    Box,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    Alert
} from "@eccosolutions/ecco-mui";
import { ExpandMore } from "@eccosolutions/ecco-mui-controls";
import { Referrals } from "./ReferralActionsCard";
import { ReferralsWithClient } from "./ReferralActionsCard";

interface ReferralItemProps {
    referrals: Referrals;
    forceOnline?: boolean;
    showErrors?: boolean;
}

/**
 * Modern React component to display a referral item, replacing ReferralSummaryControl
 */
export const ReferralItem: FC<ReferralItemProps> = ({ 
    referrals, 
    forceOnline = false, 
    showErrors = true 
}) => {
    const [expanded, setExpanded] = useState(false);
    
    // Get the first referral for basic info (they're grouped by client)
    const firstReferral = referrals.referrals[0];
    
    // Check for errors (similar to original implementation)
    const errors: any[] = (referrals as any)._errors;
    const hasErrors = showErrors && Array.isArray(errors) && errors.length > 0;

    return (
        <Box mb={2}>
            <Card elevation={2}>
                <Accordion 
                    expanded={expanded} 
                    onChange={() => setExpanded(!expanded)}
                    variant="elevation"
                    elevation={0}
                >
                    <AccordionSummary expandIcon={<ExpandMore />}>
                        <Box width="100%">
                            <Typography variant="h6" component="div">
                                {firstReferral.clientDisplayName || `Client ${firstReferral.clientId}`}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                {referrals.referrals.length === 1 
                                    ? `1 referral` 
                                    : `${referrals.referrals.length} referrals`
                                }
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                c-id: {firstReferral.clientCode || firstReferral.clientId}
                            </Typography>
                        </Box>
                    </AccordionSummary>
                    
                    <AccordionDetails>
                        <Box width="100%">
                            {hasErrors && (
                                <Box mb={2}>
                                    <Alert severity="warning">
                                        <Typography variant="body2">
                                            <strong>Error</strong>
                                        </Typography>
                                        <Typography variant="body2">
                                            {errors.join(", ")}
                                        </Typography>
                                    </Alert>
                                </Box>
                            )}
                            
                            <ReferralsWithClient 
                                referrals={referrals} 
                                requiresOnline={forceOnline} 
                            />
                        </Box>
                    </AccordionDetails>
                </Accordion>
            </Card>
        </Box>
    );
};

export default ReferralItem;
