import {delay, StringUtils} from "@eccosolutions/ecco-common"
import * as dto from "ecco-dto/evidence-risk-dto";
import {ServiceRecipientWithEntities} from "ecco-dto";
import {showInModalDom} from 'ecco-components-core';
import RadarChartSpokes = require("../../draw/RadarChartSpokes");

import services = require("ecco-offline-data");

import BaseGraphControl = require("./BaseGraphControl");
import events = require("../events");
import domain = require("../risk/domain");
import RiskEvidenceSnapshot = domain.RiskEvidenceSnapshot;
import RiskGroupEvidence = domain.RiskAreaEvidence;
import RiskGroupEvidenceDto = dto.RiskGroupEvidenceDto;


enum RedAmberGreen {
    Unknown = -1, Red = 0, Amber = 1, Green = 2, None = 3
}


class BackingData {
    constructor(public serviceRecipient: ServiceRecipientWithEntities, public evidence: dto.RiskGroupEvidenceDto[]) {
    }
}

/**
 * All data for RAG can be gleaned from supportthreatoutcomes table where we have columns:
 * - levelMeasure = "rag" | "recencyImpact"
 * - sto_level (service type outcome) = RedAmberGreen enum or the recency level which would appear to be (0-20)
 */
class RedAmberGreenRiskChart extends BaseGraphControl<BackingData> {
    private riskEvidenceSnapshot: RiskEvidenceSnapshot;
    private loaded = false;

    constructor(private serviceRecipientId?: number) {
        super();
        events.RiskUpdateEvent.bus.addHandler( (event) => this.handleUpdateEvent(event) );
    }

    protected override onActivated() {
        if (this.loaded) {
            this.load();
        }
    }
    protected fetchViewData() {
        return services.getReferralRepository().findOneServiceRecipientWithEntities(this.serviceRecipientId)
            .then(svcRec =>
                services.getRiskWorkRepository().findRiskAreaEvidenceByServiceRecipientId(svcRec.serviceRecipientId)
                    .then( evidence => new BackingData(svcRec, evidence) )
                );
    }

    protected renderGraph(data: BackingData): void {
        this.riskEvidenceSnapshot = new RiskEvidenceSnapshot(data.serviceRecipient.configResolver.getServiceType());
        this.drawAxes(); // draw the axes even if needs assessment not done
        this.applyHistory(data.evidence);
    }

    private drawAxes() {
        if (!this.riskEvidenceSnapshot) {
            console.log("ignored called to drawAxes on RAG chart... not yet loaded"); // TODO: find out how this can ever happen
            return; // not loaded
        }
        const numSpokes = this.riskEvidenceSnapshot.getRiskAreaEvidences().length;

        const labelledSpokes = new RadarChartSpokes(this.paper, this.centreX, this.centreY, "#bbb", "1.5", numSpokes);
        this.riskEvidenceSnapshot.getRiskAreaEvidences().forEach( (re) => {
            const maxLabelLen = numSpokes < 22 ? 26 - numSpokes : 4; // shorter the more there are
            const shortTxt = StringUtils.abbreviateWords(re.getRiskGroup().getName(), maxLabelLen);
            const label = labelledSpokes.addSpoke(this.radius).addLabel(shortTxt);
        });
    }

    /** Apply history and redraw after each piece of support work */
    private applyHistory(itemsByNewestFirst: RiskGroupEvidenceDto[]) {
        const handleItem = (i: number): Promise<void> => {
            if (i >= 0) {
                this.applyWork(itemsByNewestFirst[i], i, itemsByNewestFirst.length);
                return delay(1000 / itemsByNewestFirst.length)   // 1000 = total animation time
                        .then(() => handleItem(i - 1));
            } else {
                this.loaded = true;
                return Promise.resolve(null);
            }
        };
        handleItem(itemsByNewestFirst.length - 1);
    }

    private applyWork(work: RiskGroupEvidenceDto, itemIndex: number, itemCount: number ) {
        const isLastItem = itemIndex == itemCount - 1;
        this.riskEvidenceSnapshot.addRiskAreaEvidence(work);
        if (itemIndex == 0 || isLastItem || this.isModernBrowser()) {
            this.redraw();
        }
    }

    public drawChart() {
        const numSpokes = this.riskEvidenceSnapshot.getRiskAreaEvidences().length;

        const radialLines = new RadarChartSpokes(this.paper, this.centreX, this.centreY, "black", "2.5", numSpokes);

        this.riskEvidenceSnapshot.getRiskAreaEvidences().forEach( (re) => {
            this.addDataPoint(re, radialLines);
        });
    }

    private addDataPoint(riskEvidence: RiskGroupEvidence, radialLines: RadarChartSpokes) {

        // TODO: Warning .. we should be picking which spoke!
        // Instead we're just going round-robin and order may be wrong
        const percent = riskEvidence.isEmpty() ? 0 : this.calculateRiskLevelPercent(riskEvidence);

//            if (this.options.threatGraph == 'CAF10') {
//                var colour = (percent < 36) ? 'green' :
//                             (percent < 45) ? 'yellow' :
//                                              'red';
//            }
//            else {
        const colour = (percent <= 15) ? 'white' :
                (percent <= 20) ? 'green' :
                        (percent <= 60) ? 'yellow' :
                                'red';
//            }
            radialLines.addSpoke(percent, colour);
    }

    /**
     * From ThreatEvidenceBusinessLogic.java:388
     * 'unknown' isn't part of the calculation
     */
    private calculateRiskLevelPercent(riskEvidence: RiskGroupEvidence): number {
        if (riskEvidence.getLevelMeasure() == "recencyImpact") {
            if (riskEvidence.getLevel() > -1) {
                return riskEvidence.getLevel() * 5;
            }
        } else {
            if (riskEvidence.getLevel() == RedAmberGreen.Red)
                return 100;
            if (riskEvidence.getLevel() == RedAmberGreen.Amber)
                return 60;
            if (riskEvidence.getLevel() == RedAmberGreen.Green)
                return 20;
            // if we are trying to indicate no risk
            if (riskEvidence.getLevel() == RedAmberGreen.None)
                return 15;
        }
        return 0;
    }

    private handleUpdateEvent(event: events.RiskUpdateEvent) {
        if (!this.riskEvidenceSnapshot) {
            return; // not loaded yet so no need to update
        }
        this.riskEvidenceSnapshot.applyCommand(event.command);
        this.redraw();
    }

    private redraw() {
        this.clear();
        this.drawAxes();
        this.drawChart();
    }

    public static showInModal(serviceRecipientId: number, serviceId: number, serviceTypeId: number) {
        const form = new RedAmberGreenRiskChart(serviceRecipientId);
        showInModalDom("Risk Chart", form.domElement());
        form.load();
    }
}
export = RedAmberGreenRiskChart;
