import $ = require("jquery");

import Element = require("../controls/Element");
import SelectList = require("../controls/SelectList");
import ListDefinitionEntry = cfgDomain.ListDefinitionEntry;
import * as cfgDomain from "ecco-dto";
import {SessionDataRepository} from "ecco-dto";

/** Select list based on feature config based select lists
 * FIXME: Refactor this. It's not an ecco-standard component as it's relying on data attributes and a supplied container
 */
class ListDefSelectList implements Element {
    private name: string;
    private id: string;
    private initialValue: string; // don't do multiple values for select list - awful UX
    private listName: string;
    private readOnly: boolean;
    private selectList: SelectList;
    private useMetaValue: boolean = false;
    private sessionData: cfgDomain.SessionData;
    private nullEntryText = "-";

    public static createContainer(sharedId: string, listName: string, initialValue: any): $.JQuery {
        const $container = $("<div>");
        $container.attr("id", sharedId);
        $container.data("name-shared", sharedId);
        $container.data("initial-value-shared", initialValue);
        $container.attr("list-name", listName);
        //$container.attr("list-id", sharedId); // don't re-use the same id for multiple ddl, causes some to blank

        // attach to the dom, since the calculateInitialValue looks it up - so it needs to exist
        $("body").append($container);

        return $container;
    }

    public static getContainerValue(sharedId: string): $.JQuery {
        return $("#" + sharedId).val();
    }

    constructor(private $container: $.JQuery, private featureConfigRepository?: SessionDataRepository,
            private onChange?: (value: string) => void, private className?: string) {
        this.name = $container.attr("data-name");
        this.initialValue = $container.attr("data-initial-value");
        var readOnlyStr = $container.attr("data-read-only");
        this.readOnly = readOnlyStr ? /true/i.test(readOnlyStr) : false; // http://stackoverflow.com/questions/263965/how-can-i-convert-a-string-to-boolean-in-javascript?page=2&tab=votes#tab-top
        this.listName = $container.attr("list-name");
        this.id = $container.attr("list-id");
        this.createControl();
    }

    public load(): Promise<void> {
        return this.featureConfigRepository.getSessionData().then( (x) => {
            var entries = this.getEntries(x, this.getListName());
            this.populateControl(entries);
        })
        .catch( (reason) => {
            this.handleError(reason);
        })
    }

    /**
     * useMetaValue is used when what is passed/stored is not the id of the list def, but a value for looking up the
     * listdef based on its metadata.value. The value is also returned on getSelected()
     * NB This does NOT sort the list definition entries - as it's used in places that don't want it
     */
    public init(f: cfgDomain.SessionData, listName: string, initValue: string,
            useMetaValue?: boolean) {
        this.sessionData = f;
        this.listName = listName;
        this.useMetaValue = useMetaValue;

        const listEntries = f.getListDefinitionEntriesByListName(listName);

        if (initValue && useMetaValue) {
            var lookupListNameByValue = listEntries.filter((listDef) => listDef.getValue() == initValue).pop();
            this.initialValue = lookupListNameByValue.getId().toString();
        } else {
            this.initialValue = initValue;
        }

        this.nullEntryText = f.getMessages()[ "listDef.nullText." + this.listName] || "-";
        this.populateControl(listEntries);
    }

    public getListName(): string {
        return this.listName;
    }

    public getSelectedId() {
        return this.useMetaValue
            ? this.getSelectedMetaValueId()
            : this.selectList.selectedValAsNumber();
    }

    public getSelectedMetaValueId() {
        var valueId = this.selectList.selectedValAsNumber();
        return valueId
                ? parseInt(this.sessionData.getListDefinitionEntryById(valueId).getValue())
                : null;
    }

    public isValid() {
        return this.getSelectedId() != null;
    }

    public element(): $.JQuery {
        return this.$container;
    }

    public getLength(): number {
        return this.selectList.length();
    }

    protected getEntries(f: cfgDomain.SessionData, listName: string): Array<ListDefinitionEntry> {
        return f.getListDefinitionEntriesByListName(listName);
    }

    private createControl() {
        // NOTE: some values will be null or defaults in some situations until this class is properly refactored
        this.selectList = new SelectList(this.name, undefined, this.nullEntryText, this.id, this.readOnly, this.className);
        this.selectList.withEmptyEntryValue(""); // not -1
        if (this.onChange) {
            this.selectList.change(this.onChange);
        }

        this.$container.empty();
        this.selectList.element().appendTo(this.$container);
    }

    public populateControl(entries: ListDefinitionEntry[], overrideInitialValue?: string) {
        if (overrideInitialValue) {
            this.initialValue = overrideInitialValue;
        }
        this.selectList.withEmptyEntryText(this.nullEntryText).clear();
        entries = entries.filter( (entry) => !entry.getDisabled() );
        this.selectList.populateFromList<ListDefinitionEntry>(entries,
            (entry) => ({key: entry.getId().toString(), value: this.getListEntryName(entry)}),
            (entry) => entry.getId().toString() == this.initialValue);
    }

    protected getListEntryName(entry: ListDefinitionEntry) {
        return entry.getName();
    }

    protected handleError(reason: any) {
        this.$container.text("failed to load: click to retry")
            .click( () => {
                this.load()
            });
    }
}
export = ListDefSelectList;

