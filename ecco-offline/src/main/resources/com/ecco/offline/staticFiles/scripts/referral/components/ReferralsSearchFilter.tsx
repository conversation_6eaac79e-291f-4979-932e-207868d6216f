import * as React from "react";
import { FC } from "react";
import {
    Box,
    TextField,
    FormControl,
    FormLabel,
    RadioGroup,
    FormControlLabel,
    Radio,
    Grid,
    InputAdornment
} from "@eccosolutions/ecco-mui";
import SearchIcon from "@material-ui/icons/Search";

export type MeOrTeam = "my clients" | "team clients";

interface ReferralsSearchFilterProps {
    searchInput: string;
    onSearchInputChange: (value: string) => void;
    meOrTeamValue: MeOrTeam;
    onMeOrTeamChange: (value: MeOrTeam) => void;
    showMeOrTeamFilter?: boolean;
    placeholder?: string;
}

/**
 * Search and filter component for referrals list
 */
export const ReferralsSearchFilter: FC<ReferralsSearchFilterProps> = ({
    searchInput,
    onSearchInputChange,
    meOrTeamValue,
    onMeOrTeamChange,
    showMeOrTeamFilter = true,
    placeholder = "e.g. r-id or client name"
}) => {
    return (
        <Box p={2}>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={showMeOrTeamFilter ? 8 : 12}>
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder={placeholder}
                        value={searchInput}
                        onChange={(e) => onSearchInputChange(e.target.value)}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    <SearchIcon />
                                </InputAdornment>
                            ),
                        }}
                        size="small"
                    />
                </Grid>
                
                {showMeOrTeamFilter && (
                    <Grid item xs={12} md={4}>
                        <FormControl component="fieldset">
                            <FormLabel component="legend" style={{ fontSize: '0.875rem' }}>
                                View
                            </FormLabel>
                            <RadioGroup
                                row
                                value={meOrTeamValue}
                                onChange={(e) => onMeOrTeamChange(e.target.value as MeOrTeam)}
                            >
                                <FormControlLabel
                                    value="my clients"
                                    control={<Radio size="small" />}
                                    label="My clients"
                                    style={{ fontSize: '0.875rem' }}
                                />
                                <FormControlLabel
                                    value="team clients"
                                    control={<Radio size="small" />}
                                    label="Team clients"
                                    style={{ fontSize: '0.875rem' }}
                                />
                            </RadioGroup>
                        </FormControl>
                    </Grid>
                )}
            </Grid>
        </Box>
    );
};

export default ReferralsSearchFilter;
