import {withSessionData} from "ecco-components";
import {StaffDto} from "ecco-dto";
import * as React from "react";
import {applicationRootPath} from "application-properties";
import {Button} from "react-bootstrap";
import StaffSearchAndImport = require("./StaffSearchAndImport");


function onStaffSelected(worker: StaffDto, event: React.MouseEvent<Button>) {
    // shift-click for JSP version
    location.href = new URL(event.shiftKey
        ? `${applicationRootPath}nav/secure/hr/worker/overview.html?workerId=${worker.workerId}`
        : `${applicationRootPath}nav/r/welcome/${worker.workerId}/`,
        location.href).href;
}

const StaffListSearch = () =>
    withSessionData(sessionData =>
        <StaffSearchAndImport
            sessionData={sessionData}
            selectedResultRender={false}
            subFormsAsModal={true}
            existingOnly={false}
            selectedResultRenderer={worker => <span><small>{worker.workerId}</small>
                    <Button key={worker.workerId}
                            bsStyle="link"
                            title="shift-click for old"
                            onClick={event => onStaffSelected(worker, event)}>
                        {worker.firstName} {worker.lastName}
                    </Button>
                </span>
            }/>
    , null);

function WelcomePage() {
    return (
        <div className="container-fluid top-gap-15 client-search col-xs-12 col-lg-offset-2 col-lg-8">
            {/*<h3>Staff</h3>*/}
            <StaffListSearch/>
        </div>
    );
}

export default WelcomePage;
