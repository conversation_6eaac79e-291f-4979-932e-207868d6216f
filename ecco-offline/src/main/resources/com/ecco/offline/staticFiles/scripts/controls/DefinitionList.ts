import _ = require("lodash");
import $ = require("jquery");
import HtmlElement = require("./HtmlElement");

/**
 * A Definition list
 */
class DefinitionList extends HtmlElement {

    constructor(private id?: string) {
        super($("<dl>"), id);
    }

    /**
     * @param defId optional id to add to <dd> element to make easy to find later
     */
    public addEntry(term: string, def: string, onClick?: () => void, defId?: string) {
        return this.addEntryJQuery(term, document.createTextNode(def || "-"), onClick, defId);
    }

    public addEntryOrOmit(term: string, def: string) {
        def && this.addEntry(term, def);
        return this;
    }

    /** Convert newlines but ensure still escape correctly */
    public addEntryWithLines(term: string, def?: string) {
        var html = def ? def.split("\n").map( (t) => _.escape(t) ).join("<br>") : "-";
        this.addEntryHtml(term, html);
        return this;
    }

    public addNumEntry(term: string, def: number) {
        this.addEntry(term, def ? def.toString() : "-");
        return this;
    }

    public addNumEntryOrOmit(term: string, def: number) {
        def && this.addEntry(term, def.toString());
        return this;
    }

    public addEntryHtml(term: string, defHtml: string) {
        super.element()
            .append($("<dt>").text(term))
            .append($("<dd>").html(defHtml));
        return this;
    }

    public addEntryJQuery(term: string, defJQ: $.JQuery|Text, onClick?: () => void, defId?: string) {
        var $term = $("<dt>");
        if (onClick) {
            $term.append( $("<span>").addClass("button").text(term).click(onClick) );
        }
        else {
            $term.text(term);
        }
        super.element()
            .append($term)
            .append($("<dd>").attr("id", defId).append(defJQ));
        return this;
    }
    public addEntryJQueryWrapped(term: string, defJQ: $.JQuery, wrapperClasses: string) {
        super.element()
            .append($("<div>").addClass(wrapperClasses)
                .append($("<dt>").text(term))
                .append($("<dd>").append(defJQ)) );
    }
}

export = DefinitionList;