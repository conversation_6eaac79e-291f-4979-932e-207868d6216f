import $ = require("jquery");
import * as _ from 'lodash';
import BaseControl = require("./BaseControl");
import InputControl = require("./InputControl");
import {ValidationErrors, ValidationChecksBuilder, ValidationCheck} from "../common/validation";
import Cancelable = _.Cancelable;
import {autoSaveKey} from "./autosave";
import InputValidation = require("../controls/InputValidation");
import {ReloadEvent} from "@eccosolutions/ecco-common";


abstract class StringInputControl extends BaseControl implements InputControl {

    private onChange: (val: string) => void;

    private maxLength?: number;

    protected placeholder?: string;

    private autoSaveStop = false;
    private debouncedAutoSave: ((val: string) => void) & Cancelable;

    private validation: InputValidation;

    protected id: string;

    /** If id is supplied, it is used for the id and name attributes of the input element */
    public constructor($container: $.JQuery, protected $input: $.JQuery = $container, idAndName: string, id?: string) {
        super($container);

        if (idAndName) {
            this.id = idAndName;
            $input.attr("id", idAndName)
                .attr("name", idAndName);
        }
        if (id) {
            this.id = id;
            $input.attr("id", id);
        }
        // TODO this doesn't fire for div's (which contenteditable is - see TextAreaInput) - see https://api.jquery.com/change/
        // so we'd need to use binding: $input.on('oninput propertychange', (event) => this.handleChange(event));
        this.$input.change(event => this.handleChange(event));
        this.debouncedAutoSave = _.debounce(this.autoSaveWithCheck, 2000);
        // when an evidence form is saved, we have a few hooks to clear the draft saves
        // however, the debounce can still cause a save after that since it waits 2 seconds
        // so we need a way to stop the debounce when a save is being triggered
        // if we use ReloadEvent then we could miss the odd case where saving takes a while
        // but ReloadEvent is followed by removeDraftsFromPage (see flushCommands and BaseEvidenceForm.afterSubmitted)
        // which means we stop the draft save after a successful save, but then anything in the meantime is cleared - which is great
        // NB if we didn't want to removeDraftsFromPage everywhere, we could use commandSubmittedEventBus, or AjaxStartEvent
        ReloadEvent.bus.addHandler(() => {
            this.autoSaveStop = true;
        });
    }

    /** Generate an autosave key for use with localstorage, and based on href and field name */
    protected autoSaveKey(): string {
        // .replace("/", "_") ??
        return `autosave-${autoSaveKey()}-${this.$input.attr("id")}`;
    }

    private autoSaveWithCheck(val: string) {
        if (!this.autoSaveStop) {
            this.autoSave(val);
        }
    }

    protected autoSave(val: string) {
        // override in child classes for now
    }

    protected handleChange(event: $.JQueryEventObject) {
        const val = this.valOnInput();
        this.onChange && this.onChange(val);
        this.validation && this.validation.validate(val);
        this.debouncedAutoSave(val);
    }

    public change( onChange: (value: string) => void, liveUpdate: boolean = false, clearOnEsc: boolean = false ): this {
        if (this.onChange) {
            throw new Error("Error: Don't attach multiple event handlers");
        }

        this.onChange = onChange;

        if (liveUpdate) {
            this.$input.keyup((event:$.JQueryEventObject) => {
                event.stopPropagation();
                // If user hits ESC key, delete content
                if (clearOnEsc && event.which == 27) {
                    $(event.delegateTarget).val("");
                }
                this.handleChange(event);
            });
        } else {
            this.onChange = (value: string) => {
                this.validation && this.validation.validate(value);
                onChange(value);
            };
        }
        return this;
    }

    protected trimMaxLength(event: $.JQueryEventObject) {
        let val = this.valOnInput();
        if (this.maxLength && val.length > this.maxLength) {
            event.preventDefault();
            // Doing as below is horrid. We should validate the length instead
            // val = val.substr(0, this.maxLength - 1); // -1 to allow for what is being added
            // this.setVal(val);
        }
    }

    /** Set the max length - works for textarea under HTML5 (IE10+) */
    public withMaxLength(maxLength: number) {
        this.maxLength = maxLength;
        this.$input.attr("maxlength", maxLength);
        return this;
    }

    /** Set the displayed width in chars */
    public withSize(size: number) {
        this.$input.attr("size", size);
        return this;
    }

    public setReadOnly() {
        this.$input.attr('disabled', 'disabled');
    }

    public placeholderText(text: string) {
        this.placeholder = text;
        this.$input.attr("placeholder", text);
        return this;
    }

    public setVal(value: string) {
        this.setValOnInput(this.$input, value);
        return this;
    }

    public val(): string {
        return this.valOnInput();
    }

    /**
     * Return the string value, or null if there is no value.
     * @param {boolean} hasInitialValue could be calculated, but easier as-is for now
     * NB it is assumed hasInitialValue is false with an initialValue of null or undefined
     */
    public getValueOrNull(hasInitialValue: boolean): string {
        const rawVal: string = this.val(); // jquery val, but we know its a string value
        return hasInitialValue
            // return what we have - empty text is a valid value
            ? rawVal
            // if no initial value, then empty means no value, return null
            : rawVal != "" ? rawVal : null;
    }

    protected setValOnInput($elem: $.JQuery, value: string) {
        $elem.val(value);
        $elem.trigger('change');
    }

    protected valOnInput(): string {
        return this.$input.val();
    }

    public isValid() {
        return this.val() && this.val().length > 0;
    }

    public withValidationChecks(checks: ValidationChecksBuilder,
                                errors: ValidationErrors,
                                validationCallBack: (valid: boolean, message?: string) => void) {
        if (this.onChange) {
            throw new Error("Error: Call withValidationChecks before change");
        }
        this.validation = new InputValidation(this.id, this, this, checks, errors, validationCallBack);
        return this;
    }
    public enableValidation() {
        this.validation.primeValidation();
    }
    public validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors) {
        if (checks.isChecked(ValidationCheck.Required)) {
            errors.requireLength(field, this.val());
        }
        if (checks.isChecked(ValidationCheck.UTF8)) {
            errors.requireNoEmojis(field, this.val());
        }
        if (checks.isChecked(ValidationCheck.Money)) {
            errors.requireMoney(field, this.val());
        }
        if (checks.isChecked(ValidationCheck.Number)) {
            errors.requireNumber(field, this.val());
        }
    }
}
export = StringInputControl;
