import BaseControl = require("../controls/BaseControl");
import {CommandQueue} from "ecco-commands";
import DialogContent from "../controls/DialogContent";
import {ActionsChangedCallback} from "@eccosolutions/ecco-common";
import {getCommandQueueRepository, showErrorAsAlert} from "ecco-offline-data";


/** For use with the following from the derived class
 *       var form = new ManageServiceActivitiesControl(serviceId, serviceTypeId);
 *      Modal.showInModal(form);
 *      form.load();
 */
class BaseCommandForm extends BaseControl implements DialogContent {


    /** callback should be called when the Form has successfully saved it's content and the dialog can close.
     *  This is usually in response to a "done" button being clicked in the footer, for example */
    private onFinished: () => void;

    private updateActions: ActionsChangedCallback;

    protected commandQueue = new CommandQueue(getCommandQueueRepository());

    constructor(private title: string) {
        super();
    }

    protected submitForm(): Promise<void> {
        return this.commandQueue.flushCommands()
            .then(() => this.onFinished())
            .catch(showErrorAsAlert);
    }

    private setActions(disabled: boolean) {
        this.updateActions([{
            label: "save",
            clickedLabel: "saving...",
            disabled,
            style: "primary",
            onClick: () => this.submitForm()
        }]);
    }

    protected enableSubmit(enabled = true) {
        this.setActions(enabled);
    }

    // DialogContent
    registerActionsChangeListener(updateActions: ActionsChangedCallback) {
        this.updateActions = updateActions;
        this.setActions(false);
    }

    public getTitle(): string {
        return this.title;
    }

    public getFooter() {
        return null;
    }

    public setOnFinished( onFinished: () => void) {
        this.onFinished = onFinished;
    }
}

export = BaseCommandForm;
