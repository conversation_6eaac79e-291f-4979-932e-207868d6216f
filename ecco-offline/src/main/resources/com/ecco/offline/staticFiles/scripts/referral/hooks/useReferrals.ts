import { useMemo } from "react";
import { usePromise } from "ecco-components";
import { useServicesContext } from "ecco-components";
import { ReferralDto, SessionData } from "ecco-dto";
import { ReportAjaxRepository } from "ecco-reports";
import { with<PERSON><PERSON><PERSON><PERSON>r<PERSON>and<PERSON> } from "ecco-offline-data";
import { groupBy } from "lodash";
import { Referrals } from "../components/ReferralActionsCard";
import services from "ecco-offline-data";

export interface ReferralsFilter {
    myReferrals: boolean;
    buildingId?: number;
    search?: string;
}

export interface UseReferralsOptions {
    filter: ReferralsFilter;
    forceOnline?: boolean;
    liveReferralsQ?: Promise<ReferralDto[]>;
}

export function useReferrals(options: UseReferralsOptions) {
    const { filter, forceOnline = false, liveReferralsQ } = options;
    
    const { resolved: sessionData, loading: sessionLoading } = usePromise(
        () => services.getFeatureConfigRepository().getSessionData(),
        []
    );

    const referralsPromise = useMemo(() => {
        if (!sessionData) return null;
        
        if (filter.buildingId) {
            return services.getReferralRepository().findAllReferralsInsideBuilding(filter.buildingId);
        } else if (filter.myReferrals) {
            return services.getReferralRepository().findAllReferralsForOffline();
        } else {
            return liveReferralsQ || services.getReferralRepository().findAllReferralSummary(
                ReportAjaxRepository.generateLiveReportCriteria()
            );
        }
    }, [filter.buildingId, filter.myReferrals, liveReferralsQ, sessionData]);

    const { resolved: rawReferrals, loading: referralsLoading, error } = usePromise(
        () => referralsPromise ? withAuthErrorHandler(referralsPromise) : Promise.resolve([]),
        [referralsPromise]
    );

    const processedReferrals = useMemo(() => {
        if (!rawReferrals || !sessionData) return [];
        
        // Group by client ID
        const byClient = groupBy(rawReferrals, item => item.clientId);
        let referralGroups = Object.values(byClient).map(referrals => new Referrals(referrals));
        
        // Apply search filter if provided
        if (filter.search && filter.search.trim()) {
            const searchTerm = filter.search.toLowerCase().trim();
            referralGroups = referralGroups.filter(referralGroup => {
                return referralGroup.referrals.some(r => {
                    // Search in referral code/ID
                    if (r.referralCode?.toLowerCase().includes(searchTerm) || 
                        r.referralId?.toString().includes(searchTerm)) {
                        return true;
                    }
                    // Search in client display name
                    if (r.clientDisplayName?.toLowerCase().includes(searchTerm)) {
                        return true;
                    }
                    return false;
                });
            });
        }
        
        return referralGroups;
    }, [rawReferrals, sessionData, filter.search]);

    return {
        referrals: processedReferrals,
        sessionData,
        loading: sessionLoading || referralsLoading,
        error
    };
}

export function useReferralsFromBuildingSrId(srId: number) {
    const { resolved: building, loading: buildingLoading, error: buildingError } = usePromise(
        () => services.getBuildingRepository().findOneBuildingBySrId(srId),
        [srId]
    );

    const referralsOptions = useMemo(() => ({
        filter: {
            myReferrals: false,
            buildingId: building?.buildingId
        },
        forceOnline: true
    }), [building?.buildingId]);

    const referralsResult = useReferrals(referralsOptions);

    return {
        ...referralsResult,
        building,
        loading: buildingLoading || referralsResult.loading,
        error: buildingError || referralsResult.error
    };
}
