import _ = require("lodash");
import $ = require("jquery");
import Element = require("./Element");
import SearchableContainer = require("./SearchableContainer");

/**
 Control which presents a accordion UI of a series of titled, switchable panels.
 Uses Bootstrap collapse - http://getbootstrap.com/javascript/#collapse
*/
class Accordion implements SearchableContainer {
    private $container:$.JQuery;

    private $headingTemplate: $.JQuery = $("<div>").addClass("panel-heading");

    private $bodyTemplate: $.JQuery = $("<div>").addClass("panel-collapse collapse")
            .append($("<div>").addClass("panel-body"));

    private $panelTemplate: $.JQuery = $("<div>").addClass("panel panel-default")
            .append(this.$headingTemplate)
            .append(this.$bodyTemplate);

    private panels: { [panelId: string]: $.JQuery; } = {};

    constructor(private id: string = _.uniqueId("acrdn_")) {
        this.$container = $("<div>")
                .attr("id", this.id)
                .addClass("panel-group");
        this.$headingTemplate.find(".panel-title>a").attr("data-parent", "#" + this.id);
    }

    public addControlForNewEntry(addEntryText: string, iconClasses: string, adminOnly: boolean,
        addNewEntry: () => void) {
        throw new TypeError("Not implemented");
    }

    /** Adds a panel to the accordion with a custom ID, title and content element, and returns the index of that panel, 0-based. */
    public append(panelId: string, heading:$.JQuery, body: $.JQuery): number;
    public append(panelId: string, heading: Element, body: Element): number;
    append(panelId: string, heading: any, body: any): number {
        var containerId = _.uniqueId('pnl_' + panelId + '_');

        this.panels[panelId] = this.$panelTemplate.clone();
        this.panels[panelId].find(".panel-collapse").attr("id", containerId);

        var $heading = this.toJQuery(heading);
        var $title = $heading.hasClass("panel-title")? $heading.children("a") : $heading.find(".panel-title>a");
        $title.attr("data-original-href", $title.attr("href")).attr("href", "#" + containerId).attr("data-toggle", "collapse");
        this.panels[panelId].find(".panel-heading").append($heading);
        if (body) {
            this.panels[panelId].find(".panel-body").append(this.toJQuery(body));
        }

        var index = this.$container.children().length;
        this.$container.append(this.panels[panelId]);
        return index;
    }

    public hasItem(panelId: string): boolean {
        return !!this.panels[panelId];
    }

    public hideAll(): void {
        this.$container.children().addClass('hidden');
    }

    public showAll(): void {
        this.$container.children().removeClass('hidden');
    }

    public show(panelId: string): void {
        this.panels[panelId].removeClass('hidden');
    }

    public addClass(classes: string, panelId: string): void {
        this.panels[panelId].addClass(classes);
    }

    public removeClass(classes: string, panelId?: string): void {
        if (panelId) {
            this.panels[panelId].removeClass(classes);
        }
        else {
            this.$container.children().removeClass(classes);
        }
    }

    private toJQuery(element: any): $.JQuery {
        var $content: $.JQuery;
        if ((<Element>element).element) {
            $content = element.element();
        }
        else {
            $content = $(element);
        }
        return $content;
    }

    public element(): $.JQuery {
        return this.$container;
    }
}

export = Accordion