import $ = require("jquery-bundle");
import environment = require("../environment");
import {EccoDateTime} from "@eccosolutions/ecco-common";


class DateTimePicker {
    private static CSS_CLASS = "datetime-picker";

    private $inputElement: $.JQuery;
    private $span: $.JQuery;

    constructor(private initialDate: Date, private onSelect: (source: DateTimePicker, date: Date) => void) {
    }

    /** default is datetime */
    public attach($input: $.JQuery) {
        this.attachInit($input);
        this.attachDateTime($input);
    }

    private attachInit($input: $.JQuery) {
        if (!$input.is("input[type=text], input[type=hidden], input:not([type])")) {
            // input:not([type]) is semantically equivalent to input[type=text].
            throw new Error("DatePicker must be attached to input[type=text] or input[type=hidden]");
        }

        this.$inputElement = $input;

        this.$span = $("<span>")
            .addClass(DateTimePicker.CSS_CLASS);

        $input.replaceWith(this.$span);

        this.$span.append($input);
    }

    public attachDateTime($input: $.JQuery) {
        this.attachInit($input);

        var $datepicker = (<any>$input).datetimepicker({
            defaultDate: this.initialDate,
            autoSize: false,
            dateFormat: 'dd/mm/yy',
            yearRange: 'c-01:c+10', // i.e. currently selected year - 1 : + 10
            changeMonth: true,
            changeYear: true,
            stepMinute: 1,
            showOn: "both",
            showButtonPanel: true,
            buttonImage: environment.imagesBase + "datepicker.png",
            buttonImageOnly: true, // without this it's a "<button>"
            //            buttonText: "Reschedule",
            onClose: (dateText: string, instance: any) => {
                var trimmed = $.trim(dateText);
                $input.val(trimmed);
                this.changeDate();
            },

            // Stupid hack to make datepicker work in conjunction with Bootstrap-modal.
            // See: https://github.com/jschr/bootstrap-modal/issues/239
            beforeShow: () => {
                // JQuery UI sets the z-index of the datepicker immediately
                // after this function returns, so we arbitrarily delay for one
                // more turn of the event loop using setTimeout.
                setTimeout(() => $(".ui-datepicker").css("z-index", "10000"), 0)
            }
        });
        this.setDate(this.initialDate);
        return this.$span;
    }

    public navNextDay() {
        var date = this.getDate();
        date.setDate( date.getDate() + 1);
        this.setDate(date);
        this.changeDate();
    }

    public navToday() {
        var date = new Date();
        this.setDate(date);
        this.changeDate();
    }

    public navPrevDay() {
        var date = this.getDate();
        date.setDate( date.getDate() - 1);
        this.setDate(date);
        this.changeDate();
    }

    private changeDate() {
        if (this.getDate()) {
            this.onSelect(this, this.getDate());
        }
    }

    private getDate(): Date {
        return this.$inputElement.datetimepicker('getDate');
    }

    public getDateTime(): EccoDateTime {
        return EccoDateTime.fromLocalJsDate(this.getDate());
    }

    public setDate(date: Date) {
        this.$inputElement.datetimepicker( "setDate", date );
    }
}

export = DateTimePicker
