import $ = require("jquery")
import {EvidenceGroup, SessionData} from "ecco-dto";
import {Question} from "ecco-dto/service-config-dto";
import QuestionnaireWizardForm = require("../../evidence/questionnaire/QuestionnaireWizardForm");
import {HactAnswerEvent} from "./HactNotificationControl";


/**
 * Form to display the HACT questions in a wizard
 */
class HactWizardForm extends QuestionnaireWizardForm {

    // NB all the 'taskSource' for hact we assume just as 'hactQuestionnaire'
    // it could be possible to save the taskSource as the page the work was done
    // but this isn't currently how its intended, and has little benefit in changing
    public static HACT_TASKNAME: string = "hactQuestionnaire";

    public static showHactInModalByIds(
            sessionData: SessionData,
            svcrepId: number,
            preQuestions: Question[],
            postQuestions: Question[],
            afterSubmittedIn: () => void,
            afterCancelledIn: () => void) {
        var form = new HactWizardForm(sessionData, svcrepId,
                            preQuestions,
                            postQuestions,
                            () => {
                                afterSubmittedIn();
                                HactAnswerEvent.bus.fire();
                            },
                            () => {
                                afterCancelledIn();
                            });
        form.showFormInModal();
    }

    constructor(sessionData: SessionData, serviceRecipientId: number,
            private preQuestions: Question[],
            private postQuestions: Question[],
            afterSubmitted: () => void,
            afterCancelled: () => void) {
        super(sessionData, serviceRecipientId, EvidenceGroup.hactQuestionnaire, HactWizardForm.HACT_TASKNAME,
            "HACT Survey", preQuestions.concat(postQuestions), null, afterSubmitted, afterCancelled);
        this.labelPreSurveys();
    }

    private labelPreSurveys() {
        const preSurveyIds: number[] = this.preQuestions ? this.preQuestions.map(pq => pq.id) : [];
        this.questions.filter(q => preSurveyIds.indexOf(q.id) > -1).forEach(q => {
            if (q.name.indexOf("pre survey") == -1) {
                q.name = q.name + " [pre survey]"
            }
        });
    }

    public override getWizardContentForIntroPage(): $.JQuery {
        return $("<p>").text("Please read the following questions as written. It is important" +
            " that we ask questions at this stage as it will allow us to" +
            " understand the service we run and inform an improved service.");
    }

    public override getWizardContentForCancelPage(): $.JQuery | null {
        let hasAnswered = (this.countAnswers() > 0);
        if (hasAnswered) {
            return $("<span>").text("you have unsaved answers")
                .append("<br>");
        }
        let hasPreSurveys = this.preQuestions.length > 0;
        if (hasPreSurveys) {
            return $("<span>").text("there are pre-surveys unanswered (which must be answered soon)")
                .append("<br>");
        }
        return null;
    }

    public override getWizardContentForSummaryPage(): $.JQuery {

        var $line1 = $("<span>").text(this.preQuestions.length +
            " pre survey question(s) were asked and you answered " +
            this.countAnsweredPreSurveys());

        var $line2 = $("<span>").text(this.postQuestions.length +
            " post survey question(s) were asked and you answered " +
            this.countAnsweredPostSurveys());

        return $line1.append("<br>").append($line2);
    }

    private countAnsweredPreSurveys() {
        return this.preQuestions.filter( qn => {
            return this.hasAnswer(qn.id);
        }).length;
    }

    private countAnsweredPostSurveys() {
        return this.postQuestions.filter( qn => {
            return this.hasAnswer(qn.id);
        }).length;
    }

    public cancelConfirm() {
        let hasAnswered = (this.countAnsweredPreSurveys() > 0 || this.countAnsweredPostSurveys() > 0);
        let hasPreSurveys = this.preQuestions.length > 0;
        if (hasAnswered || hasPreSurveys) {
            this.callCancelWizardPage();
        } else {
            this.callAfterCancelledPage();
        }
    }
}
export = HactWizardForm;
