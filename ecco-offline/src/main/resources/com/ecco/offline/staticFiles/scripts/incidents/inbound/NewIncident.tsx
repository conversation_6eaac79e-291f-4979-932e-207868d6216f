import * as React from "react";
import {FC, useState, ReactElement} from "react";
import {
    possiblyModalForm
} from "ecco-components-core";
import {Grid, Button, ButtonGroup} from '@eccosolutions/ecco-mui';

const Wizard = React.lazy(() => import("./IncidentComponent").then(i => ({default: i.Wizard})));

const ModalWizard: FC<{linkContactId?: number | undefined, open: boolean, close: () => void}> = props => {
    return possiblyModalForm(
        "new incident",
        true,
        props.open,
        () => props.close(),
        () => props.close(),
        true, // TODO could emitChangesTo and see if there are any commands
        true,
        <Wizard linkContactId={props.linkContactId} schemaUri="incidents/$schema/" serviceCategorisationId={null}/>,
        undefined,
        undefined,
        undefined,
        "lg",
        undefined,
        true
    );
}

const ButtonRenderer = (onClick: () => void) => <Grid item>
    <ButtonGroup size="small" aria-label="new incident">
        <Button onClick={() => onClick()}>
            new incident
        </Button>
    </ButtonGroup>
    </Grid>;

export const NewIncident: FC<{linkContactId?: number | undefined, renderer?: ((onClick: () => void) => ReactElement) | undefined}> = props => {
    const [modal, setModal] = useState(false);

    return (<>
        {modal && <ModalWizard linkContactId={props.linkContactId} open={modal} close={() => setModal(false)}/>}
        {props.renderer
                ? props.renderer(() => setModal(true))
                : ButtonRenderer(() => setModal(true))}
    </>)
};
