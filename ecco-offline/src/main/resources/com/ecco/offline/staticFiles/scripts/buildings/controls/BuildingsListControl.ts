import $ = require("jquery");

import BaseAsyncListControl = require("../../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../../controls/BaseListEntryControl");
import Building = dto.Building;
import {apiClient} from "ecco-components";
import {BuildingAjaxRepository, LIST_DEF_IDS, SessionData} from "ecco-dto";
import * as dto from "ecco-dto/building-dto";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {showBuildingEditor} from "../components/BuildingForm";
import {rotaLinks} from "./BuildingOverviewControl";
import {applicationRootPath} from "application-properties";
import services = require("ecco-offline-data");

const repository = new BuildingAjaxRepository(apiClient);

type ListEntry = { building: Building, features: SessionData };

class EntryControl extends BaseListEntryControl<ListEntry> {

    constructor(building: ListEntry) {
        super(building, "fa fa-pencil");
    }
    protected administerEntry(): void {
        showBuildingEditor(this.entry.building.serviceRecipientId);
    }

    protected getEditElement(): $.JQuery {
        // TODO undo this hard-coded url for 'rooms' tab - we've lost the wide mode etc of WelcomeAppBar useRouteMatch
        return $("<span>")
            .append($("<a>").attr("href",  `${applicationRootPath}nav/r/main/bldg/${this.entry.building.serviceRecipientId}/`)
                .text(this.entry.building.name))
            // .append($("<a>").attr("href",  `${applicationRootPath}nav/r/welcome/buildings/${this.entry.building.buildingId}/`)
            //     .text(" [v1]"));
    }

    protected getEntryIconClass(): string {
        return "fa fa-home";
    }
}

/** Match either runs or anything but runs depending on showRuns */
function omitOrIncludeRuns(showRuns: boolean) {
    return (building: Building) => building.resourceTypeId == LIST_DEF_IDS.CARERUN_RESOURCETYPE_ID ? showRuns : !showRuns;
}

export class BuildingsListControl extends BaseAsyncListControl<ListEntry> {

    public static buildingsListControl(srId: number): Promise<BuildingsListControl> {
        return services.getBuildingRepository().findOneBuildingBySrId(srId).then(bldg => {
            return new BuildingsListControl(bldg.buildingId)
        });
    }

    constructor(private buildingId: number = null, private showRuns = false) {
        super(`add new ${buildingId == null ? "building" : "unit"}`, "no buildings defined", "fa fa-home");
    }

    protected fetchViewData(): Promise<ListEntry[]> {
        if (this.buildingId) {
            return repository.findAllBuildingsOf(this.buildingId)
                .then(buildings => buildings
                    .filter(b => !b.disabled)
                    .filter(omitOrIncludeRuns(this.showRuns))
                    .sort((a, b) => a.name.localeCompare(b.name)))
                .then(buildings =>
                    SessionDataService.getFeatures().then(features =>
                        buildings.map(building => ({building, features}))
                    )
                );
        }
        const params = new URLSearchParams(window.location.search);
        return repository.findAllBuildings({
            resourceType: params.get("resourceType") ?? undefined,
            showChildren: params.get("showChildren") as ("true" | "false") ?? undefined
        })
            .then(buildings => buildings.sort((a, b) => a.name.localeCompare(b.name)))
            .then(buildings =>
                SessionDataService.getFeatures().then(features =>
                    buildings
                        .filter(b => !b.disabled)
                        .map(building => ({building, features}))
                )
            );
    }

    protected createItemControl(entry: ListEntry) {
        const $menu = rotaLinks($("<div>").addClass("text-center"), entry);
        // We get a <div row> so add another xs=12 to contain the menu items
        const entryControl = new EntryControl(entry);
        entryControl.append(
            $("<div>").addClass("col-xs-12").append($menu)
        );
        return entryControl;
    }

    // new building or unit
    protected addNewEntity() {
        showBuildingEditor(null, this.buildingId);
    };

}
