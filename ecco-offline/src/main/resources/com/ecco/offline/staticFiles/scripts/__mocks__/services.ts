import { ReferralWithEntities } from 'ecco-dto';
import Mock = jest.Mock;

const mockReferral: Mock<Promise<ReferralWithEntities>> = jest.fn();
const mockClient: Mock<Promise<ReferralWithEntities>> = jest.fn();


mockReferral.mockResolvedValue({clientId: 100001} as ReferralWithEntities);
mockClient.mockResolvedValue({firstName: "George"} as ReferralWithEntities);

export = {
            getReferralRepository: () => ({             // NOTE: ({ is important - ensures we return the object
                findOneReferralWithEntities: mockReferral
            }),
            getClientRepository: () => ({
                findOneClient: mockClient
            })
        };
