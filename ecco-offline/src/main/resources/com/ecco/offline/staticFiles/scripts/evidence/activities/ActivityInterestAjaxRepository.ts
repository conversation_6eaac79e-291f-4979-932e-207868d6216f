import URI = require("URI");
import {ActivityInterestRepository} from "./ActivityInterestRepository"
import {ApiClient} from "ecco-dto";
import {EvidenceGroup} from 'ecco-dto';

export class ActivityInterestAjaxRepository implements ActivityInterestRepository {
    constructor(private apiClient: ApiClient) {
    }

    // GET referrals/{id}/evidence/needs/goals/" + actionDefId.toString() + "/activityType
    findActivityTypeIdsForGoal(serviceRecipientId: number, actionDefId: number): Promise<number[]> {
        var apiPath = URI("referrals/")
            .segmentCoded(serviceRecipientId.toString())
            .segment("evidence")
            .segment(EvidenceGroup.needs.name)
            .segment("goals")
            .segmentCoded(actionDefId.toString())
            .segment("activityTypes")
            .segment("");

        return this.apiClient.get<number[]>(apiPath);
    }
}
