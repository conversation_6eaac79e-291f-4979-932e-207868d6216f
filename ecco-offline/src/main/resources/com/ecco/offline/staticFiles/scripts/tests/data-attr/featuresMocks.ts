import {ListDefinitionEntryDto, SessionData, SessionDataDto, SessionDataGlobal, SessionDataRepository} from "ecco-dto";
import {TaskDefinition} from 'ecco-dto/service-config-dto';


export class MockSessionDataRepository implements SessionDataRepository {

        getSessionDataDto(): Promise<SessionDataDto> {
            return null;
        }

        getSessionDataGlobal(): Promise<SessionDataGlobal> {
            return this.getSessionData();
        }

        getSessionData(): Promise<SessionData> {

            const testDefs: { [id: string]: ListDefinitionEntryDto[] } = {};

            const parentListItem1: ListDefinitionEntryDto = {
                id: 11,
                name: "UK",
                listName: "my-countries",
                businessKey: "UK",
                disabled: false,
                defaulted: true,
                order: null
            };
            const parentListItem2: ListDefinitionEntryDto = {
                id: 12,
                name: "Japan",
                listName: "my-countries",
                businessKey: "Japan",
                disabled: false,
                defaulted: false,
                order: null
            };
            testDefs["my-countries"] = [parentListItem1, parentListItem2];

            const listItem1: ListDefinitionEntryDto = {
                id: 1,
                name: "english",
                listName: "my-languages",
                businessKey: "engligh",
                disabled: false,
                defaulted: false,
                parentId: 11,
                order: null
            };
            const listItem2: ListDefinitionEntryDto = {
                id: 2,
                name: "scottish",
                listName: "my-languages",
                businessKey: "scottish",
                disabled: false,
                defaulted: true,
                parentId: 11,
                order: null
            };
            const listItem3: ListDefinitionEntryDto = {
                id: 3,
                name: "welsh",
                listName: "my-languages",
                businessKey: "welsh",
                disabled: false,
                defaulted: false,
                parentId: 11,
                order: null
            };
            const listItem4: ListDefinitionEntryDto = {
                id: 4,
                name: "japanese",
                listName: "my-languages",
                businessKey: "japenses",
                disabled: false,
                defaulted: false,
                parentId: 12,
                order: null
            };
            testDefs["my-languages"] = [listItem1, listItem2, listItem3, listItem4];

            const parentChildListItem1: ListDefinitionEntryDto = {
                id: 5,
                name: "scouse",
                listName: "my-dialects",
                businessKey: "scouse",
                disabled: false,
                defaulted: false,
                parentId: 1,
                order: null
            };
            testDefs["my-dialects"] = [parentChildListItem1];


            const country1: ListDefinitionEntryDto = {
                id: 20,
                name: "England",
                listName: "my-countries2",
                businessKey: "england2",
                disabled: false,
                defaulted: true,
                order: null
            };
            const country2: ListDefinitionEntryDto = {
                id: 21,
                name: "Scotland",
                listName: "my-countries2",
                businessKey: "scotland",
                disabled: false,
                defaulted: false,
                order: null
            };
            testDefs["my-countries2"] = [country1, country2];

            const ceremonialCounty1: ListDefinitionEntryDto = {
                id: 22,
                name: "Greater London",
                listName: "my-counties",
                businessKey: "greaterLondon",
                disabled: false,
                defaulted: false,
                parentId: 20,
                order: null
            };
            testDefs["my-counties"] = [ceremonialCounty1];

            const district1: ListDefinitionEntryDto = {
                id: 23,
                name: "Lewisham",
                listName: "my-districts",
                businessKey: "lewisham",
                disabled: false,
                defaulted: false,
                parentId: 22,
                order: null
            };
            const district2: ListDefinitionEntryDto = {
                id: 24,
                name: "Islington",
                listName: "my-districts",
                businessKey: "islington",
                disabled: false,
                defaulted: false,
                parentId: 22,
                order: null
            };
            const district3: ListDefinitionEntryDto = {
                id: 25,
                name: "Fife",
                listName: "my-districts",
                businessKey: "fife",
                disabled: false,
                defaulted: false,
                parentId: 21,
                order: null
            };
            testDefs["my-districts"] = [district1, district2, district3];

            const taskDefinitions: TaskDefinition[] = [];

            const mockFeature = {
                userId: null,
                calendarId: null,
                calendarIdUserReferenceUri: null,
                individualUserSummary: null,
                roles: null,
                softwareModulesEnabled: {},
                featureSets: {global: {featureVotes: {}}},
                appointmentTypes: [],
                listDefinitions: testDefs,
                taskDefinitions: taskDefinitions,
                restrictedServicesProjects: {services: []},
                services: [],
                projects: [],
                serviceCategorisations: [],
                serviceTypesById: {},
                settings: {},
                supportOutcomes: [],
                riskAreas: [],
                questionGroups: [],
                exitReasons: [],
                flags: [],
                signpostReasons: [],
                restrictedServiceCategorisations: []
            } as any as SessionDataDto;

            return Promise.resolve( new SessionData(mockFeature) );
    }
}

export const featureRepository: SessionDataRepository = new MockSessionDataRepository();
