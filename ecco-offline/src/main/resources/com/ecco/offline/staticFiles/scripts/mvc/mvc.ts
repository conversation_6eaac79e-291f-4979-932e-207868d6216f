import _ = require("lodash");
import * as applicationProperties from "application-properties";
import {AuthenticationException, NOT_FOUND_ERROR, stringifyPossibleError} from "ecco-offline-data"
import {FlashScope} from "@eccosolutions/ecco-common";
import mapping = require("./mapping");
import MvcBrowserEnvironment = require("./MvcBrowserEnvironment");
import MvcUtils = require("./MvcUtils");
import URI = require("URI");


export interface ModelEnvironment {
    requestUri: URI.URI;
    requestPath: string[];
    applicationRootUri: URI.URI;
}

class ModelEnvironmentImpl implements ModelEnvironment {
    constructor(public requestUri: URI.URI,
            public requestPath: string[],
            public applicationRootUri: URI.URI) { }
}

class RenderOutcome {
    constructor(public uri: URI.URI) { }
    title:string;
}

export interface RenderEnvironment<TModel> {
    requestUri: URI.URI;
    applicationRootUri: URI.URI;
    model:TModel;
    redirect(targetUri:URI.URI):void;
    title(title:string):void;
}

class RenderEnvironmentImpl<TModel> implements RenderEnvironment<TModel> {
    private outcome:RenderOutcome;

    constructor(public requestUri: URI.URI,
            public applicationRootUri: URI.URI,
            outcome: RenderOutcome,
            public model: TModel) {
        this.outcome = outcome;
    }

    public redirect(targetUri:URI.URI) {
        this.outcome.uri = targetUri ? URI(targetUri) : null;
    }

    public title(title:string) {
        this.outcome.title = title;
    }
}

export interface ViewEnvironment<TModel> {
    model: TModel;
    rootElement: HTMLElement;
};

export interface Controller<TModel> {
    model(environment: ModelEnvironment): Promise<TModel>;
    render(environment: RenderEnvironment<TModel>): string;

    /** Provides callback with document.body, once body.innerHtml has been set with the content */
    view(environment: ViewEnvironment<TModel>): void;
}

class RedirectController implements Controller<void> {
    constructor(targetUri: URI.URI) {
        this.render = (environment: RenderEnvironment<void>): string => {
            environment.redirect(targetUri);
            return null;
        };
    }

    public model(environment: ModelEnvironment) {
        return Promise.resolve(null);
    }

    public render: (environment: RenderEnvironment<void>) => string;

    public view(environment: ViewEnvironment<void>) {
        // do nothing
    }
}


export class MvcSystem {
    private browserEnvironment:MvcBrowserEnvironment;
    private applicationRootUri:URI.URI;
    private controllerMappings = new mapping.ControllerMappings<Controller<any>>();

    constructor(browserEnvironment:MvcBrowserEnvironment) {
        this.browserEnvironment = browserEnvironment;
        this.applicationRootUri = MvcUtils.getApplicationRootUri();
    }


    public registerController<TModel>(path:string, controller:Controller<TModel>);
    public registerController<TModel>(path:any[], controller:Controller<TModel>);

    public registerController<TModel>(path:any, controller:Controller<TModel>) {
        this.controllerMappings.registerController(path, controller);
    }

    public registerRedirect(path:string, targetUri:string);
    public registerRedirect(path:string, targetUri:string[]);
    public registerRedirect(path:string, targetUri:URI.URI);
    public registerRedirect(path:any[], targetUri:string);
    public registerRedirect(path:any[], targetUri:string[]);
    public registerRedirect(path:any[], targetUri:URI.URI);

    public registerRedirect(path:any, targetUri:any) {
        var targetUriUri:URI.URI;
        if (_.isArray(targetUri)) {
            targetUriUri = URI("").
                    segmentCoded(<string[]>targetUri).
                    absoluteTo(this.applicationRootUri);
        } else if (!(targetUri instanceof URI)) {
            // If targetUri is a path, always treat it as relative to the
            // application root path, for consistency with the other
            // argument.
            var targetUriUri = URI(String(targetUri));
            if (!targetUriUri.scheme() && !targetUriUri.authority() && !targetUriUri.query() && !targetUriUri.fragment()) {
                targetUriUri = URI(".").
                        segment(targetUriUri.segment()).
                        absoluteTo(this.applicationRootUri);
            }
        }

        this.registerController(path, new RedirectController(targetUriUri));
    }

    private static NavigateType = {
        NAVIGATE: 'navigate',
        REDIRECT: 'redirect',
        START: 'start'
    };


    private doNavigateOrRedirect(uriObj: any, navigateType: string) {
        const mvcSystem = this;

        if (!(uriObj instanceof URI)) {
            uriObj = String(uriObj);
        }

        const currentUri = mvcSystem.browserEnvironment.location();

        const targetUri = URI(uriObj).normalize().absoluteTo(currentUri);

        new Promise<{controller, modelEnvironment}>((resolve, reject) => {
            // doing try/catch to allow us to catch stuff below
            try {
                if (targetUri.scheme() !== currentUri.scheme()
                    || targetUri.authority() !== currentUri.authority()) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw NOT_FOUND_ERROR;
                }

                const targetPath = MvcUtils.getTargetPathComponents(mvcSystem.applicationRootUri, targetUri);

                let controller = mvcSystem.controllerMappings.resolveController(targetPath);

                if (!controller) {
                    if (_.last(targetPath) !== "") {
                        const directoryPath: string[] = targetPath.concat([""]);
                        controller = mvcSystem.controllerMappings.resolveController(directoryPath);

                        if (controller) {
                            targetUri.segment(targetUri.segment().concat([""]));
                        }
                    }
                }

                if (!controller) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw NOT_FOUND_ERROR;
                }

                const modelEnvironment = new ModelEnvironmentImpl(targetUri, targetPath, mvcSystem.applicationRootUri);
                resolve({controller, modelEnvironment});
            } catch (e) {
                reject(e);
            }

        }).then(({controller, modelEnvironment}): void => {
            const model = controller.model(modelEnvironment);

            const renderOutcome = new RenderOutcome(targetUri);
            const renderEnvironment = new RenderEnvironmentImpl<any>(targetUri, mvcSystem.applicationRootUri, renderOutcome, model);
            const body = controller.render(renderEnvironment);

            if (navigateType === MvcSystem.NavigateType.NAVIGATE) {
                mvcSystem.browserEnvironment.navigate(renderOutcome.uri, renderOutcome.title, body);
            } else {
                mvcSystem.browserEnvironment.replace(renderOutcome.uri, renderOutcome.title, body);
            }

            controller.view({
                model: model,
                rootElement: document.body
            });
        }).catch((error) => {
            if (error === NOT_FOUND_ERROR) {
                if (navigateType === MvcSystem.NavigateType.NAVIGATE) {
                    mvcSystem.browserEnvironment.navigate(targetUri);
                } else if (navigateType === MvcSystem.NavigateType.REDIRECT) {
                    mvcSystem.browserEnvironment.replace(targetUri);
                } else {
                    // TODO use error controller
                    mvcSystem.browserEnvironment.replace(targetUri, "Not Found", "<h1>Not Found</h1>");
                }
            } else if (error instanceof AuthenticationException) {
                const loginUri = URI("offline/login")
                    .absoluteTo(URI(applicationProperties.applicationRootPath));
                mvcSystem.browserEnvironment.navigate(loginUri);
                FlashScope.put("success-url", targetUri.toString());
                FlashScope.put("message", "You need to login to visit that page");
            } else {
                // TODO use error controller
                mvcSystem.browserEnvironment.replace(targetUri, "Render Error", "<h1>Render Error</h1>"
                        + "<pre>" + stringifyPossibleError(error) + "</pre>");
                try {
                    console.error("Render Error: %o", error);
                } catch (ignored) {
                }
                throw error;
            }
        });
    }

    public redirect(uri:string):void;
    public redirect(uri:URI.URI):void;

    public redirect(uri:any):void {
        this.doNavigateOrRedirect(uri, MvcSystem.NavigateType.REDIRECT);
    }

    public navigate(uri:string):void;
    public navigate(uri:URI.URI):void;

    public navigate(uri:any):void {
        this.doNavigateOrRedirect(uri, MvcSystem.NavigateType.NAVIGATE);
    }

    public start() {
        this.doNavigateOrRedirect(this.browserEnvironment.location(), MvcSystem.NavigateType.START);
    }
}
