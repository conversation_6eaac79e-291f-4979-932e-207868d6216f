import View = require("../controls/View");
import EmbeddedView = require("../controls/EmbeddedView");


/**
 * Wraps an EmbeddedView such that this form can replace the provided EmbeddedView to provide embedded functionality.
 * Embedded functionality is such that the outer container becomes aware of a forms modal-like concepts,
 * so we can disable navigation when the form is dirty and potentially provide extra submit and cancel elements to
 * place in more prominent and consistent locations outside the embedded form (eg the nav bar gets replaced).
 */
class EmbeddedForm implements View {

    //private submitContainer = new ElementContainer(); // allows the outer container to loca
    //private submitCompleted: () => void;
    //private cancelContainer = new ElementContainer();
    //private wrapperContainer = new ElementContainer()
            //.append(this.submitContainer).append(this.cancelContainer);

    /**
     * @param disableNavigation Allows us to call the container to disable navigation should the form be dirty
     * @param navigateOnFinish Allows us to navigate away when cancelled or completed
     * @param embeddedView The control which is being embedded
     */
    constructor(
            private disableNavigation: () => void,
            private navigateOnFinish: () => void,
            private embeddedView: EmbeddedView) {
        embeddedView.onFormDirty(() => this.formDirty());
        embeddedView.onFormCompleted(() => navigateOnFinish());
    }

    public formDirty() {
        this.disableNavigation();
    }

    title() {
        return this.embeddedView.title();
    }

    element() {
        return this.embeddedView.element();
    }

    getFooter() {
        return this.embeddedView.getFooter();
    }

}
export = EmbeddedForm;
