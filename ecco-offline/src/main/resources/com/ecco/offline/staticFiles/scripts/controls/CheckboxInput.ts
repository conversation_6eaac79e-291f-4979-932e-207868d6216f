import $ = require("jquery");
import BaseControl = require("./BaseControl");
import InputControl = require("./InputControl");

class CheckboxInput extends BaseControl implements InputControl {

    private id: string;
    private value: string;
    private onChange: (value: string, state: boolean) => void;
    private $input: $.JQuery;

    public constructor(private label: string, id?: string, value?: string, $icon?: $.JQuery) {
        super($("<span>", id));
        id = id || label; // default to label.
        value = value || id; // default to id
        this.$input = $('<input>', {'class': 'checkbox', type: 'checkbox', id: id, value: value});
        if (label != null) {
            var $lbl = $('<label>', {'for': id}).append(this.$input).append(label);
            this.element().addClass("checkbox").append($lbl);
        } else {
            this.element().addClass("checkbox").append(this.$input);
        }
        if ($icon) {
            this.element().append($icon.css("padding-left", "10px"));
        }
        this.id = id;
        this.value = value;
    }

    public change( onChange: (value: string, state: boolean) => void ): this {
        if (!onChange) return this;

        this.onChange = onChange;

        this.$input.change( (event: $.JQueryEventObject) => {
            var val = $(event.delegateTarget).val();
            this.onChange(val, this.isChecked());
        });

        return this;
    }

    public setChecked(isChecked: boolean) {
        this.$input.prop('checked', isChecked);
        return this;
    }

    public setReadOnly() {
        this.$input.attr('disabled', 'disabled');
    }

    public isChecked(): boolean {
        return this.$input.is(":checked");
    }

    /**
     * Return the string value, or null if there is no value.
     * @param {boolean} hasInitialValue could be calculated, but easier as-is for now
     * NB it is assumed hasInitialValue is false with an initialValue of null or undefined
     */
    public getValueOrNull(hasInitialValue: boolean): string {
        const rawVal: boolean = this.isChecked(); // jquery :is returns boolean
        return hasInitialValue
            // return what we have - true or false, both are valid
            ? rawVal.toString()
            // if no initial value, then false means no value, return null
            : rawVal == true ? "true" : null;
    }

    public getId() { return this.id; }
    public getLabel() { return this.label; }
    public getValue() { return this.value; }
}

export = CheckboxInput;
