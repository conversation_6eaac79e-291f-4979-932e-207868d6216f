import $ = require("jquery");
import BaseAsyncTableControl = require("../controls/BaseAsyncTableControl");
import BaseTableRowControl = require("../controls/BaseTableRowControl");
import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient, getGlobalEccoAPI} from "ecco-components";
import {ReferralAjaxRepository, SessionData} from "ecco-dto";
import {ReferralSummaryDto} from "ecco-dto/referral-dto";
import {openReferral} from "../clientdetails/components/ClientReferralsPopup";
import ActionButton from "../controls/ActionButton";

var repository = new ReferralAjaxRepository(apiClient);

type ReferralSummaryColumnMappings = StringToObjectMap<(dto: ReferralSummaryDto) => string|$.JQuery>;

class RowControl extends BaseTableRowControl<ReferralSummaryDto> {

    constructor(private columnMappings: ReferralSummaryColumnMappings,
                private referral: ReferralSummaryDto) {
        super(referral);
    }

    protected override getColumnMapping(): ReferralSummaryColumnMappings {
        return this.columnMappings;
    }

}

/**
 * Called from RelationshipNode and /secure/entity/clients/ which loads clientInit, and referralsForClientDef which seems unused
 */
class ReferralsForClientControl extends BaseAsyncTableControl<ReferralSummaryDto> {

    constructor(private clientId: number,
                private openParent: boolean,
                private sessionData: SessionData, // this could be servicesprojects-dto.Services as per ReferralsListControl
                private referrals?: ReferralSummaryDto[]) {
        super();
    }

    protected fetchViewData(): Promise<ReferralSummaryDto[]> {
        if (this.referrals) {
            return Promise.resolve(this.referrals);
        }
        // handle _readOnly below
        return repository.findAllReferralWithoutSecuritySummaryByClient(this.clientId);
    }

    protected createRowControl(referral: ReferralSummaryDto) {
        return new RowControl(this.getColumnMapping(), referral);
    }

    protected getHeaders() {
        return this.getHeadingsFromColumnMapping();
    }

    protected getColumnMapping(): ReferralSummaryColumnMappings {
        const messages = getGlobalEccoAPI().sessionData.getMessages()
        return {
            "r-id": item => item.referralCode || item.referralId.toString(),
            "name": item => this.renderNameWithLink(item),
            "to": item => this.renderServiceDescription(item),
            "received": item => this.toShortDateOrBlank(item.receivedDate),
            "start": item => this.toShortDateOrBlank(item.receivingServiceDate),
            "status now": item => messages[item.statusMessageKey]
        };
    }

    private renderServiceDescription(item: ReferralSummaryDto): $.JQuery {
        const svcCat = this.sessionData.getServiceCategorisation(item.serviceAllocationId);
        let serviceName = this.sessionData.getServiceName(svcCat.serviceId);
        let projectName = svcCat.projectId ? this.sessionData.getProjectName(svcCat.projectId) : null;
        return projectName
                ? $("<span>").html(serviceName + "<br> - " + projectName)
                : $("<span>").text(serviceName);
    }

    private toShortDateOrBlank(isoDate?: string) {
        if (isoDate) {
            return EccoDate.iso8601ToFormatShort(isoDate);
        } else {
            return "";
        }
    }

    protected renderNameWithLink(r: ReferralSummaryDto, disableOpenParent = false) {
        if (r._readOnly) {
            return $("<span>")
                .text(r.lastName + ", " + r.firstName);
        } else {
            const $btn = new ActionButton(r.lastName + ", " + r.firstName, undefined, true)
                .clickSynchronous(() => openReferral(this.sessionData, r, disableOpenParent));
            return $btn.element();
        }
    }

    private getHeadingsFromColumnMapping(): any[] {
        let columnMappings = this.getColumnMapping();
        var keys: string[] = [];
        for (var key in columnMappings) {
            if(columnMappings.hasOwnProperty(key)) { //to be safe
                keys.push(key);
            }
        }
        return keys;
    }

}

export = ReferralsForClientControl;

