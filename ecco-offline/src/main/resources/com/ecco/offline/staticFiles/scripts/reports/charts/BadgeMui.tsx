import * as React from "react";
import {
    Card,
    CardContent,
    createSty<PERSON>,
    Grid, LinearProgress,
    makeStyles,
    Theme, Typography,
} from "@eccosolutions/ecco-mui";
import {componentAsElementForMui} from "../../components/MUIAsElement";


//************
// Based on article: https://blog.statsbot.co/material-ui-dashboard-with-react-3fef78061733
// which integrates an independent engine from the db to visuals - cube.js
// but we just take the building of the dashboard
//************

// BADGE
// react-countup
const useStylesKPI = makeStyles((theme) => ({
    root: {
        height: '100%',
    },
    content: {
        alignItems: 'center',
        display: 'flex',
    },
    title: {
        fontWeight: 500,
    },
    progress: {
        marginTop: theme.spacing(3),
        height: '8px',
        borderRadius: '10px',
    },
    difference: {
        marginTop: theme.spacing(2),
        display: 'flex',
        alignItems: 'center',
    },
    differenceIcon: {
        color: theme.palette.error.dark,
    },
    differenceValue: {
        marginRight: theme.spacing(1),
    },
    green: {
        color: theme.palette.success.dark,
    },
    red: {
        color: theme.palette.error.dark,
    },
}));

const KPIChart = (props: {title: string, value: string, progressUp: string, progressDown: string}): any => {
    const classes = useStylesKPI();

    // originally used with...
    //const card = {title: 'referrals', value: 100, difference: -10, progress: false, duration: 1.5};
    const difference = null;
    const progress = null;
    const duration = null;
    const { title, value, progressUp, progressDown, ...rest } = props;
    /*
    const { className, title, progress, query, difference, duration, ...rest } = props;
    const { resultSet, error, isLoading } = useCubeQuery(query);
    const differenceValue = useCubeQuery(differenceQuery);
    if (isLoading || differenceValue.isLoading) {
        return (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress color="secondary" />
                </div>
        );
    }
    if (error || differenceValue.error) {
        return <pre>{(error || differenceValue.error).toString()}</pre>;
    }
    */
    return (
            /*<Card {...rest} className={clsx(classes.root, className)}>*/
            <Card {...rest} className={classes.root}>
                <CardContent>
                    <Grid container justify="space-between">
                        <Grid item>
                            <Typography className={classes.title} color="textSecondary" gutterBottom variant="body2">
                                {title}
                            </Typography>
                            <Typography variant="h4">
                                {/*<CountUp
                                        end={fullValue}
                                        duration={duration}
                                        separator=","
                                        decimals={0}
                                />*/}
                                {value}
                            </Typography>
                        </Grid>
                    </Grid>
                    {progressUp ? (
                            <span className={classes.green}>{progressUp}</span>
                    ) : null}
                    {progressDown ? (
                            <span className={classes.red}>{progressDown}</span>
                    ) : null}
                    {progress ? (
                            <LinearProgress
                                    className={classes.progress}
                                    value={Number(value)}
                                    variant="determinate"
                            />
                    ) : null}
                    {difference ? (
                            <div className={classes.difference}>
                                <Typography className={classes.differenceValue} variant="body2">
                                    {Number(value) > 1 ? (
                                            <span className={classes.green}>{value}%</span>
                                    ) : (
                                            <span className={classes.red}>{value}%</span>
                                    )}
                                </Typography>
                                {/*<Typography className={classes.caption} variant="caption">*/}
                                <Typography variant="caption">
                                    in the period
                                </Typography>
                            </div>
                    ) : null}
                </CardContent>
            </Card>
    );
};

const useStylesMain = makeStyles((theme: Theme) => // NOTE: Must always have root:
        createStyles({
            root: {
                //backgroundColor: 'lightgrey',
                padding: '5px'
            }
        })
);

export const BadgeMui = (props: {title: string, value: string, progressUp: string, progressDown: string}) => {
    const classes = useStylesMain();
    return <>
            <div>
                <Grid className={classes.root} container spacing={4}>
                    <Grid key={props.title} item xs={12}>
                        <KPIChart {...props}/>
                    </Grid>
                </Grid>
            </div>
    </>
};

export function badgeMuiAsElement(id: string, title: string, value: string, progressUp: string, progressDown: string) {
    return componentAsElementForMui(<BadgeMui title={title} value={value} progressUp={progressUp} progressDown={progressDown}/>, id);
}
