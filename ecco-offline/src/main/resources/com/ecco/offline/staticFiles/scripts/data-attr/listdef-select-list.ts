import $ = require("jquery");
import ListDefSelectList = require("./ListDefSelectList");
import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";

var repositoryDefault = new SessionDataAjaxRepository(apiClient);

/**
 * Find select list elements with data-select-list-url attribute, and populate from that list.
 */
class Enhancer {

    constructor() {
    }

    /** Find the different items we support and attach the appropriate component to each of them */
    public attach() {
        var $roots = $(".listdef-select-list");
        $roots.each( (index, element) => {
            new ListDefSelectList($(element), repositoryDefault).load();
        });
    }
}


var enhancer = new Enhancer();
enhancer.attach();

