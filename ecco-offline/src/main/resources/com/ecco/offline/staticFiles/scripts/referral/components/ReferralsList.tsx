import * as React from "react";
import { FC, useState, useEffect, useMemo } from "react";
import {
    Box,
    Typography,
    CircularProgress,
    Grid,
    Alert,
    TablePagination
} from "@eccosolutions/ecco-mui";
import { useDebounce } from "ecco-components-core";
import { ReferralDto } from "ecco-dto";
import { useReferrals, useReferralsFromBuildingSrId } from "../hooks/useReferrals";
import { ReferralsSearchFilter, MeOrTeam } from "./ReferralsSearchFilter";
import { ReferralItem } from "./ReferralItem";
import events from "../../../common/events";
import $ from "jquery";

interface ReferralsListProps {
    /** Whether to show only current user's referrals */
    myReferrals?: boolean;
    /** Building ID to filter by */
    buildingId?: number;
    /** Force online mode */
    forceOnline?: boolean;
    /** Pre-loaded referrals promise */
    liveReferralsQ?: Promise<ReferralDto[]>;
    /** Building SR ID (alternative to buildingId) */
    buildingSrId?: number;
}

/**
 * Modern React component to replace ReferralsListControl
 */
export const ReferralsList: FC<ReferralsListProps> = ({
    myReferrals = false,
    buildingId,
    forceOnline = false,
    liveReferralsQ,
    buildingSrId
}) => {
    // State for search and filtering
    const [searchInput, setSearchInput] = useState<string>("");
    const [meOrTeamValue, setMeOrTeamValue] = useState<MeOrTeam>(
        myReferrals ? "my clients" : "team clients"
    );

    // Pagination state
    const [pageNumber, setPageNumber] = useState<number>(0);
    const pageSize = 20;

    // Debounce search input
    const debouncedSearch = useDebounce(searchInput, 300);

    // Use different hook based on whether we have buildingSrId
    const buildingResult = buildingSrId ? useReferralsFromBuildingSrId(buildingSrId) : null;

    const directResult = useReferrals({
        filter: {
            myReferrals: meOrTeamValue === "my clients",
            buildingId: buildingId || buildingResult?.building?.buildingId,
            search: debouncedSearch
        },
        forceOnline,
        liveReferralsQ
    });

    // Use building result if available, otherwise direct result
    const { referrals, sessionData, loading, error, building } = buildingResult || directResult;

    // Update page title based on context
    useEffect(() => {
        const label = buildingId || building
            ? "buildings"
            : meOrTeamValue === "my clients" ? "my live referrals" : "live referrals";

        const $menu = $("<div>").css({"text-align": "center"}).text(label);
        events.MenuUpdateEvent.bus.fire(new events.MenuUpdateEvent("nav", $menu));
    }, [buildingId, building, meOrTeamValue]);

    // Paginated referrals
    const paginatedReferrals = useMemo(() => {
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        return referrals.slice(startIndex, endIndex);
    }, [referrals, pageNumber, pageSize]);

    // Handle me/team filter change
    const handleMeOrTeamChange = (value: MeOrTeam) => {
        setMeOrTeamValue(value);
        setPageNumber(0); // Reset to first page when filter changes
    };

    // Handle pagination
    const handleChangePage = (event: unknown, newPage: number) => {
        setPageNumber(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        // Keep page size fixed for now
    };

    // Show loading state
    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={4}>
                <CircularProgress />
            </Box>
        );
    }

    // Show error state
    if (error) {
        return (
            <Box p={2}>
                <Alert severity="error">
                    Error loading referrals: {error.message || "Unknown error"}
                </Alert>
            </Box>
        );
    }

    const showMeOrTeamFilter = forceOnline && !buildingId && !building;
    const isSearch = debouncedSearch.trim() !== "";

    return (
        <Grid container justify="center">
            <Grid item xs={12} md={10} lg={8}>
                {/* Search and Filter */}
                <ReferralsSearchFilter
                    searchInput={searchInput}
                    onSearchInputChange={setSearchInput}
                    meOrTeamValue={meOrTeamValue}
                    onMeOrTeamChange={handleMeOrTeamChange}
                    showMeOrTeamFilter={showMeOrTeamFilter}
                />

                {/* Results */}
                <Box p={2} pt={0}>
                    {isSearch && (
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                            showing {referrals.length} result{referrals.length !== 1 ? 's' : ''}
                        </Typography>
                    )}

                    {referrals.length === 0 ? (
                        <Box textAlign="center" p={4}>
                            <Typography variant="body1" color="textSecondary">
                                {isSearch
                                    ? `No referrals found matching "${debouncedSearch}"`
                                    : "No referrals found"
                                }
                            </Typography>
                        </Box>
                    ) : (
                        <>
                            {paginatedReferrals.map((referralGroup, index) => (
                                <ReferralItem
                                    key={referralGroup.referrals[0].clientId}
                                    referrals={referralGroup}
                                    forceOnline={forceOnline}
                                />
                            ))}

                            {/* Pagination - only show when not searching and have results */}
                            {!isSearch && referrals.length > pageSize && (
                                <Box display="flex" justifyContent="center" mt={2}>
                                    <TablePagination
                                        rowsPerPageOptions={[pageSize]}
                                        component="div"
                                        count={referrals.length}
                                        rowsPerPage={pageSize}
                                        page={pageNumber}
                                        onChangePage={handleChangePage}
                                        onRowsPerPageChange={handleChangeRowsPerPage}
                                        backIconButtonProps={{
                                            "aria-label": "Previous Page"
                                        }}
                                        nextIconButtonProps={{
                                            "aria-label": "Next Page"
                                        }}
                                    />
                                </Box>
                            )}
                        </>
                    )}
                </Box>
            </Grid>
        </Grid>
    );
};
