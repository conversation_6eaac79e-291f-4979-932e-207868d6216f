package com.ecco.integration.northgate;

import com.ecco.dto.ClientDefinition;
import com.ecco.integration.api.EccoIntApiActor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.client.RestClientException;

import java.net.URI;
import java.net.URISyntaxException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;

/**
 * Test the behaviour of our Spring Boot app.
 *
 * @see <a href="http://docs.spring.io/spring-boot/docs/current/reference/html/boot-features-testing.html">Spring Boot Reference</a>
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(webEnvironment = RANDOM_PORT, classes = Application.class)
@TestPropertySource(locations="classpath:test.properties")
// TODO: Extract abstract superclass for teh test behaviour which will be same for all integrations
public class ApplicationTests {

    private TestRestTemplate template = new TestRestTemplate();
    private EccoIntApiActor actor = new EccoIntApiActor(template.getRestTemplate());

    private static final String GENDER = "FEMALE";
    private static final String DISABILITY_NO = "NO";

    @Value("${local.server.port}")
    private int httpPort;

    @Test
    public void shouldFindOneResult() throws URISyntaxException {
        ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                .lastName("Br")
                .firstName("Meg")
                .build();
        Iterable<ClientDefinition> queryClients = actor.queryClients("the source", getUri(), exemplar);
        assertThat(queryClients).hasSize(1);
        ClientDefinition client = queryClients.iterator().next();
        assertIsFullMeganBryceRecord(client);
    }

    @Test
    public void shouldCreatePaulaFrankenstein() {
        // Not implemented for Northgate
        Assertions.assertThrows(RestClientException.class, () -> {
            ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                    .firstName("Paula")
                    .lastName("Frankenstein")
                    .genderKey(GENDER)
                    .externalClientSource("the source") // which source to create the client on
                    .build();
            ClientDefinition createdClient = actor.createClient(getUri(), exemplar);
            assertThat(createdClient).isNotNull();
            assertThat(createdClient.getExternalClientRef()).isEqualTo("TODO: I made this up");
        });
    }

    public void assertIsFullMeganBryceRecord(ClientDefinition client) {
        assertThat(client.getExternalClientRef()).isEqualTo("11111");
        assertThat(client.getFirstName()).isEqualTo("Megan");
        assertThat(client.getLastName()).isEqualTo("Bryce");
//        assertThat(client.getGender()).isEqualTo(GENDER);
//        assertThat(client.getBirthDate()).isEqualTo(dob);
        assertThat(client.getNi()).isEqualTo("TG703265U");
//        assertThat(client.getFirstLanguage()).isEqualTo(FIRST_LANGUAGE);
        assertThat(client.getDisabilityKey()).isEqualTo(DISABILITY_NO);
//        assertThat(client.getEthnicOrigin()).isEqualTo(ETHNIC_ORIGIN);
//        assertThat(client.getReligion()).isEqualTo(RELIGION);
//        assertThat(client.getSexuality()).isEqualTo(SEXUALITY);
//        assertThat(client.getPostCode()).isEqualTo(POST_CODE);
        assertThat(client.getAddress()).isEqualTo(new String[]{"Junction Way, Llandudno Junction, Conwy, LL31"});
    }


    private URI getUri() throws URISyntaxException {
        return new URI("http",null,"localhost",httpPort,"/northgate/", null, null );
    }

}
