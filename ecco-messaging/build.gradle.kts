
plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-config"))

    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("com.twilio.sdk:twilio:8.15.0")

    // Esendex SMS dependencies
    implementation(files("../setup/esendex-java-sdk-4.0.3.jar"))
    implementation("io.github.x-stream:mxparser")
    implementation("xmlpull:xmlpull")
    implementation("com.thoughtworks.xstream:xstream")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

description = "ecco-messaging"
