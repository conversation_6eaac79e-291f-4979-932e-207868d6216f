package com.ecco.dom.groupsupport;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("editattendance")
public class GroupActivityAttendanceCommand extends GroupSupportCommand {
    public GroupActivityAttendanceCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull UUID activityUuid, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, activityUuid, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected GroupActivityAttendanceCommand() {
    }
}
