package com.ecco.web;

import org.springframework.http.MediaType;

/**
 * Additional predefined {@link MediaType}s.
 * <p/>
 * Many useful {@link MediaType}s are defined as static constants by the
 * {@link MediaType} class. This interface is intended to define useful values
 * that are not already defined by {@link MediaType}.
 */
public interface EccoMediaTypes {

    String FONT_WOFF2_VALUE = "application/font-woff2";
    MediaType FONT_WOFF2 = MediaType.parseMediaType(FONT_WOFF2_VALUE);

    String IMAGE_PNG_VALUE = "image/png";
    MediaType IMAGE_PNG = MediaType.parseMediaType(IMAGE_PNG_VALUE);

    String IMAGE_SVG_VALUE = "image/svg+xml";
    MediaType IMAGE_SVG = MediaType.parseMediaType(IMAGE_SVG_VALUE);

    String TEXT_HTML_UTF8_VALUE = "text/html;charset=UTF-8";
    MediaType TEXT_HTML_UTF8 = MediaType.parseMediaType(TEXT_HTML_UTF8_VALUE);

    String TEXT_JAVASCRIPT_UTF8_VALUE = "text/javascript;charset=UTF-8";
    MediaType TEXT_JAVASCRIPT_UTF8 = MediaType.parseMediaType(TEXT_JAVASCRIPT_UTF8_VALUE);

    String TEXT_CACHE_MANIFEST_VALUE = "text/cache-manifest";
    MediaType TEXT_CACHE_MANIFEST = MediaType.parseMediaType(TEXT_CACHE_MANIFEST_VALUE);

    String TEXT_CSS_VALUE = "text/css";
    MediaType TEXT_CSS = MediaType.parseMediaType(TEXT_CSS_VALUE);

    String APPLICATION_SCHEMA_JSON_UTF8_VALUE = "application/schema+json;charset=UTF-8";
    MediaType APPLICATION_SCHEMA_JSON_UTF8 = MediaType.parseMediaType(APPLICATION_SCHEMA_JSON_UTF8_VALUE);
}
