package com.ecco.infrastructure.dom;

import java.util.UUID;

import com.ecco.infrastructure.Created;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;

/**
 * @deprecated Use {@link BaseIntKeyedCommand}
 */
@MappedSuperclass
@Access(AccessType.FIELD)
@Deprecated
public abstract class BaseLongKeyedCommand extends BaseCommand<Long> implements Created {

    @Nullable
    @Id
    @GeneratedValue(generator = "commandsTableGenerator")
    @TableGenerator(
            name = "commandsTableGenerator", initialValue = 1, pkColumnValue = "commands",
            allocationSize = 1, table = "hibernate_sequences")
    @Column(name = "id", nullable = false) // oracle doesn't like using unique=true
    private Long id = null;

    /**
     * @param uuid               A UUID uniquely identifying the command, used for de-duplication.
     * @param remoteCreationTime The time at which this command was created on the client.
     * @param userId             The ID of the user who submitted the command.
     * @param body               The body of the command as a JSON string.
     */
    protected BaseLongKeyedCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected BaseLongKeyedCommand() {
    }

    @Override
    @Nullable
    public Long getId() {
        return id;
    }

}
