package com.ecco.infrastructure.entity;

import java.util.UUID;

import javax.persistence.*;

import org.hibernate.annotations.Type;

/**
 * Abstract Entity that uses {@link javax.persistence.AccessType#FIELD} access.
 */
@MappedSuperclass
public abstract class AbstractIntKeyedEntityWithUuid extends AbstractIntKeyedEntity {

    private static final long serialVersionUID = 1L;

    @Column(name="uuid", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID uuid;


    public AbstractIntKeyedEntityWithUuid() {
    }

    protected AbstractIntKeyedEntityWithUuid(Integer id) {
        super(id);
    }

    @PrePersist
    public void populateUuidIfNull() {
        if (uuid == null) {
            uuid = UUID.randomUUID();
        }
    }

    public UUID getUuid() {
        return uuid;
    }
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }
}
