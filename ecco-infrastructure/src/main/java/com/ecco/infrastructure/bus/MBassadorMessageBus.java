package com.ecco.infrastructure.bus;

import lombok.extern.slf4j.Slf4j;
import net.engio.mbassy.bus.error.IPublicationErrorHandler;
import net.engio.mbassy.listener.Handler;
import net.engio.mbassy.listener.Listener;
import net.engio.mbassy.listener.References;
import org.mbassy.spring.Transaction;
import org.mbassy.spring.TransactionAwareMessageBus;
import org.springframework.context.ApplicationEvent;

import java.util.Collections;

import static net.engio.mbassy.bus.config.IBusConfiguration.Properties.PublicationErrorHandlers;

@Slf4j
public class MBassadorMessageBus implements MessageBus<ApplicationEvent> {
    private transient TransactionAwareMessageBus<ApplicationEvent> bus = new TransactionAwareMessageBus<>();

    public MBassadorMessageBus() {
        // add our own error handler - by default mbassy swallows the exception and logs to System.err through a default ConsoleLogger
        // (which should end up in catalina.out) via our 'bus' variable which holds an internalBus calling AbstractPubSubSupport
        // attempted to add to the error handler collection, but seems we're meant to overwrite since calling getRegisteredErrorHandlers returns an unmodifyable collection
        //Assert.state(this.bus.getRegisteredErrorHandlers().isEmpty(), "we need to add our error handler, not clobber what is there");
        IPublicationErrorHandler handler = error -> {
            log.error("Error handing mbassador messagebus event: {}", error.getMessage(), error.getCause());
            // attempted to re-throw exceptions that aren't intended to be caught - so they register in monitoring systems
            // however, this just gets caught by the bus' base class AbstractPubSubSupport default ConsoleLogger and logged
            //if (error.getCause().getCause() instanceof RuntimeException) {
            //    throw new RuntimeException(error.getCause());
            //}
        };
        this.bus.getRuntime().add(PublicationErrorHandlers, Collections.singletonList(handler));
    }

    @Override
    public <T extends ApplicationEvent> void publishNow(T message) {
        log.trace("Publishing event: {}", message);
        bus.post(message).now();
    }

    @Override
    public <T extends ApplicationEvent> void publishBeforeTxEnd(T message) {
        log.trace("Publishing event: {}", message);
        bus.post(message).before(Transaction.Commit().OrIfNoTransactionAvailable());
    }

    @Override
    public <T extends ApplicationEvent> void publishAfterTxEnd(T message) {
        log.trace("Publishing event (after tx): {}", message);
        bus.post(message).after(Transaction.Commit().OrIfNoTransactionAvailable());
    }

    @Override
    public <T extends ApplicationEvent> void subscribe(Class<? extends T> clazz, Subscriber<T> subscriber) {
        final SubscriptionAdapter<T> adapter = new SubscriptionAdapter<>(clazz, subscriber);
        log.debug("Added subscriber for {}: {}", clazz.getName(), subscriber);
        bus.subscribe(adapter);
    }

    @Listener(references = References.Strong) // has to be strong because the bus holds the only reference to it
    private class SubscriptionAdapter<T extends ApplicationEvent> {
        private final Class<? extends T> clazz;
        private final Subscriber<T> subscriber;

        private SubscriptionAdapter(Class<? extends T> clazz, Subscriber<T> subscriber) {
            this.clazz = clazz;
            this.subscriber = subscriber;
        }

        @Handler
        public void receive(T event) {
            if (clazz.isAssignableFrom(event.getClass())) {
                log.debug("Processing event {} with subscriber: {}", event, subscriber);
                subscriber.receive(event);
            } else {
                log.trace("Ignoring event {} for subscriber: {}", event, subscriber);
            }
        }
    }
}
