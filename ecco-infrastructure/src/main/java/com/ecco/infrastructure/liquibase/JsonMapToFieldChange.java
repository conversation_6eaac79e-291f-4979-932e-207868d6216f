package com.ecco.infrastructure.liquibase;

import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.infrastructure.hibernate.JSONClobMap;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.ISO8601DateFormat;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * Extract a property from a JSON map and place in a dedicated field
 */
public class JsonMapToFieldChange extends UpdateableResultSetChange {

    private static JSONClobMap<LocalDate> converter = new JSONClobMap<>(
            ConvertersConfig.getObjectMapper()
                    .setDateFormat(new ISO8601DateFormat())
                    .disable(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS)
            , LocalDate.class);

    private static DateTimeFormatter formatter = ISODateTimeFormat.date();

    private String dstColumnName;
    private String srcColumnName;
    private String srcMapKey;
    private Boolean removeOriginal;

    public String getDstColumnName() {
        return dstColumnName;
    }
    public String getSrcColumnName() {
        return srcColumnName;
    }
    public String getSrcMapKey() {
        return srcMapKey;
    }
    public void setDstColumnName(String columnName) {
        this.dstColumnName = columnName;
    }
    public void setSrcColumnName(String sourceColumnName) {
        this.srcColumnName = sourceColumnName;
    }
    public void setSrcMapKey(String sourceMapKey) {
        this.srcMapKey = sourceMapKey;
    }

    public Boolean isRemoveOriginal() { return removeOriginal; }
    public void setRemoveOriginal(Boolean removeOriginal) { this.removeOriginal = removeOriginal; }

    @Override
    protected String getAdditionalColumns() {
        return srcColumnName + "," + dstColumnName;
    }

    @Override
    protected void computeChange(ResultSet rs) throws SQLException {
        Map<String, LocalDate> map = converter.getFromClob(rs, this.srcColumnName);
        if (map == null) {
            return;
        }
        LocalDate extracted = map.get(srcMapKey);
        if (extracted != null) {
            String isoDate = formatter.print(extracted);
            java.time.LocalDate dt = java.time.LocalDate.parse(isoDate);
            rs.updateDate(dstColumnName, Date.valueOf(dt));
            if (removeOriginal) {
                map.remove(srcMapKey);
                try {
                    String updatedJson = converter.getMapper().writeValueAsString(map);
                    rs.updateString(srcColumnName, updatedJson);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

}
