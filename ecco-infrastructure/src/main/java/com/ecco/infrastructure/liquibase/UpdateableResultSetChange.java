package com.ecco.infrastructure.liquibase;

import lombok.Getter;
import lombok.Setter;
import org.jspecify.annotations.NonNull;
import org.springframework.jdbc.object.SqlQuery;
import org.springframework.jdbc.object.UpdatableSqlQuery;

import liquibase.database.Database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * Custom change for changes that involve computation in Java, such as cryptography.
 *
 * that will allow changes to be based on updates against an updateable {@link ResultSet}
 */
@Getter
@Setter
abstract public class UpdateableResultSetChange extends UpdateableBaseResultSetChange {

    @Override
    SqlQuery<Void> doExecute(final Database database) {

        UpdatableSqlQuery<Void> update = new AbstractUpdatableSqlQuery<>(database, generateSql(database)) {

            @NonNull
            @SuppressWarnings("rawtypes")
            @Override
            protected Void updateRow(@NonNull ResultSet rs, int rowNum, Map context) throws SQLException {
                try {
                    computeChange(rs);
                    if (rowNum % 500 == 0) {
                        log.info("Processed rows " + rowNum);
                    }
                } catch (SQLException e) {
                    String message = "Update failed for row: " + rs.getRow() + ", col1 = " + rs.getObject(1);
                    log.severe(message);
                    throw e;
                }

                return null;
            }
        };

        return update;
    }

}
