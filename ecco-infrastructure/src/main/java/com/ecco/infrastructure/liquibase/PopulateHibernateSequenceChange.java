package com.ecco.infrastructure.liquibase;

import liquibase.database.Database;
import liquibase.exception.CustomChangeException;
import liquibase.exception.DatabaseException;
import liquibase.executor.ExecutorService;
import liquibase.sql.UnparsedSql;
import liquibase.statement.core.RawSqlStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PopulateHibernateSequenceChange extends PopulateSequenceChange {

    /** Name of sequence in hibernate_sequences to use */
    private String sequenceName;

    @Override
    public void execute(Database database) throws CustomChangeException {
        try {
            setOrderIndex(readHibernateSequence(database));
            super.execute(database);
            writeHibernateSequence(database, getOrderIndex() + 1);
        } catch (DatabaseException e) {
            throw new CustomChangeException(e);
        }
    }

    private int readHibernateSequence(Database database) throws DatabaseException {

        String sql = "SELECT "
                + database.escapeColumnNameList("next_val")
                + " FROM "
                + database.escapeTableName(null, this.getSchemaName(), "hibernate_sequences")
                + " WHERE sequence_name='" + sequenceName + "'";
        sql = new UnparsedSql(sql).toSql();

        RawSqlStatement statement = new RawSqlStatement(sql);

        return ExecutorService.getInstance().getExecutor(database).queryForInt(statement);
    }

    private void writeHibernateSequence(Database database, int orderIndex) throws DatabaseException {
        log.info("Updating hibernate_sequence " + sequenceName + " to " + orderIndex);

        String sql = "UPDATE "
                + database.escapeTableName(null, this.getSchemaName(), "hibernate_sequences")
                + " SET "
                + database.escapeColumnNameList("next_val")
                + " = "
                + orderIndex
                + " WHERE sequence_name='" + sequenceName + "'";
        sql = new UnparsedSql(sql).toSql();

        RawSqlStatement statement = new RawSqlStatement(sql);

        ExecutorService.getInstance().getExecutor(database).execute(statement);
    }
}
