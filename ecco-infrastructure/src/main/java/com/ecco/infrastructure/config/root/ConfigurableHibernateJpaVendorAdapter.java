package com.ecco.infrastructure.config.root;


import org.jspecify.annotations.NonNull;
import javax.persistence.spi.PersistenceProvider;

import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;


final class ConfigurableHibernateJpaVendorAdapter extends HibernateJpaVendorAdapter {

    private PersistenceProvider persistenceProvider;


    public ConfigurableHibernateJpaVendorAdapter(PersistenceProvider persistenceProvider) {
        super();
        this.persistenceProvider = persistenceProvider;
    }


    @NonNull
    @Override
    public PersistenceProvider getPersistenceProvider() {
        return persistenceProvider;
    }
}