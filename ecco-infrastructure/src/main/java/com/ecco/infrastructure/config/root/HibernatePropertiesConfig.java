package com.ecco.infrastructure.config.root;

import com.ecco.infrastructure.hibernate.CompoundInterceptor;
import com.ecco.infrastructure.hibernate.CreatedInterceptor;
import com.ecco.infrastructure.hibernate.HibernateOnSaveInterceptor;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

import static org.hibernate.cfg.Environment.*;

/**
 * Replaces applicationContext-hibernate-props.xml
 *
 * @since 16/04/2013
 */
@Configuration
public class HibernatePropertiesConfig {
    @Autowired
    private EccoEnvironment env;

    @Bean
    public StatementStatsInterceptor statementStatsInterceptor() {
        return new StatementStatsInterceptor();
    }

    @Bean
    public PropertiesFactoryBean hibernateProperties() {
        return new PropertiesFactoryBean() {
            @Override
            @NonNull
            protected Properties createProperties() {
                final Properties properties = new Properties();
                properties.setProperty(DEFAULT_SCHEMA, env.getDbSchema());
                properties.setProperty(USE_NEW_ID_GENERATOR_MAPPINGS, "true");
                // Required to keep cosmo using hibernate_sequences. Default changed in Hib 5.3. See https://stackoverflow.com/a/52367106/1998186
                properties.setProperty(PREFER_GENERATOR_NAME_AS_DEFAULT_SEQUENCE_NAME, "false");
                properties.setProperty(HBM2DDL_AUTO, env.getProperty("hibernate.hbm2ddl"));
                properties.setProperty(MAX_FETCH_DEPTH, "3");
                properties.setProperty(SHOW_SQL, env.getProperty("jdbc.showSql"));
                properties.setProperty(USE_SQL_COMMENTS, env.getProperty("jdbc.showSqlComments"));
                properties.setProperty(USE_REFLECTION_OPTIMIZER, "false");
                properties.setProperty(GENERATE_STATISTICS, "true");
                properties.setProperty(LOG_SESSION_METRICS, "false");
                properties.setProperty(USE_QUERY_CACHE, "true");
                properties.setProperty(USE_SECOND_LEVEL_CACHE, "true");
                properties.setProperty(USE_STRUCTURED_CACHE, "true");
                properties.setProperty(CACHE_REGION_FACTORY, "org.hibernate.cache.jcache.internal.JCacheRegionFactory");
                properties.setProperty(STATEMENT_BATCH_SIZE, "25");
                properties.setProperty("org.hibernate.envers.store_data_at_delete", "true"); // seems later version have EnversSettings interface
                properties.setProperty("org.hibernate.envers.audit_table_suffix", "_aud");
                // According to http://www.warski.org/blog/2011/04/envers-and-hibernate-4-0-0-alpha2-automatic-listener-registration/
                // we don't need to add the JpaPostXXEventListener or audit listener manually any more.
                properties.setProperty(INTERCEPTOR, CompoundInterceptor.class.getName());
                CompoundInterceptor.registerInterceptor(new CreatedInterceptor());
                CompoundInterceptor.registerInterceptor(new HibernateOnSaveInterceptor());
                CompoundInterceptor.registerInterceptor(statementStatsInterceptor());
                return properties;
            }
        };
    }
}
