package com.ecco.infrastructure.config.root;

import org.apache.tomcat.jdbc.pool.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.orm.jpa.vendor.Database;

@Configuration
@Profile({Profiles.DEV_ORACLE, Profiles.PROD_ORACLE})
public class Oracle10gDatabaseConfig extends AbstractSqlDatabaseConfig<DataSource> {

    @Autowired
    private EccoEnvironment env;

    @Override
    @Bean(destroyMethod = "close")
    public DataSource dataSource() {
        return createDataSource();
    }

    private DataSource createDataSource() {
        DataSource dataSource = getConnectionPool(env);
        dataSource.setDriverClassName(oracle.jdbc.OracleDriver.class.getCanonicalName());
        dataSource.setTestOnBorrow(true);
        dataSource.setValidationQuery("select 1 from dual");

        // old jdbc.url overrides new style to allow migration
        String jdbcUrl = env.getProperty("jdbc.url");
        if (jdbcUrl == null) {
            String dbPort = env.getDbPort() == null ? "1521" : env.getDbPort().toString();
            jdbcUrl = "jdbc:oracle:thin:@" + env.getDbHostName() + ":" + dbPort + ":xe";
            // Note: schema is set in HibernatePropertiesConfig
        }

        dataSource.setUrl(jdbcUrl);
        dataSource.setUsername(env.getProperty("jdbc.username"));
        dataSource.setPassword(env.getProperty("jdbc.password"));

        return dataSource;
    }

    @Override
    public Database database() {
        return Database.ORACLE;
    }
}
