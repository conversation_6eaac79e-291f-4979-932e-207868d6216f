package com.ecco.infrastructure.web;

import com.ecco.infrastructure.config.ApplicationProperties;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.Affordance;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.server.core.LinkBuilderSupport;
import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;


public class UriUtils {

    /** If in ecco-web-api instead use {@link com.ecco.webApi.support.ApiLinkTo#linkToApi} */
    public static Link prefixLinkPath(WebMvcLinkBuilder linkBuilder, String prefix) {
        return Link.of(linkBuilder.toUriComponentsBuilder().replacePath(prefix + linkBuilder.toUri().getPath()).toUriString());
    }

    public static class ExplicitLinkBuilder extends LinkBuilderSupport<ExplicitLinkBuilder> {

        public ExplicitLinkBuilder(UriComponentsBuilder builder, String prefixPath) {
            super(builder.replacePath(prefixPath + builder.build().getPath()).build());
        }

        public ExplicitLinkBuilder(WebMvcLinkBuilder webMvcLinkBuilder, String prefixPath) {
            this(webMvcLinkBuilder.toUriComponentsBuilder(), prefixPath);
        }

        public ExplicitLinkBuilder(UriComponents builder) {
            super(builder);
        }

        public ExplicitLinkBuilder(UriComponents builder, List<Affordance> affordances) {
            super(builder, affordances);
        }

        @Override
        @NonNull
        protected ExplicitLinkBuilder getThis() {
            return this;
        }

        @NonNull
        @Override
        protected ExplicitLinkBuilder createNewInstance(@NonNull UriComponents components, @NonNull List<Affordance> affordances) {
            return new ExplicitLinkBuilder(components, affordances);
        }
    }

    public static UriComponentsBuilder hostRelativeThenProvidedComponentBuilder(ApplicationProperties appConfig) {
        return requestExists() ? hostRelativeComponentBuilder() : hostProvidedComponentBuilder(appConfig);
    }

    public static ExplicitLinkBuilder hostRelativeThenProvidedLinkBuilder(ApplicationProperties appConfig) {
        return requestExists() ? hostRelativeLinkBuilder() : hostProvidedLinkBuilder(appConfig);
    }

    /**
     * Get a URI builder without host or scheme, but with context path
     * NB This picks up the currentContext from the request, which is why we don't supply
     * the fixed servletContext.getContextPath() from ApplicationPropertiesImpl
     */
    public static UriComponentsBuilder hostRelativeComponentBuilder() {
        return ServletUriComponentsBuilder.fromCurrentContextPath().scheme(null).host(null);
    }

    /**
     * Get a link builder without host or scheme, but with context path
     * NB This picks up the currentContext from the request, which is why we don't supply
     * the fixed servletContext.getContextPath() from ApplicationPropertiesImpl
     */
    public static ExplicitLinkBuilder hostRelativeLinkBuilder() {
            return new ExplicitLinkBuilder(
                    ServletUriComponentsBuilder.fromCurrentContextPath().host(null).scheme(null).build() );
    }

    /**
     * The @Scheduled activities do not have access to a request to get the currentContext, and therefore some
     * further work is required to have consistency. For now, we provide the url from the environment.
     */
    public static UriComponentsBuilder hostProvidedComponentBuilder(ApplicationProperties appConfig) {
        // see EmailBaseController#linkToParentFile
        //var contextNoSlash = UriUtils.stripSlahes(appConfig.getApplicationRootPath());
        return UriComponentsBuilder.fromPath(appConfig.getEccoWebsiteUrl());
    }

    /**
     * The @Scheduled activities do not have access to a request to get the currentContext, and therefore some
     * further work is required to have consistency. For now, we provide the url from the environment.
     */
    public static ExplicitLinkBuilder hostProvidedLinkBuilder(ApplicationProperties appConfig) {
        // see EmailBaseController#linkToParentFile
        //var contextNoSlash = UriUtils.stripSlahes(appConfig.getApplicationRootPath());
        final UriComponentsBuilder subdomain = UriComponentsBuilder.fromHttpUrl(appConfig.getEccoWebsiteUrl());
        return new UriUtils.ExplicitLinkBuilder(subdomain.build());
    }

    private static boolean requestExists() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        return requestAttributes instanceof ServletRequestAttributes;
    }

    public static String stripSlahes(String path) {
        return path == null ? null : path.replace("/", "");
    }

}
