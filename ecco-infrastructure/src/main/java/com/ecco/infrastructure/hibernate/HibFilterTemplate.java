package com.ecco.infrastructure.hibernate;

import org.hibernate.Session;
import org.hibernate.engine.spi.SessionImplementor;

import javax.persistence.EntityManager;
import java.util.Set;
import java.util.function.Supplier;

/**
 * Template giving the ability to execute code with default filters turned off.
 */
public class HibFilterTemplate {
    private final EntityManager entityManager;

    public HibFilterTemplate(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public <T> T executeUnfiltered(Supplier<T> action) {
        final Session session = (Session)entityManager;
        final SessionImplementor sessionImpl = entityManager.unwrap(SessionImplementor.class);
        final Set<String> enabledFilterNames = sessionImpl.getLoadQueryInfluencers().getEnabledFilterNames();
        for (String filterName : enabledFilterNames) {
            session.disableFilter(filterName);
        }

        try {
            return action.get();
        } finally {
            for (String filterName : enabledFilterNames) {
                session.enableFilter(filterName);
            }
        }
    }

}
