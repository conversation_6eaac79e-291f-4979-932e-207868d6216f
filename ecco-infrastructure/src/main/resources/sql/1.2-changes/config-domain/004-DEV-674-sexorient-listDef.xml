<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="classpath:sql/1.2-changes/config-domain/004-DEV-674-sexorient-listDef.xml">

<changeSet id="DEV-674-sexorient-listDef" author="adamjhamer">
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="124"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="unknown"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="125"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="heterosexual"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="126"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="homosexual"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="127"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="lesbian"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="128"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="gay"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="129"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="bisexual"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="130"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="not disclosed"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="131"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="sexualOrientation"/>
        <column name="name" value="no capacity to answer"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
</changeSet>

</databaseChangeLog>
