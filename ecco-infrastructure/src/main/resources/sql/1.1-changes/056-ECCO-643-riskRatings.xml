<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-643-likelihoods" author="adamjhamer">
        <createTable tableName="likelihoods">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="score" type="INT">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-643-severity" author="adamjhamer">
        <createTable tableName="severity">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="score" type="INT">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>

    <!-- remove incorrect definition if we applied it, as we want to add one with nullable=true -->
    <changeSet id="ECCO-643-supportthreatactions-remove" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/056-ECCO-643-riskRatings.xml"
                author="adamjhamer" id="ECCO-643-supportthreatactions"/>
        </preConditions>
        <dropColumn tableName="supportthreatactions" columnName="likelihood"/>
        <dropColumn tableName="supportthreatactions" columnName="severity"/>
    </changeSet>

    <changeSet id="ECCO-643-supportthreatactions-readd" author="adamjhamer">
        <addColumn tableName="supportthreatactions">
            <column name="likelihood" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="supportthreatactions">
            <column name="severity" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
