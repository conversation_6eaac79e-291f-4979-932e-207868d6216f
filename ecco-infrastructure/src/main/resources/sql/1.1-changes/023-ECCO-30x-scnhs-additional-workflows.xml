<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- Requires -Ddb.extraContexts=1.1-nhs-base-data for this to be used -->

    <changeSet id="ECCO-306-servicetypes" author="neale" context="1.1-nhs-base-data">
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="103"/>
            <column name="version" valueNumeric="0"/>
            <column name="accommodation" valueBoolean="false"/>
            <column name="anonymousSupport" valueBoolean="false"/>
            <column name="contractRequired" valueBoolean="false"/>
            <column name="logtime" valueBoolean="false"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="scnhs-fasttrack"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="103"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="scnhs (fasttrack)"/>
            <column name="servicetypeid" valueNumeric="103"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-306-enable-workflow" author="neale" context="1.1-nhs-base-data">
        <insert tableName="servicetypes_workflow">
            <column name="id" valueNumeric="103"/>
            <column name="version" valueNumeric="0"/>
            <column name="process_key" value="scnhs.fasttrack"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-306-st-referralaspects-wizard" author="neale" context="1.1-nhs-base-data">
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="45"/>
            <column name="servicetypeId" valueNumeric="103"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="15"/>
            <column name="servicetypeId" valueNumeric="103"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="18"/>
            <column name="servicetypeId" valueNumeric="103"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-306-st-questiongroups" author="neale" context="1.1-nhs-base-data">
        <update tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="103"/><!-- Fasttrack -->
            <where>questiongroupId=100138 and servicetypeId=100</where>
        </update>
    </changeSet>

</databaseChangeLog>
