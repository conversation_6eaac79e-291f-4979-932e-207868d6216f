<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- this actually doesn't work for oracle, we copied an existing addColumn and defaultValue -->
    <!-- but should have specified the defaultValue in the same addColumn command -->
    <!-- we assume a new baseline would be created by the time a new oracle instance comes along -->
    <!-- so we fudge it on oracle, but this serves as a notice in case we get stuck again! -->

    <changeSet id="self-funding" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="selfFunding" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="self-funding-default" author="adamjhamer">
        <addDefaultValue tableName="referrals" columnName="selfFunding" defaultValueBoolean="false"/>
    </changeSet>

</databaseChangeLog>
