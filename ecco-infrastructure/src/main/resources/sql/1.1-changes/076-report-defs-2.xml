<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- see 073 file for this definition -->
    <changeSet id="ECCO-2046-repDef-rename-0" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="summary: support work entry totals by service then KPIs"/>
            <where>uuid='10000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="ECCO-2046-repDef-KPI2-by-auth" author="nealeu">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="10010000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-07-14T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="totals by author then KPIs"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-2046-repDef-rename-1" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="summary: support work entry totals by author then KPIs"/>
            <where>uuid='10010000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2046-repDef-KPI2-body-3" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
                {
                "description": "client contact summary",
                "selectionCriteria": {
                "groupBy": "author",
                "selectionRootEntity": "GroupedWorkAnalysis",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "by author",
                "stageType": "CHART",
                "selectionAnalyser": "single",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "Time spent with clients in period",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comments",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "time spent"
                }
                },
                {
                "description": "average time per visit",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comment",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "average time spent"
                }
                },
                {
                "description": "latest work",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-calendar",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "latest work"
                }
                }
                ]
                }
            </column>
            <where>uuid='10010000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="ECCO-2046-repDef-KPI3-by-auth" author="nealeu">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="10020000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-07-14T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="work summary by service table breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-2046-repDef-rename2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="summary: support work by service, table breakdown"/>
            <where>uuid='10020000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2046-repDef-KPI3-body-3" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
            {
                "description": "summary by service",
                "selectionCriteria": {
                    "groupBy": "serviceRecipient.service",
                    "selectionRootEntity": "GroupedWorkAnalysis",
                    "selectorType": "byStartOfQuarter",
                    "selectionPropertyPath": "workDate",
                    "relativeStartIndex": 0,
                    "relativeEndIndex": 1,
                    "fetchRelatedEntities": []
                },
                "stages": [
                    {
                        "description": "",
                        "stageType": "TABLE",
                        "tableRepresentation": {
                            "className": "VisitsAnalysis",
                            "columns": [
                                {"title": "service", "representation": "key"},
                                "time spent",
                                "average time spent",
                                "latest work",
                                "count",
                                "unsigned work count"
                            ]
                        }
                    }
                ]
            }
            </column>
            <where>uuid='10020000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="ECCO-2046-repDef-KPI4-by-auth" author="nealeu">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="10030000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-07-14T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="work summary by author table breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-2046-repDef-rename3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="summary: support work by author, table breakdown"/>
            <where>uuid='10030000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2046-repDef-KPI4-body-3" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
            {
                "description": "summary by author",
                "selectionCriteria": {
                    "groupBy": "author",
                    "selectionRootEntity": "GroupedWorkAnalysis",
                    "selectorType": "byStartOfQuarter",
                    "selectionPropertyPath": "workDate",
                    "relativeStartIndex": 0,
                    "relativeEndIndex": 1,
                    "fetchRelatedEntities": []
                },
                "stages": [
                    {
                        "description": "",
                        "stageType": "TABLE",
                        "tableRepresentation": {
                            "className": "VisitsAnalysis",
                            "columns": [
                                {"title": "author", "representation": "key"},
                                "time spent",
                                "average time spent",
                                "latest work",
                                "count",
                                "unsigned work count"
                            ]
                        }
                    }
                ]
            }
            </column>
            <where>uuid='10030000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2046-repDef-KPI5-by-receivedDate" author="nealeu">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="10500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-07-25T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="referrals by month table breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-2046-repDef-rename4" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="summary: referrals by month, table breakdown"/>
            <where>uuid='10500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2046-repDef-KPI5-body-2" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
                {
                "description": "received by month",
                "selectionCriteria": {
                "groupBy": "receivedDate",
                "selectionRootEntity": "ReferralsByMonth",
                "selectorType": "byStartOfMonth",
                "selectionPropertyPath": "receivedDate",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ByMonthAnalysis",
                "columns": [
                {"title": "received month", "representation": "month"},
                "service",
                "count"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='10500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-621-repDef-audits-create" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2018-06-20T09:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="30"/>
            <column name="name" value="audits / activity stream"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <!-- update the body with escape xml -->
    <changeSet id="DEV-798-repDef-audits-body-daily" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on all commands&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;ServiceRecipientCommand&quot;,
                &quot;selectorType&quot;: &quot;byStartOfDay&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;selectionPropertyPath&quot;: &quot;&quot;,
                &quot;referralStatus&quot;: &quot;&quot;,
                &quot;selectionKey&quot;: &quot;&quot;,
                &quot;fetchRelatedEntities&quot;: []
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;serviceRecipientCommandCountsByCommandName&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by name&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;value&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of commands&quot;,
                &quot;stageType&quot;: &quot;AUDIT&quot;
                }
                ]
                }
            </column>
            <where>uuid='04500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-621-repDef-riskFlagsLive-create" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04600000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2018-06-20T09:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="30"/>
            <column name="name" value="risk flags live"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <!-- update the body with escape xml -->
    <changeSet id="DEV-621-repDef-riskFlagsLive-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;all risk flags&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;RiskFlags&quot;,
                &quot;entityStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;selectionPropertyPath&quot;: &quot;startDate&quot;,
                &quot;fetchRelatedEntities&quot;: [&quot;referral&quot;]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;riskFlagsCountsByValue&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by value&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of risk flags&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;RiskFlagsWithReferralSummary&quot;,
                &quot;columns&quot;: [&quot;r: r-id&quot;, &quot;r: c-id&quot;, &quot;r: name&quot;, &quot;r: service&quot;, &quot;r: project&quot;, &quot;r: received&quot;, &quot;r: worker&quot;, &quot;f-id&quot;, &quot;work date&quot;, &quot;name&quot;, &quot;value&quot;]
                }
                }
                ]
                }
            </column>
            <where>uuid='04600000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-621-repDef-reviewSnapshot-create" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2018-06-20T09:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="30"/>
            <column name="name" value="reviews (snapshot)"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <!-- update the body with escape xml -->
    <changeSet id="DEV-621-repDef-reviewsSnapshot-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;all reviews&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Review&quot;,
                &quot;entityStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;selectionPropertyPath&quot;: &quot;startDate&quot;,
                &quot;fetchRelatedEntities&quot;: [
                &quot;referral&quot;
                ]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;reviewCountsByComplete&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by completed&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of reviews&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReviewWithReferralSummary&quot;,
                &quot;columns&quot;: [
                &quot;r: r-id&quot;,
                &quot;r: c-id&quot;,
                &quot;r: name&quot;,
                &quot;r: service&quot;,
                &quot;r: project&quot;,
                &quot;r: received&quot;,
                &quot;r: worker&quot;,
                &quot;r: status now&quot;,
                &quot;rev-id&quot;,
                &quot;review date&quot;,
                &quot;% complete&quot;,
                &quot;complete&quot;
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='04700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-899-repDef-projectcalendar-create" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2018-11-22T23:47:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="name" value="calendar"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <!-- update the body with escape xml -->
    <changeSet id="DEV-899-repDef-projectcalendar-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {}
            </column>
            <where>uuid='04800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- TAKE NOTE: ARE YOU SURE it goes here, and not in the configDomain or securityDomainChangeLog -->
</databaseChangeLog>
