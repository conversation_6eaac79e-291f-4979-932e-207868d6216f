<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
                   logicalFilePath="classpath:sql/1.1-changes/003-addClientAndWorkflowModules.xml">

    <changeSet author="default" id="ECCO-133-ClientPerspective">

        <insert tableName="softwaremodule">
            <column name="name" value="client-centric"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="softwaremodule">
            <column name="name" value="workflow"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>

        <insert tableName="menuitem">
            <column name="id" valueNumeric="27"/>
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png"/>
            <column name="linkText" value="menu.linktext.client_perspective"/>
            <column name="roles" value="ROLE_USER"/>
            <column name="url" value="/dynamic/secure/menuClientPerspective.html"/>
            <column name="module_name" value="client-centric"/>
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="28"/>
            <column name="imageUrl" value="/icons/crystal/junk-with-tick-48.png"/>
            <column name="linkText" value="menu.linktext.allocation_basket"/>
            <column name="roles" value="ROLE_USER"/>
            <column name="url" value="/dynamic/secure/allocationBasket.html"/>
            <column name="module_name" value="workflow"/>
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="29"/>
            <column name="imageUrl" value="/icons/crystal/junk-with-stop2-48.png"/>
            <column name="linkText" value="menu.linktext.duty_basket"/>
            <column name="roles" value="ROLE_USER"/>
            <column name="url" value="/dynamic/secure/dutyBasket.html"/>
            <column name="module_name" value="workflow"/>
        </insert>

        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="27"/>
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="28"/>
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="29"/>
        </insert>
    </changeSet>

</databaseChangeLog>
