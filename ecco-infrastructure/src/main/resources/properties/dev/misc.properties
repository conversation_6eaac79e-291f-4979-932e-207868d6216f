misc.showErrors=true
misc.analytics.webPropertyId=
misc.upload.tolocalhost=true
misc.dwr.debug=true
# 15728640 is 15(mb) = 15*1024*1024(b), much more than 1gb is not good over http post (no resumes)
# NB - double this and set the max_allowed_packet setting under mysqld in /etc/my.cnf (comment the default, search 'packet')
# so max_allowed_packet is set for 32*1024*1024 = 33554432
# 4194304 is 4(mb) = 4*1024*1024(b)
# 4096 is 4(kb) 4*1024(b)
misc.upload.maxSize=15728640
misc.upload.tempDir=file:/tmp/ecco/uploads

misc.labels.debug=true

misc.sslchannel=any
