

# For development, create, create-drop, validate, update are all valid, but validate is probably best option if manually managing the schema
# however, if validating, set spring.schemaValidate to true as it has been configured to print the neccessary sql
# when doing so, set hbm2dll to 'none' to avoid it running first - undocumented, http://stackoverflow.com/questions/3179765/how-to-turn-off-hbm2ddl
hibernate.hbm2ddl=validate

# Use org.hibernate.SQL logger via Log4j.xml instead
# However, HibernatePropertiesConfig does use this (at least for int. tests)
jdbc.showSql=false
jdbc.showSqlComments=true

jdbc.username=ecco
jdbc.password=ecco

# These are ready for Spring Boot config
spring.datasource.tomcat.max-active=8

# Some settings to attempt to diagnose anything that didn't get cleaned up in the application
# See https://tomcat.apache.org/tomcat-8.5-doc/jdbc-pool.html

# NB For long running liquibase statements, we need to disable abandoned
# -Dspring.datasource.tomcat.remove-abandoned=false

spring.datasource.tomcat.log-abandoned=true
spring.datasource.tomcat.remove-abandoned=true
spring.datasource.tomcat.remove-abandoned-timeout=900
spring.datasource.tomcat.suspect-timeout=615
# Abandon connections after an hour rather than reusing them
spring.datasource.tomcat.max-age=3600000