BPMN Workflow in ecco
=====================

Referral aspects in classic ecco define an aspect of a referral with an associated user interface for editing this aspect.
These aspects are then associated with a service type, effectively providing a linear flow through which is user is
guided to edit the referral.

This concept can be replaced using a workflow engine. In this case, Activiti workflow engine. The process is defined
as a BPMN 2.0 XML file and can be imported, edited and exported with the editor bundled with Activiti Explorer. Activiti
Explorer can also be configured to connect directly to the ecco database to inspect and edit workflow definitions and
instances.

BPMN 2.0 allows much more complex workflows to be defined, including conditional branches, explicit completion and ownership of tasks,
and using a workflow engine provides query APIs to support consistent views onto the current workfow definitions and instances.
Activiti does not support the BPMN 2.0 implementationUri property, so we use the Activiti-specific form key and form
properties to build an implementation URI to pass through the API to define a user task form implementation in terms of
either 'flow' (Spring WebFlow) or 'generic' (GenericTypeController) views.

BPMN is also a good tool for communicating with clients in terms they will understand in order to get a correct and
precisely defined business process from the outset. Business Analysts can also work with it and hand the results directly
over for development without the usual communication gap.

There is a reasonable BPMN 2.0 reference in the Activiti user guide. We currently use Parallel Gateways, Exclusive
Gateways, Service Tasks and User Tasks.

The Workflow API in use by ecco is defined by `com.ecco.workflow.WorkflowService` in the ecco-workflow module. The Activiti
implementation of that API is defined in the ecco-workflow-activiti module. We are slightly subverting the normal BPMN 2.0
model by allowing old, completed, user tasks to be revisited and re-edited. This is to mirror the current referral aspect
behaviour althought it would be cleaner to provide defined 'reset points' and mechanisms for looping the workflow back
to an earlier step whilst only ever permitting current tasks to be performed.

Where to start looking through the code
---------------------------------------
- `WorkflowConfig` - integration
- `WorkflowService` - API, all interface-based
- `ActivitiIntegrationTest` - exercises some of Activiti's underlying API, use to understand under-the-hood a bit
- `TaskController` - the task list
- `GenericTypeSupport` - code supporting the generic views (needs/risk assessment etc)
- `referralFlow.xml` - branches off for Workflow-based referrals
- `referralAspectFlow.xml` - support for directly accessing subflows using Workflow-based referrals
- `referralWorkflowView.jsp` - the workflow version of the referral overview


Areas requiring further testing/work
------------------------------------
- Spider graphs
- Duty basket (to make it non-NHS specific, an 'all tasks' view with group filter?)
- Branching/conditional workflow
- Use of timer or signal intermediate events to suspend and resume workflow processes
- Allocations basket (view of referrals with NO active tasks ('dormant'?) - also requires changing the NHS workflow to
  introduce an  intermediate event after schedule reviews so that there are no active tasks until a review date is hit).
  In general we need to look at better modelling processes to reflect reality rather than the current models which are
  somewhat shoe-horned into the do-anything-anytime aspect model.
- Navigation from generic types back to the referral overview
- `Save Draft` and `Save & Progress` for generic type views on non-Activiti workflows
- Global settings (should be process variables)
- Hardcoded references to particular aspects
- All aspect views should manage their own state
- All aspect summaries should manage their own content and visibility
- Referral aspects still required for initial referral creation steps
- What happens when revisiting old workflow steps and changing variables which affect later branches?
- Anything which says `TODO: ASPECT LEGACY`
- Contribute `GetTaskFormCmd` back to Alfresco for Activiti
- Find a way to identify live execution paths to support a standard way for disabling availability of historic tasks (`HistoricTaskWorkflowTaskAdapter`)
- Availability of historic/active tasks based on logged in user (apart from on My Tasks) - `WorkflowTaskAdapterFactory`

Migration overview
------------------
Activiti stores process state in its own database tables which are created/updated at startup. Migrating exisiting
in-flight referrals to Activiti will be difficult as you would not only need to create the process instance but also
get it into the right state to reflect the current state in the referral aspect model.

The recommended approach is to define new service types as workflow-based service types. If these are wholly new services
then this is fine; for existing clients the better approach is to create a new service type mirroring the existing service
type and disable the old one for new referrals.

It would be nice to move the service from the old service type to the new service type, since the workflow is defined
(as referral aspects are) against a service type rather than a service. This would allow reporting to be consistent
across old and new processes. This would require the decision as to whether to adopt an aspect-based or workflow-driven
user interface to be based on whether there is a workflow instance associated with the referral rather than as (at
present) whether the service type is workflow-driven or not.

We should create workflow-driven versions of the demo services and use them in future in place of the existing versions -
this will also help to flush out any defects in the workflow UI and service.

Resources:
==========

- Activiti - Getting Started http://activiti.org/userguide/index.html#demo.setup.one.minute.version
