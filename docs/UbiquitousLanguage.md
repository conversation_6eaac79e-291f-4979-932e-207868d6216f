This document defines domain concepts as per Domain Driven Design practice.  Where possible we should use this
language within our code base, only deviating to the language variations of specific organisations where necessary.


ECCO
----
*Evidenced Client-Centred Outcomes*


Support Worker
--------------
The person responsible for ensuring that appropriate Services are provided to the Service User

Service User
------------
aka 'client'.  The person receiving the services provided by the Service Provider, paid for by the Commissioner


Service Type
------------
Common configuration for Services which have the same referral aspects


Service
-------
e.g. Youth project


Projects
--------
Something that can provide one or more Service Types (usually a venue/building)


Referral
--------


Referral Aspects
----------------


Needs Assessment
----------------


Outcome
-------


SMART Steps
-----------


Review
------


Funding Stream
--------------


Activities (Group Support menu)
----------


Evidence History (Overview link when entering new items - e.g. when in Group Support)
----------------

