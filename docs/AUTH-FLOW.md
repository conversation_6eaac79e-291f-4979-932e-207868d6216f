Authentication flow in ECCO
===========================

This relates to the flow of login pages, failures, successes and logout
around online authentication with respect to website-based and app-based logins,
including oauth azure. It refers to what is live now, which is not spring-boot.
NB https://github.com/spring-projects/spring-security/wiki/OAuth-2.0-Features-Matrix

There are other places documenting the kerberos/ad approach, and offline
auth flow - although there may be some benefit in bringing it in here.


LOGIN PAGE flow
---------------
    classes:
        TODO document entry points flow by classes - eg via spring-boot and not
        see getPublicGuidanceCustomForm commit

    website:
        page:
            handled by SecurityComponentConfig (and spring-boot WebSecurityConfigurer) (was -security.xml defaultHttpSecurity's loginEntryPoint, /nav/secure/login.html)
            login.html renders content/login.jsp
            and includes kerberos (KerberosConfig / spnegoAuthenticationProcessingFilter), active directory on-premise (ActiveDirectoryConfig)
            and active directory cloud (aadHandleConditionalAccessFilter)
        api:
            handled by WebApiEndpointSecurityConfigurer (was -security.xml webApiHttpSecurity's and http-basic's entry-point-ref), WebApiAuthenticationEntryPoint
            which returns a 403 for the code to present a page itself

    app:
        page:
            handled by WebAppSecurityConfigurer (inactive) and WebSecurityConfigurer
            doesn't include kerberos (KerberosSecurityConfigurer)
            doesn't include active directory on-premise (ActiveDirectorySecurityConfigurer)
        api:
            handled by WebApiSecurityConfigurer


FAILURE flow
------------
    website user+pwd:
        handled by -security.xml defaultHttpSecurity's authenticationProcessingFilter in SecurityComponentConfig, configured with authenticationFailureHandler
        which redirects to a page for content/login.jsp to find the error in the server session
        the default is to go to login page with ?error on the end (AbstractAuthenticationFilterConfigurerer)
    website oauth:
        handled by -security.xml defaultHttpSecurity's oauth2-login, authenticationFailureHandler
        which redirects to a page for content/login.jsp to find the error in the server session

    app user+pwd:
        handled by SecurityComponentConfig UsernamePasswordAuthenticationFilter's authenticationFailureHandler
        which has been modified to understand which url to redirect back to
        however the web-api currently traps the response and presents a toast to try again
    app oauth:
        handled by -security.xml defaultHttpSecurity's oauth2-login, authenticationFailureHandler
        the default failure url is a generated page from login url with ?error on the end (see OAuth2LoginConfigurer and this.failureUrl(this.loginPage + "?error");)
        so we avoid the generated page with our own authenticationFailureHandler
        which has been modified to understand which url to redirect back to
            we don't have access to the session error message
            we could get our message by supplying a json response - as per https://stackoverflow.com/questions/67457578/oauth2authenticationentrypoint-is-deprecated
            but it seems the wiring for oAuth AuthenticationEntryPoint refers to the resource server, which isn't us
        so we follow the spring.io guide which uses a get request to get the session error: https://spring.io/guides/tutorials/spring-boot-oauth2/#_social_login_custom_error

SUCCESS flow
------------
    website:
        handled by authenticationSuccessHandler which does a redirect
        this uses the saved request, so the page goes to the original requested
        EccoAuthenticationSuccessHandler also checks the expiry of the password
        the login page first posts to /api/keys/userDevices/valid/{existingKey}
        which requires auth from applicationContext-security.xml /api/keys/** for ROLE_USER
        then it posts to nav/secure/j_acegi_security_check a form
    app:
        handled by the default, since one isn't specified in the oauth2-login config (or spring boot options)
        a default oauth2-login is SavedRequestAwareAuthenticationSuccessHandler (follow oAuth2 in HttpSecurity)
        and our custom request-cache is now hooked up to save the right url (ie not the api request)
        so the app goes to the page, but misses out on some EccoAuthenticationSuccessHandler methods - which is a good thing for now

LOGOUT flow
-----------
    website:
        handled by LogoutFilter which redirects to logoutSuccess.html
        however, the UserMenu after a logout.html reloads the page (to cope for the app or website) - killing the logoutSuccess.html

    app:
        handled by LogoutFilter which redirects to logoutSuccess.html
        however, the UserMenu after a logout.html reloads the page (to cope for the app or website) - killing the logoutSuccess.html
        a default oauth logout url would be the login url with ?logout on the end (see logoutSuccessUrl(this.loginPage + "?logout")


COOKIE SECURITY
===============
For Secure / SameSite / HttpOnly.
See also context.xml and CookieConfigServletContextListener.

Ideally, we want set-cookie to SameSite=Strict;Secure;HttpOnly.
This avoids some csrf issues, which is why we can do .csrf().disable().

    'SameSite=
        'None' is required for 3rd party session creation (eg Azure AD).
        'Strict' ensures cookies aren't sent to other sites
        'Secure' ensures cookies are set over https (avoiding MITM attacks on http-snooping)
        'HttpOnly' ensures cookies aren't read by other browser tabs.

The awkward bit is that SameSite= 'None' requires 'Secure' - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite
However, we can have different settings for different environments depending on whether 3rd party session creation is required (eg Azure AD):

      For localhost:
            with 3rd party: -Dcookie.samesite=none (applied in context.xml)
                then the default, -Dcookie.insecure=false, can be used since although 'Secure' turns on, it is ignored for localhost, (at least in Chrome)
                see https://www.chromium.org/updates/same-site/test-debug
                see https://docs.microsoft.com/en-us/azure/active-directory/develop/howto-handle-samesite-cookie-changes-chrome-browser?tabs=dotnet
            without 3rd party: -Dcookie.samesite=strict (default in context.xml)
                then we can allow http: -Dcookie.insecure=true
      For hosting:
            normal: use defaults, don't specify
            intranet: -Dcookie.insecure=true (to allow http)
                This is only acceptable if https and http use different domains,
                because 'Secure' is designed to prevent a MITM attack in this scenario.
                It helps if SameSite=Strict to keep cookies to the same site,
                and HttpOnly to prevent java-script access.
                If 3rd party, then 'Secure' is required due to samesite=none.
      Default:
            -Dcookie.insecure=false -Dcookie.samesite=strict - so SameSite=Strict;Secure
