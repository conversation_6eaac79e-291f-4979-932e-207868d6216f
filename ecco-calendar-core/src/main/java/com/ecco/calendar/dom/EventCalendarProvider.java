package com.ecco.calendar.dom;

/**
 * Entities which do not nicely fit implementing {@link EventEntryDefinition} directly can instead implement this and use
 * e.g. an adapter class.
 *
 * @since 18/06/15
 */
public interface EventCalendarProvider {
    /**
     * Get this event as an entry for storing to the full calendar
     *
     * @return an entry for storing to the full calendar system (e.g. Cosmo)
     */
    @SuppressWarnings("unused")
    EventEntryDefinition toEventEntry();
}
