package com.ecco.calendar.core;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.DtoBuilderProxy;
import com.ecco.dto.ProxyDtoBuilderProxy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents an attendee of a calendar entry from the calendaring system.
 *
 * @since 26/08/2013
 */
public interface Attendee extends BuildableDto<Attendee> {
    // Something along the lines of ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=TENTATIVE;CN=Test User:MAILTO:<EMAIL>

    /**
     * Unique reference to the native object of the user (calendarId) of the attendee.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter#getDirEntryRef}
     */
    String getCalendarIdUserReferenceUri();

    String getCalendarId();

    /** The name of the attendee for display purposes. */
    String getName();

    /** The email address of the attendee. */
    String getEmail();

    /** Is this attendee required for the meeting? */
    boolean isRequired();

    enum Status {
        TENTATIVE,
        ACCEPTED,
        DECLINED
    }

    /** The participation status of the attendee. May be null to indicate an unknown status. */
    Status getStatus();

    interface Builder extends DtoBuilder<Attendee> {
        Builder calendarIdUserReferenceUri(String uri);
        Builder calendarId(String calendarId);
        Builder name(String name);
        Builder email(String email);
        Builder required(boolean required);
        Builder status(Status status);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, Attendee.class);
        }

        public static Builder create(Attendee template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }
    }

    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class AttendeeProxyImpl implements Attendee {

        String calendarIdUserReferenceUri;
        String calendarId;
        String name;
        String email;
        boolean required;
        Status status;

    }
}
