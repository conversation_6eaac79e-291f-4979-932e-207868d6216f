package com.ecco.calendar.core;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.DtoBuilderProxy;
import com.ecco.dto.ProxyDtoBuilderProxy;

import java.io.Serializable;
import java.net.URI;

/**
 * Represents the location of a calendar entry.
 *
 * @since 11/03/2014
 */
public interface LocationDefinition  extends Serializable, BuildableDto<LocationDefinition> {
    /** The value of the location for display purposes. */
    String getValue();

    /** URI of directory entry which holds more details about the location. */
    URI getManagedByUri();

    interface Builder extends DtoBuilder<LocationDefinition> {
        Builder value(String value);
        Builder managedByUri(URI managedByUri);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, LocationDefinition.class);
        }

        public static Builder create(LocationDefinition template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }
    }
}
