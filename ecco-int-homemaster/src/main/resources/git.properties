#Wil be overwritten by Git-Commit-Id-Plugin
#Wed Dec 11 22:57:15 GMT 2013
#This version is served directly when running using Eclipse WTP (and tomcat:run perhaps)
git.commit.id.abbrev=1234567
git.commit.user.email=<EMAIL>
git.commit.message.full=ECCO-nnn Implement mega-feature in one commit
git.commit.id=332b1761e2680dc1ae32dd122123123213213
git.commit.message.short=ECCO-nnn Implement mega-feature in one commit
git.commit.user.name=<PERSON><PERSON> Upstone
git.build.user.name=<PERSON><PERSON> Upstone
git.commit.id.describe=20000101-main-g1234567-dirty
git.build.user.email=<EMAIL>
git.branch=main
git.commit.time=01.01.2000 @ 00\:00\:00 GMT
git.build.time=01.01.2000 @ 00\:00\:00 GMT
