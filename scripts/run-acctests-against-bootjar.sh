#!/usr/bin/env bash

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $DIR/..
PARAMS="$*"
echo "=== API Acceptance Tests : params: $PARAMS ==="

JAVA_ARGS="-Ddb.schema=acctest
    -Denv=dev \
    -Ddb.extraContexts=acceptanceTests -Duser.timezone=UTC \
    -Dcookie.insecure=true -Dcookie.samesite=strict \
    -Dazure.activedirectory.client-id=asdf -Dazure.activedirectory.client-secret=asdf \
    --add-opens=java.base/java.util=ALL-UNNAMED \
    --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
    --add-opens=java.base/java.lang=ALL-UNNAMED \
    --add-opens=java.base/java.text=ALL-UNNAMED \
    --add-opens=java.desktop/java.awt.font=ALL-UNNAMED"

# NOPE ./gradlew --foreground :ecco-webapi-boot:bootRun &
# NOPE because stuff breaks in fat jar: java $JAVA_ARGS -jar ./ecco-webapi-boot/build/libs/ecco-webapi-boot.CI-1.0.0-SNAPSHOT.jar &

# use classpath and main
rm -rf ./ecco-webapi-boot/build/libs/exploded/
unzip -q ./ecco-webapi-boot/build/libs/ecco-webapi-boot-1.0.0.CI-SNAPSHOT.jar -d ./ecco-webapi-boot/build/libs/exploded/
cd ./ecco-webapi-boot/build/libs/exploded/
echo "  === STARTING SERVER ==="
java $JAVA_ARGS -cp . org.springframework.boot.loader.JarLauncher &

#TODO needs opens java.util" etc.

# call wget until response is 200 (could also check contains "healthStatus":"green")
for i in {1..30}
do
  sleep 3
  echo "  >>> Checking if server has started..."
  wget -q -O - http://localhost:8899/api/stats/ && break
  if [ $i == 30 ]; then
    exit 1;
  fi
done
echo "\n\n=== Server started! Running tests with additional params: $PARAMS ===\n\n"
cd $DIR/..
./gradlew :ecco-acceptance-tests:checkFrontend :ecco-acceptance-tests:test -Ptest.api -Ptest.ui $PARAMS
RESULT="$?"

echo "   === Stopping server ==="
kill %1

exit $RESULT